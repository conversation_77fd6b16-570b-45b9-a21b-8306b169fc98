import { InjectionToken } from '@angular/core';
import { devServer, environment as env, isG<PERSON><PERSON><PERSON><PERSON>, partnerId } from '../globals';
import { Capacitor } from '@capacitor/core';
import { IAuthConfig } from 'ionic-appauth';

function domain(): string {
  if (!Capacitor.isNativePlatform()) return '';

  if (env != 'prod') {
    throw new Error(`environment not supported: ${env}`);
  }

  if (isGreyLabel) {
    return 'business-center-prod.apigateway.co';
  }

  switch (partnerId) {
    case 'ABC': {
      return 'login.custom-abc.com';
    }
    case 'NBLY': {
      return 'neighborly.broadly.com';
    }
    case 'PRBS': {
      return 'home.broadly.com';
    }
    case 'SICA': {
      return 'login.sicamarketing.com';
    }
    default: {
      console.warn(`Unknown partnerId: ${partnerId}`);
    }
  }
}

function clientId(): string {
  if (!Capacitor.isNativePlatform()) return '';

  if (env != 'prod') {
    throw new Error(`environment not supported: ${env}`);
  }

  if (isG<PERSON><PERSON><PERSON><PERSON>) {
    return 'bbad12f1-687b-4e6b-8b85-34452978b9c2';
  }

  switch (partnerId) {
    case 'ABC': {
      return 'ccb3ae08-fab1-4897-af06-9be02d4c8e17';
    }
    case 'PRBS':
    case 'NBLY': {
      return 'b4cbb7e8-0a47-4a4a-8ed0-e9b80d122fe5';
    }
    case 'SICA': {
      return '2020e5f2-09f6-4f99-9635-8d7c50dbd360';
    }
    default: {
      console.warn(`Unknown partnerId: ${partnerId}`);
    }
  }
}

// WARNING: you must set this per app in Xcode
// refer to https://developer.apple.com/documentation/xcode/defining-a-custom-url-scheme-for-your-app#Register-your-URL-scheme
function iOSNativeAppCustomUrlScheme(): string {
  if (!Capacitor.isNativePlatform()) return '';

  if (env != 'prod') {
    throw new Error(`environment not supported: ${env}`);
  }

  if (isGreyLabel) {
    // TODO (WARP-1542): use correct custom scheme for grey label
    return 'com.businessapp.abc';
  }

  switch (partnerId) {
    case 'ABC': {
      return 'com.businessapp.abc';
    }
    // since PRBS and NBLY use the same native app they need to use the same custom scheme for iOS
    // see redirect_uris in: https://vstore-prod.vendasta-internal.com/namespace/single-sign-on/table/Config/entity/b4cbb7e8-0a47-4a4a-8ed0-e9b80d122fe5
    case 'PRBS':
    case 'NBLY':
      return 'com.broadly.neighborly';
    case 'SICA': {
      return 'com.sicamarketing';
    }
    default: {
      console.warn(`Unknown partnerId: ${partnerId}`);
    }
  }
}

interface HostOptions {
  useCustomScheme?: boolean;
}

function host(options?: HostOptions): string {
  if (devServer && !Capacitor.isNativePlatform()) return 'http://localhost:4200';

  const scheme = !options?.useCustomScheme ? 'https' : iOSNativeAppCustomUrlScheme();
  return `${scheme}://${domain()}`;
}

function redirectUrl(): string {
  const opts = {
    useCustomScheme: Capacitor.getPlatform() === 'ios',
  };
  return `${host(opts)}/oauth/redirect`;
}

// WARNING if adding scopes you must update the business app native apps clients' service providers' preapproved scopes
// for full list of clients|service providers see: https://github.com/vendasta/sso/blob/9bebc0cc77872155391116dcb3f24b1ac9299f16/microservice.yaml#L106-L121
// sample service provider: https://vstore-prod.vendasta-internal.com/namespace/single-sign-on/table/ServiceProvider/entity/com.businessapp.abc
// sample request to update service provider:
/*
curl --location --globoff 'https://sso-api-{{env}}.vendasta-internal.com/sso.v1.ServiceProviderAdmin/UpdateServiceProvider' \
--header 'Content-Type: application/json' \
--header 'Authorization: ••••••' \
--data '{
  "serviceProviderId": "com.businessapp.abc",
  "mutations": [
    {
      "preApprovedScopes": {
        "repeated_string": [
          "offline_access",
          "openid",
          "profile",
          "business",
          "admin",
          "business-app",
          "crm",
          "user:read",
          "user",
          "user.password",
          "user.business"
        ]
      }
    }
  ]
}'
 */
const config: IAuthConfig = {
  client_id: clientId(),
  server_host: `https://iam-${env}.apigateway.co`,
  redirect_url: redirectUrl(),
  scopes: 'openid profile offline_access business admin business-app crm user:read user user.password user.business', // TODO (WARP-687): Need to remove admin as it is for partner users
  pkce: true,
  end_session_redirect_url: undefined,
};

export default new InjectionToken('auth.config', {
  providedIn: 'root',
  factory: () => config,
});
