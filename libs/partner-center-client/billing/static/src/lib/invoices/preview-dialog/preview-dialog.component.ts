import { Component, inject, Inject, NgModule } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { Invoice } from '@galaxy/billing';
import { InvoiceViewModel, SMBInvoicingModule } from '@vendasta/smb-invoicing';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ScriptLoaderService } from '../../core/stripe.service';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN } from '../../core';
import { TranslateModule } from '@ngx-translate/core';
import { AsyncPipe, NgIf } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';

export interface PreviewInvoiceDialogData {
  stripeKey: string;
  invoice: Invoice;
  hasVendastaPayments: boolean;
  companyName: string;
  stripeAccountId: string;
  confirmButtonText?: string;
}

@Component({
  templateUrl: './preview-dialog.component.html',
  styleUrls: ['./preview-dialog.component.scss'],
  standalone: false,
})
export class PreviewDialogComponent {
  stripeService = inject(ScriptLoaderService);
  merchantService = inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN).merchantService;
  merchantId$ = inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN).merchantId$;
  viewModel$: Observable<InvoiceViewModel>;

  constructor(
    public dialogRef: MatDialogRef<PreviewInvoiceDialogData>,
    @Inject(MAT_DIALOG_DATA) public data: PreviewInvoiceDialogData,
  ) {
    this.viewModel$ = combineLatest([this.merchantService.getBranding(), this.stripeService.load()]).pipe(
      map(([branding, _]) => {
        return {
          acceptPayment: this.data?.hasVendastaPayments,
          accountId: this.data?.stripeAccountId,
          partnerName: branding.name,
          partnerLogo: branding.logoUrl,
          primaryColor: branding.primaryColor ? '#' + branding.primaryColor : '',
          companyName: this.data?.companyName,
          invoice: this.data?.invoice,
          isPreview: true,
          isInvoiceInCents: true,
        } as InvoiceViewModel;
      }),
    );
  }

  confirm(): void {
    this.dialogRef.close(true);
  }
}

/**
 * This is a SCAM (single-component-angular-module) because we need to import the translations from the SMBInvoicingModule.
 * If you can get it to work with the preview dialog as a standalone component that would be great.
 */
@NgModule({
  declarations: [PreviewDialogComponent],
  imports: [MatButtonModule, MatDialogModule, SMBInvoicingModule, TranslateModule, AsyncPipe, NgIf, SMBInvoicingModule],
})
export class PreviewDialogModule {}
