@use 'design-tokens' as *;
@use '../core/breaks.scss' as breaks;
@use '../colors.scss' as colors;

@keyframes building-grow {
  0% {
    width: 126px;
    height: 106px;
  }
  100% {
    width: 186px;
    height: 166px;
  }
}
@keyframes cloud-1-anim {
  100% {
    height: 140px;
    top: -200px;
    right: -30px;
  }
}
@keyframes cloud-2-anim {
  100% {
    height: 140px;
    top: 32.5px;
    left: -300px;
  }
}
@keyframes cloud-3-anim {
  100% {
    height: 140px;
    bottom: -200px;
    right: -30px;
  }
}
@keyframes text-anim {
  100% {
    opacity: 1;
  }
}
@keyframes connection-in {
  100% {
    margin-left: 0px;
    opacity: 1;
  }
}

.welcome-banner {
  display: flex;
  flex-flow: row;
  align-items: center;
  overflow: hidden;
  position: relative;
  margin-bottom: 16px;
  border-radius: 0;

  .building {
    width: 76px;
    height: 76px;
  }
  .text {
    padding: 16px;
    flex: 1;
    opacity: 1;
    vertical-align: center;

    .welcome {
      font-weight: 500;
      font-size: 18px;
    }
    a {
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.welcome-links {
  display: flex;
  flex-direction: row;
  @include breaks.respond-to(mobile) {
    display: block;
  }
  .welcome-connections {
    display: inline-block;
    margin-right: 8px;
    @include breaks.respond-to(mobile) {
      padding: 6px 0 0 0;
    }
  }
  .video-box {
    display: inline-block;
    @include breaks.respond-to(mobile) {
      padding: 6px 0 0 0;
    }
  }
  .video {
    text-align: center;
  }
  .video-icon {
    font-size: 16px;
    vertical-align: text-bottom;
  }
  .video-boarder {
    display: inline-block;
    height: 12px;
    border-left: solid 2px $light-gray;
    margin-left: 5px;
  }
  .pipe {
    color: $primary-border-color;
    margin-right: 6px;
    @include breaks.respond-to(mobile) {
      color: transparent;
      margin-right: 0;
    }
  }
}
