@if (testResult) {
  <h2 mat-dialog-title class="title">
    {{ testResult.name }}
    @if (statusMap.get(testResult.status); as status) {
      <div class="status" [ngClass]="status.class">
        <mat-icon>{{ status.icon }}</mat-icon
        >{{ status.name }}
      </div>
    }
  </h2>
  <mat-dialog-content>
    @for (message of testResult.chatHistory; track message; let even = $even) {
      <glxy-chat-message-group [type]="even ? 'sent' : 'received'" [messageFrom]="getRole(message.role)">
        <glxy-chat-message [messageText]="message.content"></glxy-chat-message>
      </glxy-chat-message-group>
    }
    <mat-divider></mat-divider>
    <div class="evaluation">
      @if (testResult.reasoning) {
        <h3>Reasoning</h3>
        <p>{{ testResult.reasoning }}</p>
      }
      @if (testResult.citations?.length > 0) {
        <h3 class="sources">Sources</h3>
        <mat-list>
          @for (citation of testResult.citations; track citation) {
            <mat-list-item>
              <p matListItemTitle>{{ citation.title }}</p>
              @if (citation.link) {
                <div matListItemLine>
                  <a [href]="citation.link" target="_blank"
                    >{{ citation.link }}<mat-icon inline="true">open_in_new</mat-icon></a
                  >
                </div>
              }
              @if (citation.fileUrl) {
                <div matListItemLine>
                  <a matListItemLine [href]="citation.fileUrl" target="_blank"
                    >View File<mat-icon inline="true">open_in_new</mat-icon></a
                  >
                </div>
              }
              <div matListItemLine>{{ citation.reason }}</div>
            </mat-list-item>
          }
        </mat-list>
      }

      <h3>Expectation</h3>
      <p>{{ testResult.expected }}</p>

      <h3>Evaluation</h3>
      <p>{{ testResult.evaluation }}</p>
    </div>
  </mat-dialog-content>
}
