<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Loading...</title>
    <base href="/" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
    />

    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link rel="preconnect" href="hhttps://storage.googleapis.com" />

    <link
      href="https://fonts.googleapis.com/css?family=Material+Icons&display=block"
      rel="stylesheet"
      type="text/css"
    />

    <link id="app-favicon" rel="icon" type="image/x-icon" />
    <link rel="apple-touch-icon" sizes="144x144" href="/launcherIcon/144/144" />
    <link rel="apple-touch-icon" sizes="192x192" href="/launcherIcon/192/192" />
    <link rel="apple-touch-icon" sizes="512x512" href="/launcherIcon/512/512" />
    <link rel="manifest" href="/manifest.json" />
    <style>
      @keyframes spinner {
        0% {
          transform: rotate(0);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      body {
        background: #fafafa;
      }

      .loading {
        height: 100vh;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        display: none;
      }

      .loading > .loading-container {
        position: relative;
        border-radius: 50%;
        height: 84px;
        width: 84px;
        text-align: center;
        overflow: hidden;
      }

      .loading > .loading-container > .logo {
        z-index: 3;
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0;
        margin: 16px auto auto;
        width: 56px;
        height: 56px;
        fill: #424242;
      }

      .loading > .loading-container > .loading-spinner {
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }

      .loading > .loading-container > .loading-spinner > .loading-bg-drk {
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #424242;
        border: 1px solid #fafafa;
        border-radius: 50%;
      }

      .loading > .loading-container > .loading-spinner > .loading-bg-white {
        z-index: 2;
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        background: #fafafa;
        border-radius: 50%;
      }

      .loading > .loading-container > .loading-spinner > .loading-bg-box {
        z-index: 2;
        position: absolute;
        top: 0;
        left: 0;
        right: 50%;
        bottom: 0;
        background: #fafafa;
        animation: spinner 2s cubic-bezier(0.7, 0.5, 0.5, 0.7) infinite;
        transform-origin: 100% 50%;
      }

      bc-root:empty + .loading,
      bc-root > .loading:nth-child(3) {
        display: flex;
      }
    </style>
    <style media="print">
      #_hj_feedback_container {
        display: none !important;
      }
    </style>
    <script async src="https://js.stripe.com/v3/"></script>

    <!-- vStaticInject:appId -->
    <!-- vStaticInject:environment -->
    <!-- vStaticInject:deployment -->
    <!-- vStaticInject:partnerId -->
    <!-- vStaticInject:iam -->

    <script>
      var isGreyLabel = false;
      var multiPartnerApps = ['NBLY', 'PRBS'];
      function setPartnerId() {
        const storedPartnerId = localStorage.getItem('mobile-partner-id')?.toUpperCase();
        if (multiPartnerApps.includes(partnerId) && multiPartnerApps.includes(storedPartnerId)) {
          partnerId = storedPartnerId;
        }

        if (!partnerId) {
          partnerId = localStorage.getItem('mobile-partner-id')?.toUpperCase();
        }
      }

      setPartnerId();
    </script>

    <script src="https://fast.wistia.com/embed/medias/0biegn1dzb.jsonp" async></script>
    <script charset="ISO-8859-1" src="https://fast.wistia.com/assets/external/E-v1.js" async></script>
    <script src="https://cdn.apigateway.co/static/no-es6.js" nomodule defer></script>
    <script>
      initMap = () => {};
    </script>
    <script
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDqWGgrDbTRHlVwo_ViFD05agVFY7Do7zI&libraries=places&callback=initMap"
      async
    ></script>
    <script type="text/javascript" src="https://cdn.wootric.com/wootric-sdk.js"></script>
  </head>

  <body class="mat-typography">
    <bc-root></bc-root>
    <div class="loading">
      <div class="loading-container">
        <svg xmlns="http://www.w3.org/2000/svg" class="logo" width="24" height="24" viewBox="0 0 24 24">
          <path d="M0 0h24v24H0z" fill="none" />
          <path d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z" />
        </svg>
        <div class="loading-spinner">
          <div class="loading-bg-drk"></div>
          <div class="loading-bg-white"></div>
          <div class="loading-bg-box"></div>
        </div>
      </div>
    </div>
    <script>
      (function (w, d, s, g, js, fs) {
        g = w.gapi || (w.gapi = {});
        js = d.createElement(s);
        fs = d.getElementsByTagName(s)[0];
        js.src = 'https://apis.google.com/js/api.js';
        fs.parentNode.insertBefore(js, fs);
        js.onload = function () {
          g.load('client');
        };
      })(window, document, 'script');
    </script>
  </body>
</html>
