import { Component, Inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { TermsOfServiceData } from './interface/terms-of-service-data.interface';

@Component({
  selector: 'bc-terms-of-service',
  templateUrl: './terms-of-service.component.html',
  styleUrls: ['./terms-of-service.component.scss'],
  imports: [TranslateModule, MatButtonModule, MatCheckboxModule, MatDialogModule, GalaxyPipesModule],
})
export class TermsOfServiceDialogComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<TermsOfServiceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TermsOfServiceData,
  ) {}

  ngOnInit(): void {
    if (this.data?.partner) {
      this.data.termsOfService = this.replaceVariables(this.data?.termsOfService);
    }

    this.data.termsOfService = this.convertToHtml(this.data?.termsOfService);
  }

  convertToHtml(escapeString: string): string {
    const parser = new DOMParser();
    return parser.parseFromString(escapeString, 'text/html').body.textContent;
  }

  accepts(value: boolean): void {
    this.data.accepted = value;
    this.close();
  }

  replaceVariables(termsOfService: string): string {
    return termsOfService.replace(/\[\[partnerName\]\]/gm, this.data?.partner?.name);
  }

  close(): void {
    this.dialogRef.close(this.data);
  }
}
