import { inject, Injectable } from '@angular/core';
import {
  ConversationApiService,
  ConversationChannel,
  CreateConversationResponse,
  GetMultiConversationDetailsV2Request,
  MediaInterface,
  MessageType,
  MetadataIdentifier,
  Participant,
  PlatformLocation,
  SendMessageResponse,
} from '@vendasta/conversation';
import { catchError, defaultIfEmpty, EMPTY, firstValueFrom, map, Observable, of } from 'rxjs';
import { ConversationDetail } from './interface/conversation.interface';
import { fromFirestoreId } from './conversation-utils';
import { FieldMask } from '@vendasta/contacts';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { buildSendMessageTrackProperties } from './conversation-utils';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export class ConversationStatelessService {
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly analyticsService = inject(ProductAnalyticsService);

  /**
   * create a conversation using the Conversation SDK/API
   * @param {Participant[]} participants - A list of participants
   * @param {ConversationChannel} conversationChannel - A conversation channel
   * @param originLocation - The origin location of the conversation
   * @param instanceId - The instance ID of the conversation, this should typically be empty
   * @return {Observable<CreateConversationResponse>} - An observable that contains a CreateConversationResponse
   */
  createConversation(
    participants: Participant[],
    conversationChannel: ConversationChannel,
    originLocation: PlatformLocation,
    instanceId = '',
  ): Observable<CreateConversationResponse> {
    if (participants.length === 0) {
      console.error(
        'The participants are empty. It`s necessary send at least two participants to create a conversation',
      );
      return of({} as CreateConversationResponse);
    }

    if (conversationChannel === ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED) {
      console.error('The conversationChannel is undefined. It`s necessary send a valid ConversationChannel');
      return of({} as CreateConversationResponse);
    }

    return this.conversationApiService
      .createConversation({
        participants: participants,
        channel: conversationChannel,
        originLocation: originLocation,
        instanceId: instanceId,
      })
      .pipe(
        catchError((err) => {
          console.warn('error to createConversation', err);
          return EMPTY;
        }),
        defaultIfEmpty({} as CreateConversationResponse),
      );
  }

  /**
   * Get conversation details by conversation ID
   * @param {string} conversationId - The conversation ID
   * @return {Observable<ConversationDetail>} - An observable that contains the conversation details
   */
  getConversationDetail(conversationId: string): Observable<ConversationDetail> {
    conversationId = fromFirestoreId(conversationId);

    return this.getMultiConversationDetails([conversationId]).pipe(
      map((conversationsDetails) => {
        return conversationsDetails[0];
      }),
    );
  }

  /**
   * Get multiple conversation details by conversation IDs
   * @param {string[]} conversationIds - The conversation IDs
   * @param {string[]} exclusions - Elements of the response conversation details to exclude. Defaults to exclude conversation_summary only.
   * @return {Observable<ConversationDetail[]>} - An observable that contains the conversation details.
   * The conversation summary and message metadata will be excluded to prevent large responses.
   */
  getMultiConversationDetails(conversationIds: string[], exclusions: string[] = ['conversation_summary']) {
    if (conversationIds.length === 0) {
      return of([]);
    }

    const req = new GetMultiConversationDetailsV2Request();
    req.conversationIds = conversationIds;
    if (exclusions.length > 0) {
      req.exclusions = new FieldMask({ paths: exclusions });
    }

    return this.conversationApiService.getMultiConversationDetailsV2(req).pipe(
      map((response) => response.conversations),
      catchError((error) => {
        console.warn('error to getMultiConversationDetails collections', error);
        if (error?.status === 403) {
          this.snackbarService.openErrorSnack('INBOX.ERROR.PERMISSION_DENY');
        }
        return of([]);
      }),
    );
  }

  async sendMessage(
    conversationId: string,
    type: MessageType,
    body: string,
    channel: ConversationChannel,
    originLocation: PlatformLocation,
    media: MediaInterface[] = [],
    sender: Participant,
    recipient?: Participant,
    metadata?: {
      identifier: MetadataIdentifier;
      data: {
        [key: string]: string;
      };
    }[],
  ): Promise<SendMessageResponse> {
    if (!conversationId) {
      throw new Error('The conversationId is empty. It`s necessary send a valid conversationId');
    }

    if (!body && media.length === 0 && type !== MessageType.MESSAGE_TYPE_TEMPLATE) {
      throw new Error('The body is empty. It`s necessary send a valid body');
    }

    if (channel === ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED || channel === null) {
      throw new Error('The channel is invalid. It`s necessary send a valid channel');
    }

    if (!originLocation) {
      throw new Error('The originLocation is invalid. It`s necessary send a valid originLocation');
    }

    const trackingProps = buildSendMessageTrackProperties(
      conversationId,
      sender,
      channel,
      media.length,
      originLocation,
    );

    try {
      const result = await firstValueFrom(
        this.conversationApiService.sendMessage({
          sender,
          recipient,
          conversationId,
          type,
          body,
          originLocation,
          media,
          channel,
          metadata,
        }),
      );
      if (!result) {
        console.warn('ConversationStatelessService sendMessage value', result);
        return result;
      }

      this.analyticsService.trackEvent('inbox', 'conversation', 'send-message-success', 0, trackingProps);

      return result;
    } catch (httpErrorResponse) {
      this.analyticsService.trackEvent('inbox', 'conversation', 'send-message-error', 0, trackingProps);
      console.error('ConversationStatelessService sendMessage error', httpErrorResponse);
      const message = (httpErrorResponse as HttpErrorResponse)?.error?.message;
      if (message) {
        this.snackbarService.openErrorSnack(message);
      }
      throw httpErrorResponse;
    }
  }
}
