<div
  class="messages-top-bar"
  [ngClass]="{ 'sidebar__messages-top-bar': (useSidebarStyles$ | async) }"
  *ngIf="conversationDetail$ | async"
>
  @if ($showInboxBack()) {
    <div class="icon-back" [ngClass]="{ 'sidebar__icon-back': useSidebarStyles$ | async }" (click)="back()">
      <span class="material-icons">arrow_back</span>
    </div>
  }

  <div class="messages-recipient">
    <div class="recipient-info">
      <div class="conversation-title" [ngClass]="{ 'clickable-title': canAccessInboxSidebar() }">
        <inbox-conversation-title
          [conversationDetail]="conversationDetail$ | async"
          [conversationClosed]="!isOpen"
          [displayOnTopBar]="true"
          (click)="toggleDetailsDrawer()"
        ></inbox-conversation-title>
      </div>
      <div class="recipient-details">
        <ng-container *ngIf="isInternalInfoDeleted">
          <glxy-badge [color]="'red'">{{ 'INBOX.INFO.DELETED' | translate }}</glxy-badge>
        </ng-container>
        @if (canAccessInboxSidebar()) {
          <button mat-icon-button (click)="toggleDetailsDrawer()">
            <mat-icon class="info-pane-button">info_outlined</mat-icon>
          </button>
        }
      </div>
    </div>
    <inbox-conversation-account
      *ngIf="isBusinessApp && !isMobile"
      [conversationDetail]="conversationDetail$ | async"
    ></inbox-conversation-account>
    <inbox-conversation-assignee
      *ngIf="displayAssignee"
      [conversationDetail]="conversationDetail$ | async"
    ></inbox-conversation-assignee>
  </div>

  <div class="messages-actions">
    <ng-container *ngIf="showInboxViews$ | async">
      <!-- Small Button -->
      <button
        attr.data-action="clicked-conversation-follow-{{ (useSidebarStyles$ | async) ? 'sidebar' : 'fullview' }}"
        class="follow-button icon-button"
        mat-icon-button
        (click)="updateFollowStatus()"
        [matTooltip]="(isFollowing ? 'INBOX.VIEWS.FOLLOWING' : 'INBOX.VIEWS.FOLLOW') | translate"
        [ngClass]="{ 'follow-button-mobile': (useSidebarStyles$ | async) === false }"
      >
        <mat-icon>{{ isFollowing ? 'star' : 'star_border' }}</mat-icon>
      </button>

      <!-- Large Button -->
      <button
        attr.data-action="clicked-conversation-follow-{{ (useSidebarStyles$ | async) ? 'sidebar' : 'fullview' }}"
        class="follow-button follow-button-large"
        mat-stroked-button
        (click)="updateFollowStatus()"
        *ngIf="(useSidebarStyles$ | async) === false"
      >
        <mat-icon>{{ isFollowing ? 'star' : 'star_border' }}</mat-icon>
        {{ (isFollowing ? 'INBOX.VIEWS.FOLLOWING' : 'INBOX.VIEWS.FOLLOW') | translate }}
      </button>
    </ng-container>

    @if ($showConversationOverlayButton()) {
      <button
        mat-icon-button
        class="menu-icon"
        (click)="openConversationOverlay()"
        [matTooltip]="'INBOX.CHAT.OPEN_SIDE_PANEL_TOOLTIP' | translate"
      >
        <mat-icon>flip_to_front</mat-icon>
      </button>
    }

    <button
      mat-icon-button
      class="icon-button"
      [matMenuTriggerFor]="menu"
      aria-label="More"
      data-action="clicked-menu-conversation-top-bar"
    >
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      @if (!(unseen$ | async)?.read) {
        <button mat-menu-item (click)="markRead()" data-action="clicked-conversation-read">
          <span>{{ 'INBOX.SENDER.MARK_READ' | translate }}</span>
        </button>
      } @else {
        <button mat-menu-item (click)="markUnread()" data-action="clicked-conversation-unread">
          <span>{{ 'INBOX.SENDER.MARK_UNREAD' | translate }}</span>
        </button>
      }
      <button mat-menu-item (click)="toggleDetailsDrawer()" *ngIf="!isBusinessApp">
        {{ 'INBOX.INFO.CONVERSATION_DETAILS' | translate }}
      </button>

      <!-- Kabob Dynamic Available Actions -->
      <ng-container *ngFor="let action of kabobAvailableActions$ | async">
        <button mat-menu-item (click)="action.action()" [attr.data-action]="action.posthogLabel">
          <span>{{ action.title | translate }}</span>
        </button>
      </ng-container>

      @if (showInboxViews$ | async) {
        <button mat-menu-item (click)="updateFollowStatus()" class="icon-button">
          <div class="button-content">
            {{ (isFollowing ? 'INBOX.VIEWS.FOLLOWING' : 'INBOX.VIEWS.FOLLOW') | translate }}
            <mat-icon>{{ isFollowing ? 'star' : 'star_border' }}</mat-icon>
          </div>
        </button>
      }
      @if (showAIResponseToggle$ | async) {
        <button mat-menu-item (click)="toggleAIResponseStatus()">
          {{
            (aiResponseStatus() ? 'INBOX.SENDER.TURN_OFF_AI_REPLIES' : 'INBOX.SENDER.TURN_ON_AI_REPLIES') | translate
          }}
        </button>
      }
      @if ($showCloseConversationButton()) {
        <button mat-menu-item (click)="toggleConversationOpen()">
          {{ (isOpen ? 'INBOX.SENDER.MARK_AS_CLOSED' : 'INBOX.SENDER.OPEN_CONVERSATION') | translate }}
        </button>
      }
    </mat-menu>

    @if (isConversationOverlayMode()) {
      <button mat-icon-button class="menu-icon close" (click)="closeConversationOverlay()">
        <mat-icon>close</mat-icon>
      </button>
    }
  </div>
</div>
