import { Component, EventEmitter, inject, Input, OnInit, Optional, Output } from '@angular/core';

import { MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { MatDivider } from '@angular/material/divider';
import { BillingUiModule } from '@vendasta/billing-ui';
import { Frequency } from '@vendasta/galaxy/frequency';
import { MatDialog } from '@angular/material/dialog';
import { distinctUntilChanged, map, shareReplay, switchMap, take } from 'rxjs/operators';
import { BehaviorSubject, combineLatest, firstValueFrom, Observable, ReplaySubject } from 'rxjs';
import { PaymentMethod, PaymentMethodService, PaymentMethodType, PaymentService, StripeService } from '@galaxy/billing';
import { CommonModule } from '@angular/common';
import { OrderPaymentMethodSelectorComponent } from './select-payment-method/orders-payment-method.component';
import { SelectedPaymentMethod } from './select-payment-method/payment-method';
import { CustomerRecipientInterface, LineItem, SalesOrdersService } from '@vendasta/sales-orders';
import { isEqual } from 'lodash-es';
import { OrderStoreService } from '../../core/orders.service';
import { OrderPermissionsService } from '../../core/permissions';
import { OrderAction } from '../../core/permissions/permissions';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { RetailSummaryService } from './retail-summary.service';
import {
  FuturePaymentsDisplayComponent,
  FuturePaymentDisplayItemsWithCurrency,
} from '../future-payments-display/future-payments-display.component';
import { TaxOptions } from '@vendasta/sales-orders/lib/_internal';

export interface RetailSummary {
  subtotal: number;
  taxAmount: number;
  firstPayment: number;
  currencyCode: string;
  taxes: number;
  monthlyTotal: number;
  yearlyTotal: number;
  hasScheduledLineItems: boolean;
}

@Component({
  selector: 'orders-retail-summary',
  templateUrl: './retail-summary.component.html',
  styleUrls: ['./retail-summary.component.scss'],
  providers: [RetailSummaryService],
  imports: [
    MatExpansionPanelTitle,
    TranslateModule,
    MatExpansionPanel,
    MatExpansionPanelHeader,
    MatDivider,
    BillingUiModule,
    CommonModule,
    OrderPaymentMethodSelectorComponent,
    MatTooltipModule,
    GalaxyTooltipModule,
    FuturePaymentsDisplayComponent,
  ],
})
export class RetailSummaryComponent implements OnInit {
  @Input() summary: RetailSummary;

  @Input() accountGroupId: string;
  @Input() partnerId: string;
  @Input() orderId: string;
  @Input() canChargeOrder: boolean;
  @Input() orderCustomerRecipient: CustomerRecipientInterface;
  @Input() orderPaymentMethodToken: string;
  @Input() canEditPaymentMethod: boolean;
  @Input() taxOptions: TaxOptions[];

  @Input() set lineItems(lineItems: LineItem[]) {
    if (lineItems?.length) {
      this.lineItems$$.next(lineItems);
    }
  }

  @Input() set contractStartDate(contractStartDate: Date) {
    if (contractStartDate) {
      this.contractStartDate$$.next(contractStartDate);
    }
  }

  @Output() orderCharged = new EventEmitter<boolean>();

  paymentMethods$$: ReplaySubject<PaymentMethod[]> = new ReplaySubject(1);
  loadingPaymentMethods$$: BehaviorSubject<boolean> = new BehaviorSubject(true);
  defaultPaymentMethod$$ = new ReplaySubject<PaymentMethod>(1);
  defaultPaymentMethodID: string;
  lineItems$$: BehaviorSubject<LineItem[]> = new BehaviorSubject([]);
  contractStartDate$$: BehaviorSubject<Date> = new BehaviorSubject<Date>(undefined);
  futurePaymentLineItems$: Observable<FuturePaymentDisplayItemsWithCurrency>;
  canCollectPaymentFromCustomer$: Observable<boolean>;
  private readonly paymentService = inject(PaymentService);

  protected readonly stripeKey = this.stripeService.getConnectPublicKey();
  protected stripeConnectId$;

  protected readonly Frequency = Frequency;

  constructor(
    private dialog: MatDialog,
    private paymentMethodService: PaymentMethodService,
    private stripeService: StripeService,
    private salesOrderService: SalesOrdersService,
    private retailSummaryService: RetailSummaryService,
    @Optional() private orderStoreService: OrderStoreService,
    @Optional() private permissionsService: OrderPermissionsService,
  ) {}

  ngOnInit(): void {
    this._listPaymentMethods();
    this.stripeConnectId$ = this.paymentService.getRetailProvider(this.partnerId).pipe(
      map((retailProvider) => retailProvider.stripeConnectId),
      shareReplay({ refCount: true, bufferSize: 1 }),
    );

    this.canCollectPaymentFromCustomer$ = this.permissionsService?.CanDoAction(OrderAction.CollectPaymentFromCustomer);
    this.futurePaymentLineItems$ = combineLatest([this.lineItems$$.asObservable(), this.contractStartDate$$]).pipe(
      distinctUntilChanged(
        ([prevLineItems, prevContractStartDate], [currLineItems, currContractStartDate]) =>
          prevLineItems?.length === currLineItems?.length &&
          isEqual(prevLineItems, currLineItems) &&
          prevContractStartDate === currContractStartDate,
      ),
      map(([lineItems, contractStartDate]) => {
        return this.retailSummaryService.constructFuturePaymentLineItemsArray(
          lineItems,
          this.taxOptions,
          contractStartDate,
        );
      }),
    );
  }

  private async _listPaymentMethods(): Promise<void> {
    this.loadingPaymentMethods$$.next(true);
    const paymentMethods = await firstValueFrom(this.paymentMethodService.list(this.partnerId, this.accountGroupId));
    this.paymentMethods$$.next(paymentMethods);

    let defaultPaymentMethod: PaymentMethod;
    if (this.orderPaymentMethodToken) {
      defaultPaymentMethod = (paymentMethods || []).find(
        (method) => method?.details?.id === this.orderPaymentMethodToken,
      );
    } else {
      defaultPaymentMethod = (paymentMethods || []).find((method) => method?.default);
    }

    this.defaultPaymentMethodID = defaultPaymentMethod?.details?.id;
    if (defaultPaymentMethod?.type === PaymentMethodType.CARD) {
      this.defaultPaymentMethod$$.next(defaultPaymentMethod);
    }
    this.loadingPaymentMethods$$.next(false);
  }

  onPaymentMethodSelected(paymentMethod: SelectedPaymentMethod): void {
    if (!paymentMethod) {
      this._listPaymentMethods();
      return;
    }
    if (paymentMethod.userId?.length > 0) {
      this.salesOrderService
        .updateCustomerRecipientUserId(this.orderId, this.accountGroupId, paymentMethod.userId)
        .pipe(
          switchMap(() =>
            this.salesOrderService.updatePaymentMethodToken(
              this.orderId,
              this.accountGroupId,
              paymentMethod.paymentMethodId,
            ),
          ),
          take(1),
        )
        .subscribe();
    }

    this.salesOrderService
      .updatePaymentMethodToken(this.orderId, this.accountGroupId, paymentMethod.paymentMethodId)
      .pipe(take(1))
      .subscribe(() => {
        this.orderStoreService?.reloadOrder();
      });

    this.paymentMethodService
      .list(this.partnerId, this.accountGroupId)
      .pipe(take(1))
      .subscribe((cards) => {
        const defaultCard = cards.find((card) => card?.details?.id === paymentMethod?.paymentMethodId);
        this.defaultPaymentMethod$$.next(defaultCard);
      });
  }

  onOrderCharged(orderCharged: boolean): void {
    this.orderCharged.emit(orderCharged);
  }
}
