@use 'design-tokens' as *;

glxy-page {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  text-align: center;
  font-size: $font-preset-3-size;
  color: $primary-font-color;
  margin-bottom: $spacing-4;
}

ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

li {
  padding-bottom: $spacing-3;
}

.provider {
  display: flex;
  align-items: center;
  gap: $spacing-3;
  width: 100%;
  height: 100%;
  padding: $spacing-4;
  border-radius: $default-border-radius;
  border-color: $border-color;
  background-color: transparent;
}

.provider-title {
  color: $primary-font-color;
  font-size: $font-preset-3-size;
}
