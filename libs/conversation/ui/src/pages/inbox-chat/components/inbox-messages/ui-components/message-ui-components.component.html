<div class="ui-button-wrapper" [ngClass]="{ 'align-self-end': (inputMessage() | messageStatusType) === 'sent' }">
  @if (messageButtons.length > 0) {
    @for (uiComponent of messageButtons; track uiComponent.button?.id) {
      @if ((uiComponent | showMessageButton: inputMessage()) && uiComponent.button && uiComponent.button.label) {
        <button mat-flat-button class="ui-button" (click)="handleButtonClick(uiComponent.button)">
          {{ uiComponent.button.label | translate }}
        </button>
      }
    }
  }
</div>
