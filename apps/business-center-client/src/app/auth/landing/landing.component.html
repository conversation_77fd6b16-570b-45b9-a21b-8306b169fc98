<glxy-page [disableShadow]="true">
  @if (isGreyLabelApp && isPartnerIDConfigured) {
    <glxy-page-toolbar>
      <glxy-page-nav>
        <glxy-page-nav-button (click)="resetProvider()">Back</glxy-page-nav-button>
      </glxy-page-nav>
      <glxy-page-title>Choose a provider</glxy-page-title>
    </glxy-page-toolbar>
  }

  <div class="landing">
    @if (logoUrl$ | async; as logoUrl) {
      <img class="logo" [src]="logoUrl" />
    } @else if (isGreyLabelApp) {
      <div class="greylabel-logo">
        <bc-business-app-building></bc-business-app-building>
      </div>
    }
    @if (businessAppName$ | async; as businessAppName) {
      <div class="welcome-text">Welcome to {{ businessAppName }}</div>
    }

    @if (isPartnerIDConfigured) {
      <div class="subtext">Sign in to get started</div>
    } @else {
      <div class="subtext">Enter your email to get started</div>
    }

    @if (!isMultiPartnerApp) {
      <button mat-flat-button class="btn" color="primary" (click)="signIn()">Sign In</button>
    } @else {
      <div class="namespace-selector">
        <glxy-form-field size="large" bottomSpacing="none">
          <glxy-label>Email</glxy-label>
          <input matInput placeholder="Enter your email..." type="email" [(ngModel)]="userEmail" />
        </glxy-form-field>
        <button mat-flat-button class="btn" color="primary" (click)="submitEmail()">
          @if (!isPartnerIDConfigured) {
            <glxy-button-loading-indicator [isLoading]="sendingOTP()">Continue</glxy-button-loading-indicator>
          } @else {
            Sign In
          }
        </button>
      </div>
    }
  </div>
</glxy-page>
