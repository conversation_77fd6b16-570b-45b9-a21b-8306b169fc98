import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { ImageService } from '../../core/image.service';

@Component({
  selector: 'bc-business-app-building',
  templateUrl: './business-app-building.component.html',
  styleUrls: ['./business-app-building.component.scss'],
  imports: [MatIconModule],
})
export class BusinessAppBuildingComponent {
  private readonly imageService = inject(ImageService);
  readonly businessIconImgSrc = this.imageService.getImageSrc('business_icon.svg');
}
