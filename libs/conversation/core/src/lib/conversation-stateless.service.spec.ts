import { createServiceFactory, mockProvider, SpectatorService } from '@ngneat/spectator/jest';
import { ConversationStatelessService } from './conversation-stateless.service';
import {
  ConversationApiService,
  ConversationChannel,
  CreateConversationResponse,
  GetMultiConversationDetailsV2Response,
  GetMultiConversationDetailsV2ResponseDetailedConversation,
  MessageType,
  Participant,
  ParticipantType,
  PlatformLocation,
  SendMessageResponse,
} from '@vendasta/conversation';
import { EMPTY, of, throwError } from 'rxjs';
import * as conversationUtils from './conversation-utils';
import { TranslateService } from '@ngx-translate/core';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

describe('ConversationStatelessService', () => {
  let spectator: SpectatorService<ConversationStatelessService>;
  let consoleErrorSpy: jest.SpyInstance;
  let fromFirestoreIdSpy: jest.SpyInstance;

  const conversationParticipantSenderMock = new Participant({
    participantId: 'PARTICIPANT-SENDER',
    name: 'Sender',
    participantType: ParticipantType.PARTICIPANT_TYPE_PARTNER,
  });

  const createService = createServiceFactory({
    service: ConversationStatelessService,
    providers: [
      mockProvider(ConversationApiService, {
        createConversation: jest.fn().mockReturnValue(of({} as CreateConversationResponse)),
        getMultiConversationDetailsV2: jest.fn().mockReturnValue(
          of({
            conversations: [
              {
                conversation: { conversationId: 'CONVERSATION-123' },
                participants: [],
              },
            ],
          } as unknown as GetMultiConversationDetailsV2Response),
        ),
      }),
      mockProvider(TranslateService, {
        instant: jest.fn((key) => key),
      }),
      mockProvider(SnackbarService, {
        openErrorSnack: jest.fn(),
      }),
      mockProvider(ProductAnalyticsService, {
        trackEvent: jest.fn(),
      }),
    ],
  });

  beforeEach(() => {
    spectator = createService();
    fromFirestoreIdSpy = jest.spyOn(conversationUtils, 'fromFirestoreId').mockReturnValue('CONVERSATION-123');
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createConversation', () => {
    it('should show an error on console when not providing a list of participants', () => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockClear();
      spectator.service.createConversation(
        [],
        ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );
      expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    });

    it('should show an error on console when providing an undefined conversation channel', () => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockClear();
      spectator.service.createConversation(
        [conversationParticipantSenderMock],
        ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );
      expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    });

    it('should call the createConversation from the conversationApiService when passed valid parameters', () => {
      const createConversationSpy = jest
        .spyOn(spectator.service['conversationApiService'], 'createConversation')
        .mockReturnValue(of({} as CreateConversationResponse));

      spectator.service.createConversation(
        [conversationParticipantSenderMock],
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );

      expect(createConversationSpy).toHaveBeenCalledWith({
        participants: [conversationParticipantSenderMock],
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        originLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        instanceId: '',
      });
    });

    it('should handle errors gracefully', () => {
      jest.spyOn(spectator.service['conversationApiService'], 'createConversation').mockReturnValue(EMPTY);

      spectator.service.createConversation(
        [conversationParticipantSenderMock],
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
      );

      // No assertion needed as we're testing that no exception is thrown
    });
  });

  describe('getMultiConversationDetails', () => {
    it('should return empty array when no conversation IDs are provided', () => {
      const result = spectator.service.getMultiConversationDetails([]);
      result.subscribe((conversations) => {
        expect(conversations).toEqual([]);
      });
    });

    it('should call the API service with the correct parameters without summary', () => {
      const getMultiConversationDetailsSpy = jest.spyOn(
        spectator.service['conversationApiService'],
        'getMultiConversationDetailsV2',
      );

      spectator.service.getMultiConversationDetails(['CONVERSATION-123']);

      expect(getMultiConversationDetailsSpy).toHaveBeenCalledWith({
        conversationIds: ['CONVERSATION-123'],
        exclusions: { paths: ['conversation_summary'] },
      });
    });

    it('should call the API service with the correct parameters with summary', () => {
      const getMultiConversationDetailsSpy = jest.spyOn(
        spectator.service['conversationApiService'],
        'getMultiConversationDetailsV2',
      );

      spectator.service.getMultiConversationDetails(['CONVERSATION-123'], ['conversation_summary', 'message.metadata']);

      expect(getMultiConversationDetailsSpy).toHaveBeenCalledWith({
        conversationIds: ['CONVERSATION-123'],
        exclusions: { paths: ['conversation_summary', 'message.metadata'] },
      });
    });

    it('should map the response correctly', () => {
      const mockResponse = {
        conversations: [
          {
            conversation: { conversationId: 'CONVERSATION-123' },
            participants: [],
          } as unknown as GetMultiConversationDetailsV2ResponseDetailedConversation,
        ],
      } as unknown as GetMultiConversationDetailsV2Response;

      jest
        .spyOn(spectator.service['conversationApiService'], 'getMultiConversationDetailsV2')
        .mockReturnValue(of(mockResponse));

      spectator.service.getMultiConversationDetails(['CONVERSATION-123']).subscribe((conversations) => {
        expect(conversations).toEqual(mockResponse.conversations);
      });
    });

    it('should handle errors gracefully', () => {
      jest
        .spyOn(spectator.service['conversationApiService'], 'getMultiConversationDetailsV2')
        .mockReturnValue(throwError(() => new Error('Test error')));

      spectator.service.getMultiConversationDetails(['CONVERSATION-123']).subscribe((conversations) => {
        expect(conversations).toEqual([]);
      });
    });
  });

  describe('getConversationDetail', () => {
    it('should convert conversation ID from firestore format', () => {
      spectator.service.getConversationDetail('test-doc-id');
      expect(fromFirestoreIdSpy).toHaveBeenCalledWith('test-doc-id');
    });

    it('should call getMultiConversationDetails with the single ID', () => {
      jest.spyOn(spectator.service, 'getMultiConversationDetails').mockReturnValue(of([]));

      spectator.service.getConversationDetail('test-doc-id');
      expect(spectator.service.getMultiConversationDetails).toHaveBeenCalledWith(['CONVERSATION-123']);
    });

    it('should return the first conversation from the result', () => {
      const mockConversation = {
        conversation: { conversationId: 'CONVERSATION-123' },
        participants: [],
      } as unknown as GetMultiConversationDetailsV2ResponseDetailedConversation;

      jest.spyOn(spectator.service, 'getMultiConversationDetails').mockReturnValue(of([mockConversation]));

      spectator.service.getConversationDetail('test-doc-id').subscribe((conversation) => {
        expect(conversation).toEqual(mockConversation);
      });
    });
  });

  describe('sendMessage', () => {
    const messageMedia = [
      {
        mediaContentType: 'image/png',
        mediaLocationPath: '2023-07-05-cfa39d6e-e89c-4ab8-9413-16951edcd92b',
        mediaFileName: 'test.png',
        fileSize: 100,
      },
    ];

    it('should call the sendMessage from the conversationApiService when passing valid args with media', async () => {
      const conversationApiService = spectator.inject(ConversationApiService);
      const analyticsService = spectator.inject(ProductAnalyticsService);

      jest.spyOn(conversationApiService, 'sendMessage').mockReturnValue(
        of({
          messageId: 'MESSAGE-123',
        } as SendMessageResponse),
      );
      jest.spyOn(analyticsService, 'trackEvent');

      const result = await spectator.service.sendMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test body',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        messageMedia,
        conversationParticipantSenderMock,
      );

      expect(conversationApiService.sendMessage).toHaveBeenCalledWith({
        conversationId: 'CONVERSATION-123',
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        body: 'Test body',
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        originLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        media: messageMedia,
        sender: conversationParticipantSenderMock,
      });
      expect(result).toEqual({
        messageId: 'MESSAGE-123',
      });
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should call the sendMessage from the conversationApiService when only passing message body without media', async () => {
      const conversationApiService = spectator.inject(ConversationApiService);
      const analyticsService = spectator.inject(ProductAnalyticsService);

      jest.spyOn(conversationApiService, 'sendMessage').mockReturnValue(
        of({
          messageId: 'MESSAGE-123',
        } as SendMessageResponse),
      );
      jest.spyOn(analyticsService, 'trackEvent');

      const result = await spectator.service.sendMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        'Test body',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        [],
        conversationParticipantSenderMock,
      );

      expect(conversationApiService.sendMessage).toHaveBeenCalledWith({
        conversationId: 'CONVERSATION-123',
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        body: 'Test body',
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        originLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        media: [],
        sender: conversationParticipantSenderMock,
      });
      expect(result).toEqual({
        messageId: 'MESSAGE-123',
      });
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should raise an error when the conversationId is empty', async () => {
      await expect(
        spectator.service.sendMessage(
          '',
          MessageType.MESSAGE_TYPE_MESSAGE,
          'Test body',
          ConversationChannel.CONVERSATION_CHANNEL_SMS,
          PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
          [],
          conversationParticipantSenderMock,
        ),
      ).rejects.toThrow('The conversationId is empty. It`s necessary send a valid conversationId');
    });

    it('should raise an error when both body and media are empty', async () => {
      await expect(
        spectator.service.sendMessage(
          'CONVERSATION-123',
          MessageType.MESSAGE_TYPE_MESSAGE,
          '',
          ConversationChannel.CONVERSATION_CHANNEL_SMS,
          PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
          [],
          conversationParticipantSenderMock,
        ),
      ).rejects.toThrow('The body is empty. It`s necessary send a valid body');
    });

    it('should raise an error when the channel is invalid', async () => {
      await expect(
        spectator.service.sendMessage(
          'CONVERSATION-123',
          MessageType.MESSAGE_TYPE_MESSAGE,
          'Test body',
          ConversationChannel.CONVERSATION_CHANNEL_UNDEFINED,
          PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
          [],
          conversationParticipantSenderMock,
        ),
      ).rejects.toThrow('The channel is invalid. It`s necessary send a valid channel');
    });

    it('should raise an error when the originLocation is invalid', async () => {
      await expect(
        spectator.service.sendMessage(
          'CONVERSATION-123',
          MessageType.MESSAGE_TYPE_MESSAGE,
          'Test body',
          ConversationChannel.CONVERSATION_CHANNEL_SMS,
          PlatformLocation.PLATFORM_LOCATION_UNDEFINED,
          [],
          conversationParticipantSenderMock,
        ),
      ).rejects.toThrow('The originLocation is invalid. It`s necessary send a valid originLocation');
    });

    it('should pass media when provided and empty body', async () => {
      const conversationApiService = spectator.inject(ConversationApiService);
      const analyticsService = spectator.inject(ProductAnalyticsService);

      jest.spyOn(conversationApiService, 'sendMessage').mockReturnValue(
        of({
          messageId: 'MESSAGE-123',
          workflowId: '',
          toApiJson: jest.fn(),
        }),
      );
      jest.spyOn(analyticsService, 'trackEvent');

      const result = await spectator.service.sendMessage(
        'CONVERSATION-123',
        MessageType.MESSAGE_TYPE_MESSAGE,
        '',
        ConversationChannel.CONVERSATION_CHANNEL_SMS,
        PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        messageMedia,
        conversationParticipantSenderMock,
      );

      expect(conversationApiService.sendMessage).toHaveBeenCalledWith({
        conversationId: 'CONVERSATION-123',
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        body: '',
        channel: ConversationChannel.CONVERSATION_CHANNEL_SMS,
        originLocation: PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
        media: messageMedia,
        sender: conversationParticipantSenderMock,
      });
      expect(result).toEqual({
        messageId: 'MESSAGE-123',
        workflowId: '',
        toApiJson: expect.any(Function),
      });
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should handle API errors properly', async () => {
      const conversationApiService = spectator.inject(ConversationApiService);
      const analyticsService = spectator.inject(ProductAnalyticsService);
      const error = new Error('API error');

      jest.spyOn(conversationApiService, 'sendMessage').mockReturnValue(throwError(() => error));
      jest.spyOn(analyticsService, 'trackEvent');
      jest.spyOn(conversationUtils, 'buildSendMessageTrackProperties').mockReturnValue({
        id: 'CONVERSATION-123',
      } as any);

      await expect(
        spectator.service.sendMessage(
          'CONVERSATION-123',
          MessageType.MESSAGE_TYPE_MESSAGE,
          'Test body',
          ConversationChannel.CONVERSATION_CHANNEL_SMS,
          PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP,
          [],
          conversationParticipantSenderMock,
        ),
      ).rejects.toThrow('API error');

      expect(spectator.inject(ProductAnalyticsService).trackEvent).toHaveBeenCalledWith(
        'inbox',
        'conversation',
        'send-message-error',
        0,
        {
          id: 'CONVERSATION-123',
        },
      );
    });
  });
});
