import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AppPrice } from '@vendasta/marketplace-apps';

export interface VariablePriceInputs {
  appPrice: AppPrice;
  name: string;
  iconUrl?: string;
  packageID?: string;
  packageName?: string;
  packageIconUrl?: string;
  customPrice?: number;
}

export type AppVariablePriceMap = Map<string, VariablePriceInputs>;
export type PackageVariablePriceMap = Map<string, AppVariablePriceMap>;

@Component({
  selector: 'orders-variable-prices',
  templateUrl: './variable-prices.component.html',
  styleUrls: ['./variable-prices.component.scss'],
  standalone: false,
})
export class VariablePricesComponent {
  @Input() variablePricesMap: AppVariablePriceMap;
  @Input() variablePackagePricesMap: PackageVariablePriceMap;
  @Input() partnerCurrency: string;
  @Input() viewOnly: boolean;

  @Output() updateData: EventEmitter<{ appId: string; customPrice: number; packageId?: string }> = new EventEmitter();

  changeSpendAmount(customPrice, appId, packageId?): void {
    if (!this.viewOnly) {
      this.updateData.emit({ appId: appId, customPrice: customPrice, packageId: packageId });
    }
  }
}
