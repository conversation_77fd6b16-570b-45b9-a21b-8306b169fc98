import { HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';
import {
  AccountsService,
  AppAndAddonActivationStatus,
  ListAppAndAddonActivationStatusFilter,
} from '@vendasta/accounts/legacy';
import { DateRange } from '@vendasta/galaxy/utility/date-utils';
import {
  AddonActivation,
  DirectSyncSource,
  FieldMask,
  GetActiveSEOAddonsRequest,
  GetDirectSyncSourceInfoRequest,
  GetLocalSearchSEODataRequest,
  GetMultiListingProfileRequest,
  GetOrGenerateSEOSuggestedKeywordsResponse,
  GetPartnerSettingsRequest,
  GetPartnerSettingsResponse,
  GetSEODataSummaryRequest,
  GetSEOSettingsRequest,
  GetSEOSuggestedKeywordsRequest,
  KeywordInfo,
  ListingProductsApiService,
  ListingProfile,
  ListingProfileApiService,
  LocalSearchData,
  PartnerSettingsApiService,
  ProjectionFilter,
  RichData,
  SaveSEOSettingsRequest,
  SEOApiService,
  SEODataSummary,
  SEOSettingsResponse,
  SEOSuggestedKeywordsApiService,
  UpdateListingProfileRequest,
  UpdateOperation,
} from '@vendasta/listing-products';
import {
  Alignment,
  AlignmentPeriod,
  AlignmentPeriodCalendar,
  BusinessResourceId,
  FieldFilter,
  FieldFilterOperator,
  Filter,
  GroupBy,
  Measure,
  MeasureAggregate,
  MeasureAggregateOperator,
  MetricResult,
  MultiLocationAnalyticsService,
  Order,
  PropertyType,
  QueryMetricsRequest,
  QueryMetricsResponse,
  ResourceId,
} from '@vendasta/multi-location-analytics';
import dayjs from 'dayjs';
import { BehaviorSubject, combineLatest, defer, EMPTY, Observable, of } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import {
  AGIDTOKEN,
  DEMO_PAID_EDITION,
  GoogleSourceID,
  KeywordStore,
  PROD_PAID_EDITION,
  SEARCH_RADIUS_LOCAL,
} from '../../local-seo';
import { PartnerMarket, UpgradeCTADialogService } from '../../upgrade-cta-dialog/upgrade-cta-dialog.service';

export interface SEODataPoint {
  date: Date;
  value: number;
}

export interface SeoDataKeywordMetrics {
  dateRange?: DateRange;
  keyword?: string;
  localRank?: SEODataPoint[];
  organicRank?: SEODataPoint[];
}

export interface SeoDataSettings {
  businessId?: string;
  localSearchRadius?: number;
  isFullSearchEnabled?: boolean;
}

const ADDON_INCREMENT = 15;

const SEODATA_STATS: string[] = ['local_rank', 'organic_rank'];
const free_edition_keywords = 3;
const paid_edition_keywords = 15;

export const MaxRank = 50;

@Injectable({
  providedIn: 'root',
})
export class KeywordTrackingService {
  public listingProfile$$: BehaviorSubject<ListingProfile> = new BehaviorSubject<ListingProfile>(null);
  public loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public keywordLimit$: Observable<number>;

  public readonly SEOSummaryData$: Observable<SEODataSummary[]>;
  public keywordMetrics$: Observable<SeoDataKeywordMetrics[]>;

  private seoSettings$$: BehaviorSubject<SeoDataSettings> = new BehaviorSubject<SeoDataSettings>({});
  public readonly seoSettings$: Observable<SeoDataSettings>;

  private keywordStore$$: BehaviorSubject<KeywordStore> = new BehaviorSubject<KeywordStore>({
    keywords: [],
    syncingKeywords: [],
    favoriteKeywords: [],
  });
  public readonly keywordStore$: Observable<KeywordStore> = this.keywordStore$$.asObservable();

  private canEditKeywords$$ = new BehaviorSubject<boolean>(true);
  public canEditKeywords$ = this.canEditKeywords$$.asObservable();

  private defaultMarketID = 'all-markets-default-fallback';

  public suggestedKeywords$: Observable<KeywordInfo[]>;
  public suggestedKeywordsErrorMessage: string | null = null;

  private dateRange$$: BehaviorSubject<DateRange> = new BehaviorSubject<DateRange>(undefined);
  private addOns$: Observable<AddonActivation[]>;

  public readonly partnerSettings$: Observable<GetPartnerSettingsResponse>;

  public missingGeoLocation$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public missingWebsite$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  public suggestedKeywords$$: BehaviorSubject<KeywordInfo[]> = new BehaviorSubject<KeywordInfo[]>([]);

  public isLBFreeEdition$: Observable<boolean>;
  public gbpData$: Observable<DirectSyncSource>;

  constructor(
    private listingProfileService: ListingProfileApiService,
    private lpSEOService: SEOApiService,
    private seoPartnerSettingsService: PartnerSettingsApiService,
    private mla: MultiLocationAnalyticsService,
    @Inject(AGIDTOKEN) private agid$: Observable<string>,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private listingProductsApiService: ListingProductsApiService,
    private seoSuggestedKeywordsService: SEOSuggestedKeywordsApiService,
    private accountsService: AccountsService,
    private upgradeDialogService: UpgradeCTADialogService,
    private listingProductsService: ListingProductsApiService,
  ) {
    this.isLBFreeEdition$ = this.agid$.pipe(
      switchMap((agid) => {
        const filters: ListAppAndAddonActivationStatusFilter = {
          appIds: ['MS'],
          statuses: [AppAndAddonActivationStatus.ACTIVATED, AppAndAddonActivationStatus.CANCELED],
        };
        return accountsService.listAppsAndAddonsActivationStatusesForBusiness(agid, filters).pipe(
          map((activations) => {
            if (activations?.length > 0) {
              switch (activations[0].editionId) {
                case DEMO_PAID_EDITION:
                  return false;
                case PROD_PAID_EDITION:
                  return false;
              }
            }
            return true;
          }),
          shareReplay(1),
        );
      }),
    );

    this.partnerSettings$ = this.upgradeDialogService.partnerMarket$.pipe(
      switchMap((pm) =>
        this.seoPartnerSettingsService
          .getPartnerSettings(
            new GetPartnerSettingsRequest({
              partnerId: pm.partnerId,
              marketId: this.defaultMarketID,
            }),
          )
          .pipe(
            catchError((err: HttpErrorResponse) => {
              if (err.status === 404) {
                return of(new GetPartnerSettingsResponse({}));
              }
              throw err;
            }),
          ),
      ),
    );

    this.gbpData$ = this.agid$.pipe(
      switchMap((agid) =>
        this.listingProductsService
          .getDirectSyncSourceInfo(
            new GetDirectSyncSourceInfoRequest({
              businessId: agid,
              refresh: true,
            }),
          )
          .pipe(
            map((res) => {
              const gbpData =
                res.directSubmitSources.find((s) => s.sourceId === GoogleSourceID) || new DirectSyncSource();
              return gbpData;
            }),
          ),
      ),
      shareReplay(1),
    );
    this.fetchBusinessData();
    this.fetchSEODataSettings();
    this.getProductSettings();
    this.getSuggestedKeywords();

    const seoDataResponse$ = combineLatest([this.agid$, this.keywordStore$, this.dateRange$$.asObservable()]).pipe(
      filter(([agid, _, dateRange]) => !!agid && !!dateRange),
      switchMap(([agid, keywordStore, dateRange]: [string, KeywordStore, DateRange]) => {
        return this.GetSEOSummaryData(agid, keywordStore.keywords, dateRange).pipe(shareReplay(1));
      }),
    );

    this.SEOSummaryData$ = seoDataResponse$.pipe(
      map((res) => res.current),
      shareReplay(1),
    );

    this.suggestedKeywords$ = this.suggestedKeywords$$.asObservable();

    this.keywordMetrics$ = combineLatest([
      this.upgradeDialogService.partnerMarket$,
      this.agid$,
      this.keywordStore$,
      this.dateRange$$.asObservable(),
    ]).pipe(
      filter(([pm, agid, keywords, dateRange]) => !!pm && !!agid && !!keywords && !!dateRange),
      switchMap(([pm, agid, keywordStore, dateRange]: [PartnerMarket, string, KeywordStore, DateRange]) => {
        return this.fetchSEOQueryMetrics(pm.partnerId, agid, keywordStore.keywords, dateRange);
      }),
      shareReplay(1),
    );

    this.seoSettings$ = this.seoSettings$$.asObservable();

    this.addOns$ = this.agid$.pipe(
      switchMap((agid) => {
        return this.lpSEOService
          .getActiveSeoAddons(
            new GetActiveSEOAddonsRequest({
              businessId: agid,
            }),
          )
          .pipe(
            map((res) => {
              return res?.activeAddons || [];
            }),
          );
      }),
      shareReplay(1),
    );

    this.keywordLimit$ = combineLatest([this.isLBFreeEdition$, this.addOns$]).pipe(
      map(([isFreeEdition, addOns]) => {
        let keywordLimit = free_edition_keywords;

        if (!isFreeEdition) {
          keywordLimit = paid_edition_keywords;
        }

        if (addOns?.length > 0) {
          // ActiveSEOAddons will always contain one element, with the count property
          // incremented based on the number of activations for that add on
          const limitIncrement = addOns[0].count * ADDON_INCREMENT;
          keywordLimit = keywordLimit + limitIncrement;
        }

        return keywordLimit;
      }),
    );
  }

  private normalizeKeywordStore(partialStore: Partial<KeywordStore>): KeywordStore {
    const current = this.keywordStore$$.value;
    return {
      keywords: partialStore.keywords !== undefined ? partialStore.keywords : current.keywords || [],
      syncingKeywords:
        partialStore.syncingKeywords !== undefined ? partialStore.syncingKeywords : current.syncingKeywords || [],
      favoriteKeywords:
        partialStore.favoriteKeywords !== undefined ? partialStore.favoriteKeywords : current.favoriteKeywords || [],
    };
  }

  private updateKeywordStore(partialStore: Partial<KeywordStore>): void {
    this.keywordStore$$.next(this.normalizeKeywordStore(partialStore));
  }

  fetchSEODataSettings(): void {
    this.agid$
      .pipe(
        switchMap((agid) =>
          this.lpSEOService.getSeoSettings(new GetSEOSettingsRequest({ businessId: agid })).pipe(
            catchError((err: HttpErrorResponse) => {
              if (err.status !== 404) {
                const message = err.error?.message
                  ? err.error.message
                  : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
                this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
                return EMPTY;
              }
              return of(
                new SEOSettingsResponse({
                  businessId: agid,
                  localSearchRadius: SEARCH_RADIUS_LOCAL,
                  favoriteKeywords: [],
                }),
              );
            }),
            map((res) => {
              return new SEOSettingsResponse({
                businessId: res.businessId,
                localSearchRadius: res.localSearchRadius,
                favoriteKeywords: res.favoriteKeywords || [],
                isFullSearchEnabled: res.isFullSearchEnabled,
              });
            }),
          ),
        ),
      )
      .subscribe((seoSettings) => {
        this.seoSettings$$.next(
          new SEOSettingsResponse({
            businessId: seoSettings.businessId || '',
            localSearchRadius: seoSettings.localSearchRadius || SEARCH_RADIUS_LOCAL,
            isFullSearchEnabled: seoSettings.isFullSearchEnabled,
          }),
        );
        this.updateKeywordStore({
          favoriteKeywords: seoSettings.favoriteKeywords || [],
        });
      });
  }

  fetchBusinessData(): void {
    this.loading$$.next(true);
    this.agid$
      .pipe(
        switchMap((agid) =>
          this.listingProfileService
            .getMulti(
              new GetMultiListingProfileRequest({
                businessIds: [agid],
                languageCode: this.translate.currentLang || this.translate.defaultLang,
                projectionFilter: new ProjectionFilter({
                  richData: true,
                  napData: true,
                }),
              }),
            )
            .pipe(
              catchError((err: HttpErrorResponse) => {
                const message = err.error?.message
                  ? err.error.message
                  : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
                this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
                return EMPTY;
              }),
              map((res) => res.listingProfiles[0].listingProfile),
            ),
        ),
      )
      .subscribe((listingProfile) => {
        this.listingProfile$$.next(listingProfile);
        this.missingGeoLocation$$.next(
          !listingProfile?.napData?.location?.latitude && !listingProfile?.napData?.location?.longitude,
        );
        this.missingWebsite$$.next(!listingProfile?.napData?.website);
        const seoKeywords = listingProfile?.richData?.seoKeywords;
        const syncingKeywords = listingProfile?.richData?.syncingSeoKeywords;
        this.updateKeywordStore({
          keywords: seoKeywords?.filter((value) => value !== '') || [],
          syncingKeywords: syncingKeywords || [],
        });
        this.loading$$.next(false);
      });
  }

  fetchSEOQueryMetrics(
    pid: string,
    businessId: string,
    keywords: string[],
    range: DateRange,
  ): Observable<SeoDataKeywordMetrics[]> {
    this.loading$$.next(true);
    const metrics: SeoDataKeywordMetrics[] = [];
    const obsMetrics: Observable<SeoDataKeywordMetrics>[] = [];
    keywords.forEach((keyword) => {
      const result = this.mla
        .queryMetrics(generateSEODataQueryMetricsRequest(pid, businessId, keyword, range.start, range.end))
        .pipe(
          catchError((err: HttpErrorResponse) => {
            const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
            this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
            return EMPTY;
          }),
          map((resp: QueryMetricsResponse) => {
            const metricResults = this.mla.unwrapMetricsResponse(resp);
            return {
              dateRange: range,
              keyword: keyword,
              localRank: ParseRowsFromQueryMetricsResult(metricResults, SEODATA_STATS.indexOf('local_rank')),
              organicRank: ParseRowsFromQueryMetricsResult(metricResults, SEODATA_STATS.indexOf('organic_rank')),
            };
          }),
        );
      obsMetrics.push(result);
    });

    return combineLatest(obsMetrics).pipe(
      map((res) => {
        res.forEach((metric) => {
          metrics.push(metric);
        });
        return metrics;
      }),
      tap(() => {
        this.loading$$.next(false);
      }),
    );
  }

  getProductSettings(): void {
    this.loading$$.next(true);
    this.upgradeDialogService.partnerMarket$
      .pipe(
        switchMap((pm) =>
          this.listingProductsApiService.getProductSettings(
            new GetPartnerSettingsRequest({
              partnerId: pm.partnerId,
              marketId: this.defaultMarketID,
            }),
          ),
        ),
        take(1),
        catchError(() => {
          return of(null);
        }),
      )
      .subscribe((partnerSettings) => {
        this.setCanEditKeywords(partnerSettings?.canEditKeywords || false);
        this.loading$$.next(false);
      });
  }

  GetSEOSummaryData(
    businessID: string,
    keywords: string[],
    dateRange: DateRange,
  ): Observable<{ current: SEODataSummary[]; previous: SEODataSummary[] }> {
    return this.lpSEOService
      .getSeoDataSummary(
        new GetSEODataSummaryRequest({
          businessId: businessID,
          keywords: keywords,
          startDate: dateRange.start,
          endDate: dateRange.end,
        }),
      )
      .pipe(
        catchError((err: HttpErrorResponse) => {
          const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
          this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
          return EMPTY;
        }),
        map((seoSummaryData) => {
          return {
            current: seoSummaryData.data,
            previous: seoSummaryData.previousData,
          };
        }),
      );
  }

  GetLocalSEOSearchDetails(keyword: string): Observable<LocalSearchData[]> {
    return defer(() => {
      return this.agid$.pipe(
        take(1),
        switchMap((agid) => {
          return this.dateRange$$.pipe(
            take(1),
            switchMap((dateRange) => {
              return this.lpSEOService
                .getLocalSearchSeoData(
                  new GetLocalSearchSEODataRequest({
                    businessId: agid,
                    keyword: keyword,
                    startDate: dateRange.start,
                    endDate: dateRange.end,
                  }),
                )
                .pipe(
                  map((response) => {
                    return formatLocalSearchSEOData(response.localSearchData);
                  }),
                );
            }),
          );
        }),
      );
    });
  }

  getSuggestedKeywords(): void {
    this.loading$$.next(true);
    combineLatest([this.agid$, this.isLBFreeEdition$])
      .pipe(
        switchMap(([agid, isFree]: [string, boolean]) => {
          if (isFree) {
            return of(new GetOrGenerateSEOSuggestedKeywordsResponse({ keywordInfo: [] }));
          }
          return this.seoSuggestedKeywordsService
            .getOrGenerateSeoSuggestedKeywords(
              new GetSEOSuggestedKeywordsRequest({
                businessId: agid,
              }),
            )
            .pipe(
              catchError((error) => {
                this.suggestedKeywordsErrorMessage = error?.error?.message || error?.message;
                return of(new GetOrGenerateSEOSuggestedKeywordsResponse({ keywordInfo: [] }));
              }),
            );
        }),
      )
      .subscribe((suggestedkeywords) => {
        this.suggestedKeywords$$.next(
          suggestedkeywords.keywordInfo.map(
            (info) =>
              new KeywordInfo({
                keyword: info.keyword || '',
                competition: info.competition || 'LOW',
                competitionIndex: info.competitionIndex || 0,
                searchVolume: info.searchVolume || 0,
                lowTopOfPageBid: info.lowTopOfPageBid || 0,
                highTopOfPageBid: info.highTopOfPageBid || 0,
              }),
          ) || [],
        );
        this.loading$$.next(false);
      });
  }

  updateSeoSettingsForDialog(mapRadius: number, isFullSearchEnabled: boolean): void {
    this.agid$
      .pipe(
        take(1),
        switchMap((agid) =>
          this.lpSEOService
            .saveSeoSettings(
              new SaveSEOSettingsRequest({
                businessId: agid,
                localSearchRadius: mapRadius,
                fieldMask: new FieldMask({
                  paths: ['local_search_radius', 'is_full_search_enabled'],
                }),
                isFullSearchEnabled: isFullSearchEnabled,
              }),
            )
            .pipe(
              catchError((err: HttpErrorResponse) => {
                const message = err.error?.message
                  ? err.error.message
                  : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
                this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
                return EMPTY;
              }),
            ),
        ),
      )
      .subscribe(() => {
        this.fetchSEODataSettings();
      });
  }

  updateFavoriteKeywords(keywords: string[]): void {
    keywords = keywords.filter((keyword) => !!keyword);
    this.agid$
      .pipe(
        take(1),
        switchMap((agid) =>
          this.lpSEOService.saveSeoSettings(
            new SaveSEOSettingsRequest({
              businessId: agid,
              favoriteKeywords: keywords,
              fieldMask: new FieldMask({
                paths: ['favorite_keywords'],
              }),
            }),
          ),
        ),
      )
      .subscribe({
        error: (err) => {
          const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
          this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
        },
        next: () => {
          this.snackBar.open(this.translate.instant('LOCAL_SEO.FAVORITES_UPDATED'), '', {
            politeness: 'polite',
            duration: 5000,
          });
          this.updateKeywordStore({ favoriteKeywords: keywords });
        },
      });
  }

  updateKeywords(oldKeywords: string[], newKeywords: string[]): void {
    const keywords = Array.from(new Set(newKeywords.concat(oldKeywords)).values());
    if (keywords.length === 0) {
      return;
    }
    this.agid$
      .pipe(
        take(1),
        switchMap((agid) =>
          this.listingProfileService.update(
            new UpdateListingProfileRequest({
              businessId: agid,
              updateOperations: [
                new UpdateOperation({
                  richData: new RichData({
                    seoKeywords: keywords,
                  }),
                  fieldMask: new FieldMask({
                    paths: ['seo_keywords'],
                  }),
                }),
              ],
            }),
          ),
        ),
        switchMap(() => this.partnerSettings$),
      )
      .subscribe({
        error: (err) => {
          const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
          this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
        },
        next: (partnerSettings: GetPartnerSettingsResponse) => {
          this.snackBar.open(this.translate.instant('LOCAL_SEO.KEYWORDS_UPDATED'), '', {
            politeness: 'polite',
            duration: 5000,
          });
          let favoriteKeywords = this.keywordStore$$.value.favoriteKeywords;
          let syncingKeywords = this.keywordStore$$.value.syncingKeywords;
          if (!partnerSettings?.isFavoriteNewKeywordsDisabled) {
            favoriteKeywords = Array.from(new Set(newKeywords.concat(favoriteKeywords)).values());
          }
          if (!partnerSettings?.isSyncKeywordsDisabled) {
            syncingKeywords = Array.from(new Set(newKeywords.concat(syncingKeywords)).values());
          }
          this.updateKeywordStore({
            keywords: keywords,
            favoriteKeywords: favoriteKeywords,
            syncingKeywords: syncingKeywords,
          });
        },
      });
  }

  updateSyncingKeywords(syncingKeywords: string[]): void {
    syncingKeywords = syncingKeywords.filter((keyword) => !!keyword);
    this.agid$
      .pipe(
        take(1),
        switchMap((agid) =>
          this.listingProfileService.update(
            new UpdateListingProfileRequest({
              businessId: agid,
              updateOperations: [
                new UpdateOperation({
                  richData: new RichData({
                    syncingSeoKeywords: syncingKeywords,
                  }),
                  fieldMask: new FieldMask({
                    paths: ['syncing_seo_keywords'],
                  }),
                }),
              ],
            }),
          ),
        ),
      )
      .subscribe({
        error: (err) => {
          const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
          this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
        },
        next: () => {
          this.snackBar.open(this.translate.instant('LOCAL_SEO.SYNCING_KEYWORDS_UPDATED'), '', {
            politeness: 'polite',
            duration: 5000,
          });
          this.updateKeywordStore({ syncingKeywords: syncingKeywords });
        },
      });
  }

  deleteKeyword(keyword: string): void {
    this.agid$
      .pipe(
        take(1),
        switchMap((agid: string) => {
          const filteredKeywords = this.keywordStore$$.value.keywords.filter((k) => k !== keyword);
          return this.listingProfileService.update(
            new UpdateListingProfileRequest({
              businessId: agid,
              updateOperations: [
                new UpdateOperation({
                  richData: new RichData({
                    seoKeywords: filteredKeywords,
                  }),
                  fieldMask: new FieldMask({
                    paths: ['seo_keywords'],
                  }),
                }),
              ],
            }),
          );
        }),
      )
      .subscribe({
        error: (err) => {
          const message = err.error?.message ? err.error.message : this.translate.instant('LOCAL_SEO.UNKNOWN_ERROR');
          this.snackBar.open(message, '', { politeness: 'assertive', duration: 5000 });
        },
        next: () => {
          this.snackBar.open(this.translate.instant('LOCAL_SEO.KEYWORD_DELETED'), '', {
            politeness: 'polite',
            duration: 5000,
          });
          this.fetchBusinessData();
        },
      });
  }

  updateDateRange(dateRange: DateRange): void {
    this.dateRange$$.next(this.applyDateRangeRules(dateRange));
  }

  applyDateRangeRules(dateRange: DateRange): DateRange {
    let { start, end } = dateRange;

    const daysWindow = 4;

    if (end) {
      const today = new Date();
      const windowStart = new Date();
      windowStart.setDate(today.getDate() - (daysWindow - 1)); // e.g., 4-day window

      const isEndInWindow = end >= windowStart && end <= today;

      if (isEndInWindow) {
        let saturdayDate: Date | null = null;

        for (let i = 0; i < daysWindow; i++) {
          const checkDate = new Date(today);
          checkDate.setDate(today.getDate() - i);
          if (checkDate.getDay() === 6) {
            // Saturday
            saturdayDate = checkDate;
            break;
          }
        }

        if (saturdayDate) {
          const timeDiff = end.getTime() - saturdayDate.getTime();
          const daysToShift = Math.floor(timeDiff / (1000 * 60 * 60 * 24)) + 1;

          // Only shift if end is after Saturday (positive shift)
          if (daysToShift > 0) {
            const adjustedEnd = new Date(end);
            adjustedEnd.setDate(end.getDate() - daysToShift);
            end = adjustedEnd;

            if (start) {
              const adjustedStart = new Date(start);
              adjustedStart.setDate(start.getDate() - daysToShift);
              start = adjustedStart;
            }
          }
        }
      }
    }
    return { start, end };
  }

  setCanEditKeywords(value: boolean): void {
    this.canEditKeywords$$.next(value);
  }

  setLocalStorage(key: string, value: string): void {
    this.agid$
      .pipe(
        map((agid) => {
          localStorage.setItem(`${agid}-${key}`, value);
          return EMPTY;
        }),
      )
      .subscribe();
  }

  getLocalStorage(key: string): Observable<string | null> {
    return this.agid$.pipe(
      map((agid) => {
        return localStorage.getItem(`${agid}-${key}`);
      }),
    );
  }
}

function formatLocalSearchSEOData(data: LocalSearchData[]): LocalSearchData[] {
  return (
    data?.map((localSearchData) => {
      const mainIndex = localSearchData.results.findIndex((result) => result.isMainBusiness);
      if (mainIndex > 2) {
        const mainBusiness = localSearchData.results[mainIndex];
        localSearchData.results = localSearchData.results.slice(0, 3);
        localSearchData.results.push(mainBusiness);
      } else {
        localSearchData.results = localSearchData.results.slice(0, 3);
      }
      return localSearchData;
    }) || []
  );
}

function ParseRowsFromQueryMetricsResult(resp: MetricResult[], metricIndex: number): SEODataPoint[] {
  return resp.reduce((arr: SEODataPoint[], metric) => {
    if (metric?.results?.metrics) {
      metric.results.metrics.forEach((nestedMetric) => {
        if (nestedMetric?.measures) {
          arr.push({
            value: nestedMetric.measures[metricIndex],
            date: metric.dimension,
          });
        }
      });
    }
    return arr;
  }, []);
}

function generateSEODataQueryMetricsRequest(
  partnerId: string,
  businessId: string,
  keyword: string,
  startDate: Date,
  endDate: Date,
): QueryMetricsRequest {
  const measures = [
    new Measure({
      aggregate: new MeasureAggregate({
        measure: 'local_rank',
        aggOp: MeasureAggregateOperator.MAX,
      }),
    }),
    new Measure({
      aggregate: new MeasureAggregate({
        measure: 'organic_rank',
        aggOp: MeasureAggregateOperator.MAX,
      }),
    }),
  ];

  const alignmentPeriod = getAlignmentPeriodForDayJSRange(dayjs(startDate), dayjs(endDate));

  return new QueryMetricsRequest({
    metricName: 'seo_data',
    partnerId: partnerId,
    resourceIds: [
      new ResourceId({
        businessId: new BusinessResourceId({ businessId: businessId }),
      }),
    ],
    filter: new Filter({
      fieldFilter: new FieldFilter({
        dimension: 'keyword',
        operator: FieldFilterOperator.EQUAL,
        value: {
          value: keyword,
          valueType: PropertyType.PROPERTY_TYPE_STRING,
        },
      }),
    }),
    dateRange: {
      start: startDate,
      end: endDate,
    },
    measures: measures,
    groupBy: new GroupBy({
      dimension: [
        {
          dimension: 'date',
        },
        {
          limitDimension: {
            dimension: 'version',
            order: Order.ORDER_DESC,
            limit: 1,
          },
        },
      ],
    }),
    alignment: Alignment.ALIGN_DELTA,
    alignmentPeriod: new AlignmentPeriod({
      calendar: alignmentPeriod,
    }),
  });
}

export function getAlignmentPeriodForDayJSRange(start: dayjs.Dayjs, end: dayjs.Dayjs): AlignmentPeriodCalendar {
  const numMonths = end.diff(start, 'months');
  const numDays = end.diff(start, 'days');
  if (numMonths >= 5) {
    // set the alignment period to monthly if the range is 6 months or more
    return AlignmentPeriodCalendar.CALENDAR_MONTH;
  } else if (numDays >= 89) {
    // Use weekly alignment for 90 days or more
    return AlignmentPeriodCalendar.CALENDAR_WEEK;
  } else {
    return AlignmentPeriodCalendar.CALENDAR_DAY;
  }
}
