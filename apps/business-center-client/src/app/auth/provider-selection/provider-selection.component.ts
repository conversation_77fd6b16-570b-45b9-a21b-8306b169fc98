import { Component, inject } from '@angular/core';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Router, RouterModule } from '@angular/router';
import { GreyLabelService } from '../grey-label.service';
import { GalaxyAvatarModule } from '@vendasta/galaxy/avatar';

@Component({
  selector: 'app-provider-selection',
  templateUrl: './provider-selection.component.html',
  styleUrls: ['./provider-selection.component.scss'],
  imports: [GalaxyPageModule, RouterModule, GalaxyAvatarModule],
})
export default class ConfirmationCodeComponent {
  private readonly router = inject(Router);
  private readonly greyLabelService = inject(GreyLabelService);

  namespaceDetails = this.greyLabelService.namespaceDetails;

  selectProvider(providerId: string): void {
    this.greyLabelService.navigateToProvider(providerId, true);
  }
}
