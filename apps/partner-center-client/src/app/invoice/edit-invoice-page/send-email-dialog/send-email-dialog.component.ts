import { Component, ElementRef, Inject, signal, ViewChild } from '@angular/core';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MatChipGrid } from '@angular/material/chips';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Recipient } from '@galaxy/partner-center-client/billing/static/core/recipients';

export interface SendEmailDialogData {
  recipient: Recipient;
  additionalRecipients: Recipient[];
  allRecipients: Recipient[];
  merchantId: string;
  invoiceId: string;
}

export interface SendEmailDialogResp {
  emailAddresses: string[];
}

@Component({
  templateUrl: './send-email-dialog.component.html',
  styleUrls: ['./send-email-dialog.component.scss'],
  standalone: false,
})
export class SendEmailDialogComponent {
  recipient: Recipient;
  additionalRecipients: Recipient[];
  allRecipients: Recipient[];
  errorMessage = signal<string>('');
  emailAddresses = signal<string[]>([]);
  merchantId: string;
  invoiceId: string;
  loadingInvoice = signal<boolean>(true);

  @ViewChild('emailInput') emailInput: ElementRef<HTMLInputElement>;
  @ViewChild('autoEmail') matAutocomplete: MatAutocomplete;
  @ViewChild('emailList') emailChipList: MatChipGrid;

  constructor(
    public dialogRef: MatDialogRef<SendEmailDialogComponent, SendEmailDialogResp>,
    @Inject(MAT_DIALOG_DATA) public data: SendEmailDialogData,
  ) {
    this.recipient = data?.recipient;
    this.additionalRecipients = data?.additionalRecipients;
    this.allRecipients = data?.allRecipients;
    this.emailAddresses.set(this.populateEmailAddresses(data));
    this.merchantId = data?.merchantId;
    this.invoiceId = data?.invoiceId;
  }

  populateEmailAddresses(data: SendEmailDialogData): string[] {
    const emailAddresses: string[] = [];
    if (data?.recipient?.email) {
      emailAddresses.push(data.recipient.email);
    }
    if (data?.additionalRecipients) {
      data.additionalRecipients.map((recipient) => {
        if (recipient.email) {
          emailAddresses.push(recipient.email);
        }
      });
    }
    return emailAddresses;
  }

  close(save: boolean): void {
    save ? this.dialogRef.close({ emailAddresses: this.emailAddresses() }) : this.dialogRef.close();
  }
}
