import { DOCUMENT } from '@angular/common';
import { Component, Inject, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { Title } from '@angular/platform-browser';
import { Capacitor } from '@capacitor/core';
import { AtlasDataService } from '@galaxy/atlas/core';
import { EnvironmentService } from '@galaxy/core';
import { AtlasThemingService } from '@galaxy/atlas';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import dayjs from 'dayjs';
import { filter, map } from 'rxjs';
import { devServer, partnerId } from '../globals';
import { AppConfigService } from './app-config.service';
import { LOCALES, LanguageService } from './core/language-service.service';
import { LocationsService, isAccountGroup, isBrand } from './locations/locations.service';

@Component({
  selector: 'bc-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: false,
})
export class AppComponent {
  globalNotificationMessage = toSignal(this.appConfig.legacyConfig$.pipe(map((config) => config.globalNotification)));

  private readonly atlasDataService = inject(AtlasDataService);
  private readonly atlasThemeService = inject(AtlasThemingService);

  constructor(
    public readonly appConfig: AppConfigService,
    @Inject(DOCUMENT) private _document: Document,
    private environmentService: EnvironmentService,
    private analytics: ProductAnalyticsService,
    private titleService: Title,
    private languageService: LanguageService,
    private locationsService: LocationsService,
  ) {
    this.appConfig.branding$
      .pipe(
        map((branding) => branding?.faviconUrl),
        takeUntilDestroyed(),
      )
      .subscribe((favicon) => {
        this._document.getElementById('app-favicon').setAttribute('href', favicon);
      });
    this.appConfig.businessAppName$.pipe(takeUntilDestroyed()).subscribe((name) => {
      this.titleService.setTitle(name);
    });

    this.languageService.currentLocale$.pipe(takeUntilDestroyed()).subscribe({
      next: (locale) => this.updateLocale(locale),
    });

    if (!devServer) {
      this.analytics.initialize({
        environment: this.environmentService.getEnvironment(),
        projectUUID: '0429bf45-19a6-46d8-b677-2eff456d5230',
        postHogID: 'Z83JPt21DKL7mnkx_x8ZNYnD8L9eTHy3Clyv_-Z1djY',
        projectName: 'business-center-client',
        partner: {
          pid: partnerId,
        },
        platform: Capacitor.getPlatform(),
      });
      this.locationsService.currentLocation$
        .pipe(
          map((loc) => (isAccountGroup(loc) ? loc.accountGroupId : isBrand(loc) ? loc.path : null)),
          filter((v) => !!v),
          takeUntilDestroyed(),
        )
        .subscribe({
          next: (businessId) => this.analytics.trackProperties({ businessId: businessId }),
        });
    }

    this.atlasDataService.theming$.pipe(takeUntilDestroyed()).subscribe((theming) => {
      this.atlasThemeService.theming = theming;
    });
  }

  private updateLocale(locale: LOCALES): void {
    switch (locale) {
      case LOCALES.EN: {
        import('dayjs/locale/en');
        dayjs.locale('en');
        break;
      }
      case LOCALES.FR: {
        import('dayjs/locale/fr-ca');
        dayjs.locale('fr-ca');
        break;
      }
      case LOCALES.FR_FR: {
        import('dayjs/locale/fr');
        dayjs.locale('fr');
        break;
      }
      case LOCALES.FR_CA: {
        import('dayjs/locale/fr-ca');
        dayjs.locale('fr-ca');
        break;
      }
      case LOCALES.CS: {
        import('dayjs/locale/cs');
        dayjs.locale('cs');
        break;
      }
      case LOCALES.PT: {
        import('dayjs/locale/pt-br');
        dayjs.locale('pt-br');
        break;
      }
      case LOCALES.NL: {
        import('dayjs/locale/nl');
        dayjs.locale('nl');
        break;
      }
      case LOCALES.DE: {
        import('dayjs/locale/de');
        dayjs.locale('de');
        break;
      }
      case LOCALES.ES_419: {
        import('dayjs/locale/es-us');
        dayjs.locale('es-us');
        break;
      }
    }
  }
}
