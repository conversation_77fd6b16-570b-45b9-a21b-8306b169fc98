import { Injectable, NgZone, inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';
import { Capacitor } from '@capacitor/core';
import { PushNotifications } from '@capacitor/push-notifications';
import { firstValueFrom } from 'rxjs';
import { REGISTRATION_CACHE_KEY } from './constants';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { NotificationsService } from '@vendasta/notifications-sdk';
import { StorageBackend } from '@openid/appauth';
import { Device } from '@capacitor/device';
import { TokenInfo } from './core/mobile';
import { partnerId } from '../globals';
import { initializeApp } from '@angular/fire/app';
import { firebaseConfig, getPWAToken } from '@vendasta/shared';
import { getMessaging, onMessage } from 'firebase/messaging';
import { AngularFireMessaging } from '@angular/fire/compat/messaging';

@Injectable({
  providedIn: 'root',
})
export class AppInitializerService {
  private readonly zone = inject(NgZone);
  private readonly router = inject(Router);
  private readonly storageBackend = inject(StorageBackend);
  private readonly authService = inject(AuthService);
  private readonly notificationsService = inject(NotificationsService);
  private readonly afMessaging = inject(AngularFireMessaging);
  private readonly firebaseApp = initializeApp(firebaseConfig);

  initializeApp(): () => void {
    return () => {
      this.registerDeepLinkHandler();

      if (Capacitor.isNativePlatform()) {
        this.clearUnknownSession();
        this.registerMobilePushNotificationListeners();
      } else {
        this.registerPWAPushNotificationOnMessage();
      }
    };
  }

  private registerDeepLinkHandler(): void {
    App.addListener('appUrlOpen', (event: URLOpenListenerEvent) => {
      const getAppPathFromDomains = (url: string, domain: string[]): string => {
        const urlObj = new URL(url);
        const domainIndex = domain.findIndex((d) => urlObj.hostname.includes(d));
        if (domainIndex === -1) {
          return '';
        }
        const pathArray = url.split(domain[domainIndex]);
        return pathArray.pop();
      };

      this.zone.run(() => {
        const appPath = getAppPathFromDomains(event.url, ['login.custom-abc.com']);
        if (appPath) {
          this.router.navigateByUrl(appPath);
        }
      });
    });
  }

  private registerMobilePushNotificationListeners(): void {
    PushNotifications.addListener('registration', async (token) => {
      const deviceId = await Device.getId();
      const tokenInfo = new TokenInfo(token.value, this.authService.userId(), partnerId, deviceId.identifier);
      let prevTokenInfoJSON: string;
      try {
        prevTokenInfoJSON = await this.storageBackend.getItem(REGISTRATION_CACHE_KEY);
      } catch (e) {
        await this.registerAppToken(tokenInfo);
      }

      if (prevTokenInfoJSON) {
        const prevTokenInfo = JSON.parse(prevTokenInfoJSON);
        const infoIsDifferent = !tokenInfo.equals(prevTokenInfo);
        if (infoIsDifferent) {
          await this.registerAppToken(tokenInfo);
        }
      }
    });

    PushNotifications.addListener('registrationError', (error: any) => {
      console.error('Error on registration: ' + JSON.stringify(error));
    });

    PushNotifications.addListener('pushNotificationActionPerformed', (notification) => {
      const path = new URL(notification.notification.data.link).pathname;
      this.router.navigateByUrl(path);
    });
  }

  private registerPWAPushNotificationOnMessage(): void {
    navigator.serviceWorker?.ready.then(async (registration) => {
      let prevTokenInfoJSON: string;
      try {
        prevTokenInfoJSON = await this.storageBackend.getItem(REGISTRATION_CACHE_KEY);
      } catch (e) {
        console.log('No previous FCM registration token info found');
      }

      const currentToken = await getPWAToken(firebaseConfig.vapidKey, registration);

      if (prevTokenInfoJSON) {
        const prevTokenInfo: TokenInfo = JSON.parse(prevTokenInfoJSON);

        if (prevTokenInfo.userId != this.authService.userId()) {
          // if a new user logs in, delete the current token, token will be re-registered
          this.afMessaging.deleteToken(currentToken);
        }

        if (prevTokenInfo.registrationToken != currentToken) {
          // if the token has changed, delete the old token
          this.afMessaging.deleteToken(prevTokenInfo.registrationToken);
        }
      }

      const deviceId = await Device.getId();

      getPWAToken(firebaseConfig.vapidKey, registration)
        .then(async (token) => {
          const tokenInfo = new TokenInfo(token, this.authService.userId(), partnerId, deviceId.identifier);
          await this.registerAppToken(tokenInfo);
        })
        .catch((err) => {
          console.error('error:', err);
        });

      const messaging = getMessaging();

      onMessage(messaging, (payload) => {
        const { title, body } = payload.notification;
        const notification = new Notification(title, {
          body: body,
        });

        notification.onclick = () => {
          const link = payload.data['link'];
          const isStandalone = window.matchMedia('(display-mode: standalone)').matches;

          if (isStandalone) {
            const path = new URL(link).pathname;
            this.router.navigateByUrl(path);
          } else {
            window.open(link, '_blank');
          }
          notification.close();
        };
      });
    });
  }

  private async registerAppToken(tokenInfo: TokenInfo): Promise<void> {
    try {
      await firstValueFrom(
        this.notificationsService.registerAppToken$(
          tokenInfo.registrationToken,
          tokenInfo.userId,
          tokenInfo.partnerId,
          tokenInfo.deviceId,
        ),
      );
      await this.storageBackend.setItem(REGISTRATION_CACHE_KEY, JSON.stringify(tokenInfo));
    } catch (err) {
      console.log('Failed to register app token', err);
    }
  }

  // The users's session should be cleared if a partner id is not set, otherwise
  // they will be stuck in a state where they cannot log in again.
  private async clearUnknownSession(): Promise<void> {
    if (!Capacitor.isNativePlatform() || partnerId) {
      return;
    }
    console.info('Clearing unknown session due to missing partner ID');
    await this.storageBackend.removeItem(REGISTRATION_CACHE_KEY);
    await this.storageBackend.removeItem('token_response');
    await this.router.navigateByUrl('/landing');
  }
}
