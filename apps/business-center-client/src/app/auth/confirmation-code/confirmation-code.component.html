<glxy-page>
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [previousPageUrl]="'../landing'">Back</glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>Enter confirmation code</glxy-page-title>
  </glxy-page-toolbar>

  <div class="container">
    <div class="confirmation-code">
      <h2>Confirm your email</h2>
      <p>
        Enter the 6-digit confirmation code we sent to <strong>{{ email() }}</strong
        >. The email will come from <strong>businessapp.io</strong>.
      </p>

      <div class="code-input">
        @if (submitted()) {
          <div class="loading-spinner">
            <glxy-loading-spinner size="small" />
          </div>
        }
        <app-code-input [totalDigits]="6" (codeComplete)="onSubmit($event)" [disabled]="submitted()"></app-code-input>
      </div>
    </div>
    <div class="resend-container">
      <div class="resend-text">Didn't get an email?</div>
      <div>
        <button mat-stroked-button class="btn" (click)="resendCode()">
          <glxy-button-loading-indicator [isLoading]="resendingOTP()"
            >Resend confirmation code</glxy-button-loading-indicator
          >
        </button>
      </div>
    </div>
  </div>
</glxy-page>
