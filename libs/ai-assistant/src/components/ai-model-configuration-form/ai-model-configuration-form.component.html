@let form = aiModelConfigForm();
<form [formGroup]="form">
  <glxy-form-field>
    <glxy-label>
      {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.MODEL.SELECT.TITLE' | translate }}
    </glxy-label>
    <mat-select [formControl]="form.controls.model" [compareWith]="compareModels">
      <ng-template #modelDisplay let-model>
        {{ model.name }}
        @if (model.recommended) {
          <glxy-badge color="green" size="small">{{
            'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.MODEL.RECOMMENDED' | translate
          }}</glxy-badge>
        }
      </ng-template>
      <ng-template #defaultModelDisplay>
        {{ 'AI_ASSISTANT.SETTINGS.PROFILE_CAPABILITY.MODEL.SELECT.DEFAULT' | translate }}
        @if (getRecommendedModel()) {
          ({{ getRecommendedModel()!.name }})
        }
      </ng-template>

      <mat-select-trigger>
        @if (aiModel() !== defaultModel) {
          <ng-container *ngTemplateOutlet="modelDisplay; context: { $implicit: aiModel() }"></ng-container>
        } @else {
          <ng-container *ngTemplateOutlet="defaultModelDisplay"></ng-container>
        }
      </mat-select-trigger>

      <mat-option [value]="defaultModel">
        <ng-container *ngTemplateOutlet="defaultModelDisplay"></ng-container>
      </mat-option>
      @for (model of availableModels(); track model) {
        <mat-option [value]="model">
          <ng-container *ngTemplateOutlet="modelDisplay; context: { $implicit: model }"></ng-container>
        </mat-option>
      }
    </mat-select>
  </glxy-form-field>
</form>
