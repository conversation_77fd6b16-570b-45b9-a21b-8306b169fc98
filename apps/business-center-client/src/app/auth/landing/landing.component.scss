@use 'design-tokens' as *;

glxy-page {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.landing {
  padding: $spacing-1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  gap: $spacing-2;
}

.logo {
  max-width: 242px;
  max-height: 120px;
  margin-bottom: $spacing-4;
  width: 100%;
  object-fit: contain;
}

.greylabel-logo {
  width: 132px;
  height: 132px;
  margin-bottom: $spacing-4;
}

.welcome-text {
  font-size: $font-preset-2-size;
  font-weight: bold;
}

.subtext {
  color: $secondary-text-color;
  font-size: 18px;
}

.btn {
  height: 42px;
  font-size: $font-preset-3-size;
  width: 100%;
  max-width: $media--phone-minimum;
  margin-top: $spacing-4;
}

.namespace-selector {
  width: 100%;
  margin-top: $spacing-4;
  glxy-form-field {
    width: 100%;
  }
  text-align: left;
}
