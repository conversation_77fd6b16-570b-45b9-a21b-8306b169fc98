import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { ChatMessageRole, TestResult } from '@vendasta/ai-assistants';
import { CommonModule } from '@angular/common';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyUploaderModule } from '@vendasta/galaxy/uploader';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { GalaxyChatModule } from '@vendasta/galaxy/chat';
import { StatusMap } from '../test-run-table/test-run-table.component';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';

@Component({
  selector: 'app-test-results-modal',
  imports: [
    CommonModule,
    GalaxyFormFieldModule,
    GalaxyUploaderModule,
    MatButtonModule,
    MatIconModule,
    MatDialogModule,
    ReactiveFormsModule,
    MatSelectModule,
    MatInputModule,
    GalaxyChatModule,
    MatDividerModule,
    MatListModule,
  ],
  templateUrl: './test-results-modal.component.html',
  styleUrl: './test-results-modal.component.scss',
})
export class TestResultsModalComponent {
  protected data = inject(MAT_DIALOG_DATA);
  private readonly dialogRef = inject(MatDialogRef<TestResultsModalComponent, void>);
  testResult: TestResult;

  ngOnInit() {
    this.testResult = this.data;
  }

  getRole(r: ChatMessageRole): string {
    switch (r) {
      case ChatMessageRole.CHAT_MESSAGE_ROLE_ASSISTANT:
        return 'Assistant';
      case ChatMessageRole.CHAT_MESSAGE_ROLE_USER:
        return 'User';
    }
    return '';
  }

  protected readonly statusMap = StatusMap;
}
