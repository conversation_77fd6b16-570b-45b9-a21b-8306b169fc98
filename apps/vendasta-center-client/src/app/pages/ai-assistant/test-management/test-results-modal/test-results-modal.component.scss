@use 'design-tokens' as *;

.status {
  display: flex;
  align-items: center;
  gap: $spacing-1;
}

.success {
  color: $success-icon-color;
}

.failure {
  color: $error-icon-color;
}

.title {
  display: flex;
  align-items: center;
  gap: $spacing-3;
  padding: $spacing-3;
  margin: 0;
}

.mat-divider {
  margin-bottom: $spacing-3;
}

.evaluation {
  max-width: 800px;

  h3 {
    @include text-preset-3--bold;
  }
  p {
    @include text-preset-4;
  }

  display: flex;
  flex-direction: column;
  gap: $spacing-2;

  .mdc-list {
    padding: 0;
  }

  .sources {
    margin-bottom: $negative-3;
  }
}
