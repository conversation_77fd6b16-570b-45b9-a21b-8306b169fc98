import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { AiAssistantFormV2Component } from './ai-assistant-form-v2.component';
import { AiAssistantService } from '../../../core/services/ai-assistant.service';
import {
  AssistantApiService,
  AssistantType,
  ConfigurableGoal,
  GoalType,
  Model,
  ModelVendor,
  Namespace,
  PromptModule,
  UpsertAssistantRequestInterface,
  UpsertAssistantResponse,
  VendorModel,
} from '@vendasta/ai-assistants';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { PageService } from '@vendasta/galaxy/page/src/page.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  AI_DEFAULT_WORKFORCE_TOKEN,
  NAMESPACE_CONFIG_TOKEN,
  PARTNER_ID_TOKEN,
} from '../../../core/tokens';
import { AiAssistantI18nModule } from '../../../assets/i18n/ai-assistant-i18n.module';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AiAssistant, ConnectionType } from '../../../core/interfaces/ai-assistant.interface';
import { ConversationApiService } from '@vendasta/conversation';
import {
  AiAssistantForm,
  AiCapabilityForm,
  AiConnectionsForm,
  AiToolForm,
  AiToolFormArray,
  AiToolHeaderForm,
  AiToolParameterForm,
} from '../../../core/forms';
import {
  AiKnowledgeService,
  SHOW_BUSINESS_PROFILE_SOURCE_TOKEN,
  ACCOUNT_GROUP_ID_TOKEN as KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN,
  BUSINESS_PROFILE_URL_TOKEN,
  MANAGE_KNOWLEDGE_URL_TOKEN,
  MARKET_ID_TOKEN,
  PARTNER_ID_TOKEN as KNOWLEDGE_PARTNER_ID_TOKEN,
  WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN,
} from '@galaxy/ai-knowledge';
import { UnsavedChangesGuard } from '../../../core/services/unsaved-changes.guard';
import { AiAssistantFormV2Service } from './ai-assistant-form-v2.service';
import { voiceFamilies } from '../../../core/services/ai-assistant-utils';
import { SmsDetailsService } from '../../../core/services/sms-details.service';
import { WhatsappDetailsService } from '../../../core/services/whatsapp-details.service';
import { PartnerServiceInterfaceToken } from '@galaxy/partner';

jest.mock('../../../core/services/ai-assistant.service');
// jest.mock('@vendasta/ai-assistants');
jest.mock('@vendasta/galaxy/snackbar-service');
jest.mock('@vendasta/galaxy/page/src/page.service');

const ACCOUNT_GROUP_ID = 'AG-1234';
const PARTNER_ID = 'ABC';

// Create a test class that extends the component to access protected members
class TestAiAssistantFormV2Component extends AiAssistantFormV2Component {
  // Expose protected members for testing
  public getTestAssistantForm() {
    return this.assistantForm();
  }

  public testSubmit() {
    return this.submit();
  }

  public testOnImageChanged(url: string) {
    return this.onImageChanged(url);
  }

  public testBack() {
    return this.back();
  }

  public testSetActiveToolForm(data: { parentCapabilityForm?: AiCapabilityForm; form: AiToolForm }) {
    return this.activeToolForm.set(data);
  }

  public testHandleToolFormDisplay(event: { parentCapabilityForm?: any; form: any }) {
    return this.handleToolFormDisplay(event);
  }

  public testHandleToolFormSubmit() {
    return this.handleToolFormSubmit();
  }

  // Expose toolsToUpdate for testing
  public toolsToUpdate: any[] = [];
}

// Helper to get plain object from Namespace or plain object
function getNamespaceObj(ns: any) {
  if (ns && typeof ns.toApiJson === 'function') {
    return ns.toApiJson();
  }
  return ns;
}

describe('AiAssistantFormV2Component', () => {
  let component: TestAiAssistantFormV2Component;
  let aiAssistantService: jest.Mocked<AiAssistantService>;
  let assistantApiService: jest.Mocked<AssistantApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let pageService: jest.Mocked<PageService>;
  let MockAiKnowledgeService: jest.Mocked<AiKnowledgeService>;
  let mockUnsavedChangesGuard: jest.Mocked<UnsavedChangesGuard>;
  let mockSmsDetailsService: jest.Mocked<SmsDetailsService>;
  let mockWhatsappDetailsService: jest.Mocked<WhatsappDetailsService>;

  const setupTestBed = async (
    overrides: {
      aiAssistantService?: Partial<AiAssistantService>;
      assistantApiService?: Partial<AssistantApiService>;
      snackbarService?: Partial<SnackbarService>;
      conversationApiService?: Partial<ConversationApiService>;
      pageService?: Partial<PageService>;
      aiKnowledgeService?: Partial<AiKnowledgeService>;
      unsavedChangesGuard?: Partial<UnsavedChangesGuard>;
    } = {},
    initialData: {
      assistant?: any;
      connections?: any[];
      capabilities?: ConfigurableGoal[];
      promptModuleContents?: Record<string, string>;
    } = {},
  ) => {
    const mockSMSConnection = {
      connection: {
        id: 'test-sms-connection-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: '',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          },
        ],
        connectionType: 'SMS',
        connectionTypeName: 'SMS',
        supportedAssistantTypes: [2],
      },
    };

    const mockWebchatConnection = {
      connection: {
        id: 'test-webchat-connection-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: '',
        assistantKeys: [
          {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          },
        ],
        connectionType: 'WebchatWidget',
        connectionTypeName: 'Web Chat',
        supportedAssistantTypes: [2],
        isConnectionLocked: true,
      },
    };

    const mockAssistant = initialData.assistant ?? {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: initialData.capabilities ?? [
          new ConfigurableGoal({
            goal: {
              id: 'test-capability',
              name: 'Test Capability',
              description: 'Test Description',
              namespace: new Namespace({
                accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
              }),
              type: GoalType.GOAL_TYPE_CUSTOM,
              functions: [],
              promptModules: [
                new PromptModule({
                  id: 'test-prompt-module-id',
                  namespace: new Namespace({
                    accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
                  }),
                  deployedVersion: '1',
                }),
              ],
            },
            configuration: [],
          }),
        ],
      },
    };

    const mockConnections = initialData.connections ?? [mockSMSConnection, mockWebchatConnection];

    aiAssistantService = {
      buildAssistantConfigurationUrl: jest.fn().mockReturnValue('/assistant/configuration'),
      getAssistant: jest.fn().mockReturnValue(of(mockAssistant)),
      hydrateAssistantWithDefaultInfo: jest.fn().mockReturnValue(mockAssistant),
      listConnectionsForAssistant: jest.fn().mockReturnValue(of(mockConnections)),
      upsertCapability: jest.fn().mockReturnValue(Promise.resolve({})),
      upsertPromptModule: jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' })),
      getMultiPromptModuleVersions: jest.fn().mockReturnValue(of([])),
      buildAppId: jest.fn().mockReturnValue('APP-AI-123'),
      $isDeepgramEnabled: jest.fn().mockReturnValue(true),
      listAvailableModels: jest.fn().mockReturnValue(
        of([
          new Model({
            id: 'gpt-4',
            vendor: ModelVendor.VENDOR_OPENAI,
            name: 'GPT-4',
            recommended: true,
          }),
          new Model({
            id: 'gpt-3.5-turbo',
            vendor: ModelVendor.VENDOR_OPENAI,
            name: 'GPT-3.5 Turbo',
            recommended: false,
          }),
        ]),
      ),
      ...overrides.aiAssistantService,
    } as unknown as jest.Mocked<AiAssistantService>;

    assistantApiService = {
      upsertAssistant: jest.fn().mockReturnValue(
        of({
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
            config: {},
            toApiJson: () => ({}),
          },
          toApiJson: () => ({}),
        } as unknown as UpsertAssistantResponse),
      ),
      setAssistantConnections: jest.fn().mockReturnValue(of({})),
      ...overrides.assistantApiService,
    } as unknown as jest.Mocked<AssistantApiService>;

    snackbarService = {
      openErrorSnack: jest.fn(),
      openSuccessSnack: jest.fn(),
      ...overrides.snackbarService,
    } as unknown as jest.Mocked<SnackbarService>;

    conversationApiService = {
      getMultiWidget: jest.fn().mockReturnValue(
        of({
          widgets: [],
        }),
      ),
      ...overrides.conversationApiService,
    } as unknown as jest.Mocked<ConversationApiService>;

    pageService = {
      navigateToPrevious: jest.fn(),
      ...overrides.pageService,
    } as unknown as jest.Mocked<PageService>;

    MockAiKnowledgeService = {
      businessProfileKSId: jest.fn().mockReturnValue('businessProfileKSId'),
      listAllKnowledgeSourcesForApp: jest.fn().mockResolvedValue([]),
      ...overrides.aiKnowledgeService,
    } as unknown as jest.Mocked<AiKnowledgeService>;

    mockUnsavedChangesGuard = {
      notifyStateChanged: jest.fn(),
      canDeactivate: jest.fn().mockReturnValue(true),
      ...overrides.unsavedChangesGuard,
    } as unknown as jest.Mocked<UnsavedChangesGuard>;

    mockSmsDetailsService = {
      phoneNumber$: of('+***********'),
    } as unknown as jest.Mocked<SmsDetailsService>;

    mockWhatsappDetailsService = {
      phoneNumber$: of('+***********'),
    } as unknown as jest.Mocked<WhatsappDetailsService>;

    await TestBed.configureTestingModule({
      imports: [
        AiAssistantI18nModule,
        HttpClientTestingModule,
        LexiconModule.forRoot(),
        TestAiAssistantFormV2Component,
      ],
      providers: [
        FormBuilder,
        { provide: PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: AI_DEFAULT_WORKFORCE_TOKEN, useValue: of({}) },
        { provide: AiAssistantService, useValue: aiAssistantService },
        { provide: AssistantApiService, useValue: assistantApiService },
        { provide: SnackbarService, useValue: snackbarService },
        { provide: PageService, useValue: pageService },
        { provide: ConversationApiService, useValue: conversationApiService },
        { provide: UnsavedChangesGuard, useValue: mockUnsavedChangesGuard },
        { provide: SHOW_BUSINESS_PROFILE_SOURCE_TOKEN, useValue: of(true) },
        { provide: AiKnowledgeService, useValue: MockAiKnowledgeService },
        AiAssistantFormV2Service,
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of({
              get: (key: string) => (key === 'assistantId' ? 'test-assistant-id' : null),
            }),
          },
        },
        { provide: TranslateService, useValue: { setDefaultLang: jest.fn(), use: jest.fn() } },
        { provide: SmsDetailsService, useValue: mockSmsDetailsService },
        { provide: WhatsappDetailsService, useValue: mockWhatsappDetailsService },
        {
          provide: NAMESPACE_CONFIG_TOKEN,
          useValue: of({
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
          }),
        },
        { provide: KNOWLEDGE_ACCOUNT_GROUP_ID_TOKEN, useValue: of(ACCOUNT_GROUP_ID) },
        { provide: KNOWLEDGE_PARTNER_ID_TOKEN, useValue: of(PARTNER_ID) },
        { provide: MARKET_ID_TOKEN, useValue: of('default') },
        { provide: MANAGE_KNOWLEDGE_URL_TOKEN, useValue: of('some-url') },
        { provide: BUSINESS_PROFILE_URL_TOKEN, useValue: of('some-url') },
        { provide: SHOW_BUSINESS_PROFILE_SOURCE_TOKEN, useValue: of(false) },
        { provide: WEB_CHAT_WIDGET_EDIT_ROUTE_TOKEN, useValue: of(['some-url']) },
        { provide: PartnerServiceInterfaceToken, useValue: { getPartnerId: () => of(PARTNER_ID) } },
      ],
    }).compileComponents();

    const fixture = TestBed.createComponent(TestAiAssistantFormV2Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
    return { fixture };
  };

  const waitForFormToBePopulated = async (
    component: AiAssistantFormV2Component,
  ): Promise<AiAssistantForm | undefined> => {
    for (let i = 0; i < 20; i++) {
      // up to 2 seconds
      const form = component['assistantForm']();
      if (form) {
        // console.log('form populated after ', i * 100, 'ms');
        return form;
      }
      await new Promise((res) => setTimeout(res, 100));
    }
    return undefined;
  };

  it('should create', async () => {
    await setupTestBed();
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', async () => {
    const initialAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
    };
    await setupTestBed({}, { assistant: initialAssistant });
    expect(aiAssistantService.getAssistant).toHaveBeenCalledWith(PARTNER_ID, ACCOUNT_GROUP_ID, 'test-assistant-id');
    const hydrateCall = aiAssistantService.hydrateAssistantWithDefaultInfo.mock.calls[0][0] as {
      assistant: { namespace: any };
      connections: any[];
    };
    // Debug log
    // eslint-disable-next-line no-console
    // console.log(
    //   'hydrateCall:',
    //   JSON.stringify(hydrateCall, (key, value) => (typeof value === 'function' ? '[Function]' : value), 2),
    // );
    // Check assistant namespace
    if (hydrateCall.assistant?.namespace === undefined) {
      throw new Error('hydrateCall.assistant.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.assistant.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    // Check connections namespaces
    if (!hydrateCall.connections?.[0]?.connection?.namespace) {
      throw new Error('hydrateCall.connections[0].connection.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[0].connection.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    if (!hydrateCall.connections?.[1]?.connection?.namespace) {
      throw new Error('hydrateCall.connections[1].connection.namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[1].connection.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    // Check assistantKeys namespaces
    if (!hydrateCall.connections?.[0]?.connection?.assistantKeys?.[0]?.namespace) {
      throw new Error('hydrateCall.connections[0].connection.assistantKeys[0].namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[0].connection.assistantKeys[0].namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    if (!hydrateCall.connections?.[1]?.connection?.assistantKeys?.[0]?.namespace) {
      throw new Error('hydrateCall.connections[1].connection.assistantKeys[0].namespace is undefined');
    } else {
      expect(getNamespaceObj(hydrateCall.connections[1].connection.assistantKeys[0].namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }

    const form = component.getTestAssistantForm();
    expect(form?.getRawValue()).toEqual({
      id: 'test-assistant-id',
      name: 'Test Assistant',
      type: 2,
      namespace: new Namespace({
        accountGroupNamespace: {
          accountGroupId: 'AG-1234',
        },
      }),
      avatarUrl: 'test-assistant-avatar-url',
      capabilities: [],
      knowledge: [],
      model: {
        model: expect.any(Object),
      },
      voiceConfig: {
        voiceFamily: voiceFamilies[3],
        modelConfig: {
          deepgramConfig: {
            voice: 'aura-asteria-en',
          },
          openAIRealtimeConfig: {
            voice: 'alloy',
            turnDetection: {
              prefixPadding: 300,
              silenceDuration: 500,
              threshold: 0.75,
            },
          },
        },
        vendorModel: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
      },
      connections: expect.any(Array),
    });

    // Get the connections as AiConnectionsForm
    const connectionsForm = form?.get('connections') as AiConnectionsForm;
    expect(connectionsForm.controls.length).toBe(2);

    // Find connections by their metadata IDs
    const smsConnection = connectionsForm.controls.find((control) => control.metadata.id === 'test-sms-connection-id');
    const webchatConnection = connectionsForm.controls.find(
      (control) => control.metadata.id === 'test-webchat-connection-id',
    );

    // Check that connections exist
    expect(smsConnection).toBeTruthy();
    expect(webchatConnection).toBeTruthy();

    // Check connection values
    expect(smsConnection?.get('connected')?.value).toBe(true);
    expect(webchatConnection?.get('connected')?.value).toBe(true);

    // Check disabled states
    expect(smsConnection?.get('connected')?.disabled).toBeFalsy();
    expect(webchatConnection?.get('connected')?.disabled).toBeTruthy();
  });

  it('should submit the expected assistant data', async () => {
    const initialAssistant = {
      assistant: {
        id: 'test-assistant-id',
        namespace: new Namespace({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        }),
        name: 'Test Assistant',
        type: 2,
        avatarUrl: 'test-assistant-avatar-url',
        configurableGoals: [],
      },
    };
    await setupTestBed({}, { assistant: initialAssistant });
    const form = component.getTestAssistantForm();
    form?.controls['name'].setValue('Updated Assistant Name');
    form?.controls['avatarUrl'].setValue('updated-avatar-url');

    // Get the connections FormArray and set the connected value to false for the SMS connection
    const connectionsArray = form?.get('connections') as AiConnectionsForm;
    const smsConnectionForm = connectionsArray.controls.find(
      (control) => control.metadata.id === 'test-sms-connection-id',
    );
    smsConnectionForm?.get('connected')?.setValue(false);
    smsConnectionForm?.markAsDirty();

    form?.markAsDirty();

    // Debug: ensure form and connection are dirty before submit
    expect(form?.dirty).toBe(true);
    expect(smsConnectionForm?.dirty).toBe(true);

    await component.testSubmit();

    // Debug: log setAssistantConnections calls
    // eslint-disable-next-line no-console
    // console.log('setAssistantConnections calls:', assistantApiService.setAssistantConnections.mock.calls);

    expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
      assistant: {
        id: 'test-assistant-id',
        name: 'Updated Assistant Name',
        namespace: expect.any(Object),
        avatarUrl: 'updated-avatar-url',
        type: AssistantType.ASSISTANT_TYPE_INBOX,
        configurableGoals: [],
        config: {
          models: [],
        },
      },
      options: { applyDefaults: false },
    });

    // Check the actual contents of the setAssistantConnections call
    const setConnectionsCall = assistantApiService.setAssistantConnections.mock.calls[0][0];
    // Debug log
    // eslint-disable-next-line no-console
    console.log(
      'setConnectionsCall:',
      JSON.stringify(setConnectionsCall, (key, value) => (typeof value === 'function' ? '[Function]' : value), 2),
    );
    expect(setConnectionsCall.assistantKey?.id).toBe('test-assistant-id');
    if (setConnectionsCall.assistantKey?.namespace === undefined) {
      throw new Error('setConnectionsCall.assistantKey.namespace is undefined');
    } else {
      expect(getNamespaceObj(setConnectionsCall.assistantKey?.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    expect(setConnectionsCall.associationStates?.length).toBe(1);
    const assoc = setConnectionsCall.associationStates?.[0];
    expect(assoc?.connectionKey?.id).toBe('test-sms-connection-id');
    if (assoc?.connectionKey?.namespace === undefined) {
      throw new Error('assoc.connectionKey.namespace is undefined');
    } else {
      expect(getNamespaceObj(assoc?.connectionKey?.namespace)).toEqual({
        accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
      });
    }
    expect(assoc?.connectionKey?.connectionType).toBe(ConnectionType.SMS);
    expect(assoc?.isAssociated).toBe(false);

    expect(snackbarService.openSuccessSnack).toHaveBeenCalledWith('AI_ASSISTANT.SETTINGS.ASSISTANT_UPDATED');
    expect(mockUnsavedChangesGuard.notifyStateChanged).toHaveBeenCalledWith(false);
  });

  describe('onSubmit', () => {
    describe('assistant fields', () => {
      it('should call the upsertAssistant method with the correct arguments', async () => {
        const initialAssistant: AiAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: AssistantType.ASSISTANT_TYPE_INBOX,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
          },
        };
        await setupTestBed({}, { assistant: initialAssistant });
        const form = component.getTestAssistantForm();
        form?.controls['name'].setValue('Another Assistant Name');
        form?.controls['avatarUrl'].setValue('another-avatar-url');
        form?.markAsDirty();

        await component.testSubmit();

        expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith({
          assistant: {
            id: 'test-assistant-id',
            name: 'Another Assistant Name',
            namespace: expect.any(Object),
            avatarUrl: 'another-avatar-url',
            type: AssistantType.ASSISTANT_TYPE_INBOX,
            configurableGoals: [],
            config: {
              models: [],
            },
          },
          options: { applyDefaults: false },
        });

        // Additional check: verify the namespace is correct
        const callArg = assistantApiService.upsertAssistant.mock.calls[0][0];
        if (!callArg.assistant) throw new Error('assistant is undefined in upsertAssistant call');
        expect(getNamespaceObj(callArg.assistant.namespace)).toEqual({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        });
      });

      it('should call the upsertAssistant method with the correct arguments - voice assistant', async () => {
        const initialAssistant: AiAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [],
          },
        };
        await setupTestBed({}, { assistant: initialAssistant });
        const form = component.getTestAssistantForm();
        form?.controls['name'].setValue('Another Assistant Name');
        form?.controls['avatarUrl'].setValue('another-avatar-url');
        form?.markAsDirty();

        await component.testSubmit();

        const expectedUpsertAssistantData: UpsertAssistantRequestInterface = {
          assistant: {
            id: 'test-assistant-id',
            name: 'Another Assistant Name',
            namespace: expect.any(Object),
            avatarUrl: 'another-avatar-url',
            type: AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST,
            configurableGoals: [],
            config: {
              voiceConfig: {
                vendorModel: VendorModel.VENDOR_MODEL_OPEN_AI_REALTIME,
                modelConfig: {
                  openaiRealtimeConfig: {
                    voice: 'alloy',
                    turnDetection: {
                      prefixPadding: 300,
                      silenceDuration: 500,
                      threshold: 0.75,
                    },
                  },
                },
              },
            },
          },
          options: { applyDefaults: false },
        };
        expect(assistantApiService.upsertAssistant).toHaveBeenCalledWith(expectedUpsertAssistantData);

        // Additional check: verify the namespace is correct
        const callArg = assistantApiService.upsertAssistant.mock.calls[0][0];
        if (!callArg.assistant) throw new Error('assistant is undefined in upsertAssistant call');
        expect(getNamespaceObj(callArg.assistant.namespace)).toEqual({
          accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
        });
      });
    });

    describe('tools', () => {
      it('should call upsertFunction for each tool that is dirty', async () => {
        await setupTestBed();
        // Create mock tools
        const mockTool1 = new AiToolForm({
          func: {
            id: 'tool1',
            description: 'Test Tool 1',
            methodType: 'GET',
            url: 'https://test1.com',
            generatesAnswer: false,
          },
        });
        const mockTool2 = new AiToolForm({
          func: {
            id: 'tool2',
            description: 'Test Tool 2',
            methodType: 'POST',
            url: 'https://test2.com',
            generatesAnswer: true,
          },
        });
        const mockTool3 = new AiToolForm({
          func: {
            id: 'tool3',
            description: 'Test Tool 3',
            methodType: 'PUT',
            url: 'https://test3.com',
            generatesAnswer: false,
          },
        });

        // Mark some tools as dirty by modifying their values
        mockTool1.controls.description.setValue('Updated Test Tool 1');
        mockTool3.controls.description.setValue('Updated Test Tool 3');

        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };

        // Create a capability form with the tools
        const capabilityForm = new AiCapabilityForm({
          goal: {
            id: 'test-capability',
            name: 'Test Capability',
            description: 'Test Description',
            namespace: namespace,
            type: GoalType.GOAL_TYPE_CUSTOM,
            functions: [],
          },
          configuration: [],
        });

        // Add tools to capability form
        capabilityForm.controls.tools.push(mockTool1);
        capabilityForm.controls.tools.push(mockTool2);
        capabilityForm.controls.tools.push(mockTool3);

        // Add the capability form to the assistant form
        (component as any).assistantForm()?.controls.capabilities.push(capabilityForm);

        // Mock the aiAssistantService
        const mockUpsertFunction = jest.fn();
        aiAssistantService.upsertFunction = mockUpsertFunction;

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was called only for dirty tools
        expect(mockUpsertFunction).toHaveBeenCalledTimes(2);
        expect(mockUpsertFunction).toHaveBeenCalledWith(mockTool1.toFunction(namespace));
        expect(mockUpsertFunction).toHaveBeenCalledWith(mockTool3.toFunction(namespace));
        expect(mockUpsertFunction).not.toHaveBeenCalledWith(mockTool2.toFunction(namespace));
      });

      it('should not call upsertFunction when tools exist but are not dirty', async () => {
        await setupTestBed();
        // Create mock tools (none will be dirty)
        const mockTool1 = new AiToolForm({
          func: {
            id: 'tool1',
            description: 'Test Tool 1',
            methodType: 'GET',
            url: 'https://test1.com',
            generatesAnswer: false,
          },
        });
        const mockTool2 = new AiToolForm({
          func: {
            id: 'tool2',
            description: 'Test Tool 2',
            methodType: 'POST',
            url: 'https://test2.com',
            generatesAnswer: true,
          },
        });
        const mockTool3 = new AiToolForm({
          func: {
            id: 'tool3',
            description: 'Test Tool 3',
            methodType: 'PUT',
            url: 'https://test3.com',
            generatesAnswer: false,
          },
        });

        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };

        // Create a capability form with the tools
        const capabilityForm = new AiCapabilityForm({
          goal: {
            id: 'test-capability',
            name: 'Test Capability',
            description: 'Test Description',
            namespace: namespace,
            type: GoalType.GOAL_TYPE_CUSTOM,
            functions: [],
          },
          configuration: [],
        });
        capabilityForm.controls.tools.push(mockTool1);
        capabilityForm.controls.tools.push(mockTool2);
        capabilityForm.controls.tools.push(mockTool3);

        // Add the capability form to the assistant form
        (component as any).assistantForm()?.controls.capabilities.push(capabilityForm);

        // Mock the aiAssistantService
        const mockUpsertFunction = jest.fn();
        aiAssistantService.upsertFunction = mockUpsertFunction;

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertFunction was not called
        expect(mockUpsertFunction).not.toHaveBeenCalled();
      });
    });

    describe('prompt modules', () => {
      it('upsertPromptModule should be called for each prompt that is modified', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt3',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
          prompt3: 'Original Prompt 3',
        };
        const { fixture } = await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                  prompt3: 'Original Prompt 3',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();
        fixture.detectChanges();
        fixture.whenStable();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;

        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Update the prompt modules to simulate user changes
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls[1];
        const prompt3 = promptModules.controls[2];

        prompt1.controls.instructions.setValue('Updated Prompt 1'); // should be dirty
        prompt2.controls.instructions.setValue('Original Prompt 2'); // should NOT be dirty
        prompt3.controls.instructions.setValue('Updated Prompt 3'); // should be dirty

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertPromptModule was called only for dirty prompt modules
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(2);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: 'prompt1',
            name: 'Test Capability',
            namespace: namespace,
          },
          'Updated Prompt 1',
        );
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: 'prompt3',
            name: 'Test Capability',
            namespace: namespace,
          },
          'Updated Prompt 3',
        );
        expect(mockUpsertPromptModule).not.toHaveBeenCalledWith(
          expect.objectContaining({ id: 'prompt2' }),
          expect.any(String),
        );

        // Verify the form state after submission
        expect(promptModules.at(0)?.controls.instructions.dirty).toBe(false);
        expect(promptModules.at(1)?.controls.instructions.dirty).toBe(false);
        expect(promptModules.at(2)?.controls.instructions.dirty).toBe(false);
      });

      it('upsertPromptModule should not be called when no prompt modules have been modified', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents2 = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
        };
        await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents: promptModuleContents2 },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;

        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Set value, but it matches the original contents (no modifications)
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls[1];
        prompt1.controls.instructions.setValue('Original Prompt 1');
        prompt2.controls.instructions.setValue('Original Prompt 2');

        // Call testSubmit
        await component.testSubmit();

        // Verify upsertPromptModule was not called
        expect(mockUpsertPromptModule).not.toHaveBeenCalled();

        // Verify the form state after submission
        expect(promptModules.at(0)?.controls.instructions.dirty).toBe(false);
        expect(promptModules.at(1)?.controls.instructions.dirty).toBe(false);
      });

      it('upsertPromptModule should not be called for the same changes twice if submit is clicked twice', async () => {
        // Prepare prompt modules in the initial assistant state
        const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
        const initialAssistant = {
          assistant: {
            id: 'test-assistant-id',
            namespace: new Namespace({
              accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
            }),
            name: 'Test Assistant',
            type: 2,
            avatarUrl: 'test-assistant-avatar-url',
            configurableGoals: [
              new ConfigurableGoal({
                goal: {
                  id: 'test-capability',
                  name: 'Test Capability',
                  description: 'Test Description',
                  namespace: namespace,
                  type: GoalType.GOAL_TYPE_CUSTOM,
                  functions: [],
                  promptModules: [
                    new PromptModule({
                      id: 'prompt1',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                    new PromptModule({
                      id: 'prompt2',
                      namespace: namespace,
                      deployedVersion: '1',
                    }),
                  ],
                },
                configuration: [],
              }),
            ],
          },
        };

        // Set up the mock before component initialization
        const mockUpsertPromptModule = jest.fn().mockReturnValue(Promise.resolve({ id: 'test-prompt-module-id' }));
        const promptModuleContents = {
          prompt1: 'Original Prompt 1',
          prompt2: 'Original Prompt 2',
        };
        await setupTestBed(
          {
            aiAssistantService: {
              upsertPromptModule: mockUpsertPromptModule,
              getMultiPromptModuleVersions: jest.fn().mockReturnValue(
                Promise.resolve({
                  prompt1: 'Original Prompt 1',
                  prompt2: 'Original Prompt 2',
                }),
              ),
            },
          },
          { assistant: initialAssistant, promptModuleContents },
        );

        // Wait for the assistantForm signal to be populated
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const form = (await waitForFormToBePopulated(component))!;
        expect(form).toBeTruthy();

        const capabilityForm = form.controls.capabilities.at(0);
        const promptModules = capabilityForm.controls.promptModules;
        if (!promptModules) {
          throw new Error('Prompt modules array is undefined');
        }

        // Update the prompt modules to simulate user changes
        const prompt1 = promptModules.controls[0];
        const prompt2 = promptModules.controls[1];
        prompt1.controls.instructions.setValue('Updated Prompt 1'); // should be dirty
        prompt2.controls.instructions.setValue('Original Prompt 2'); // should NOT be dirty

        // First submit: should call upsertPromptModule for prompt1 only
        await component.testSubmit();
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
        expect(mockUpsertPromptModule).toHaveBeenCalledWith(
          {
            id: 'prompt1',
            name: 'Test Capability',
            namespace: namespace,
          },
          'Updated Prompt 1',
        );
        expect(mockUpsertPromptModule).not.toHaveBeenCalledWith(
          expect.objectContaining({ id: 'prompt2' }),
          expect.any(String),
        );

        // Second submit: should NOT call upsertPromptModule again
        await component.testSubmit();
        expect(mockUpsertPromptModule).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('when adding and editing tools', () => {
    const setupToolTestBed = async () => {
      const namespace = { accountGroupNamespace: { accountGroupId: 'AG-1234' } };
      const initialAssistant = {
        assistant: {
          id: 'test-assistant-id',
          namespace: new Namespace({
            accountGroupNamespace: { accountGroupId: ACCOUNT_GROUP_ID },
          }),
          name: 'Test Assistant',
          type: 2,
          avatarUrl: 'test-assistant-avatar-url',
          configurableGoals: [
            new ConfigurableGoal({
              goal: {
                id: 'test-capability',
                name: 'Test Capability',
                description: 'Test Description',
                namespace: namespace,
                type: GoalType.GOAL_TYPE_CUSTOM,
                functions: [],
                promptModules: [],
              },
              configuration: [],
            }),
          ],
        },
      };
      await setupTestBed({}, { assistant: initialAssistant });
      const form = component.getTestAssistantForm();
      expect(form).toBeTruthy();
      const capabilityForm = form!.controls.capabilities.at(0);
      const toolsArray = capabilityForm.controls.tools;
      return { form, capabilityForm, toolsArray, namespace, initialAssistant };
    };

    interface TestCase {
      name: string;
      setup: (
        component: TestAiAssistantFormV2Component,
        capabilityForm: AiCapabilityForm,
      ) => Promise<{
        toolForm: AiToolForm;
        updatedToolForm?: AiToolForm;
      }>;
      checkExpectations: (
        toolsArray: AiToolFormArray,
        toolData: { toolForm: AiToolForm; updatedToolForm?: AiToolForm },
      ) => void;
    }

    const testCases: TestCase[] = [
      {
        name: 'should be able to add a new tool to a capability',
        setup: async (component, capabilityForm) => {
          const toolForm = new AiToolForm({
            func: {
              id: '',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: true, localId: 'test-tool-id' },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();
          return { toolForm };
        },
        checkExpectations: (toolsArray, { toolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          expect(toolsArray.controls[0].metadata.localId).toEqual(toolForm.metadata.localId);
          expect(toolsArray.controls[0].controls.url.value).toEqual(toolForm.controls.url.value);
          expect(toolsArray.controls[0].controls.description.value).toEqual(toolForm.controls.description.value);
        },
      },
      {
        name: 'should update form values when editing a new tool',
        setup: async (component, capabilityForm) => {
          // First create a new tool
          const toolForm = new AiToolForm({
            func: {
              id: '',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: true, localId: 'test-tool-id' },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();

          // Then edit the same tool with new headers and parameters
          const updatedToolForm = toolForm.clone();
          updatedToolForm.controls.url.setValue('https://example.com/updated');
          updatedToolForm.controls.description.setValue('Updated Tool Description');
          updatedToolForm.controls.method.setValue('POST');
          updatedToolForm.controls.headers.clear();
          updatedToolForm.controls.headers.push(new AiToolHeaderForm({ key: 'Authorization', value: 'Bearer token' }));
          updatedToolForm.controls.parameters.clear();
          updatedToolForm.controls.parameters.push(new AiToolParameterForm({ name: 'param1', type: 'string' }));
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: updatedToolForm });
          component.testHandleToolFormSubmit();

          return { toolForm, updatedToolForm };
        },
        checkExpectations: (toolsArray, { updatedToolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          const toolControl = toolsArray.controls[0];
          expect(toolControl.metadata.localId).toEqual(updatedToolForm?.metadata.localId);
          expect(toolControl.controls.url.value).toEqual(updatedToolForm?.controls.url.value);
          expect(toolControl.controls.description.value).toEqual(updatedToolForm?.controls.description.value);
          expect(toolControl.controls.method.value).toEqual(updatedToolForm?.controls.method.value);
          expect(toolControl.controls.headers.length).toEqual(updatedToolForm?.controls.headers.length);
          expect(toolControl.controls.headers.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.headers.at(0)?.controls.value.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.value.value,
          );
          expect(toolControl.controls.parameters.length).toEqual(updatedToolForm?.controls.parameters.length);
          expect(toolControl.controls.parameters.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.parameters.at(0)?.controls.type.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.type.value,
          );
        },
      },
      {
        name: 'should be able to add a pre-existing tool to a capability',
        setup: async (component, capabilityForm) => {
          const toolForm = new AiToolForm({
            func: {
              id: 'existing-tool-id',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
            metadata: { isNew: false },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();
          return { toolForm };
        },
        checkExpectations: (toolsArray, { toolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          expect(toolsArray.controls[0].metadata.localId).toEqual(toolForm.metadata.localId);
          expect(toolsArray.controls[0].controls.url.value).toEqual(toolForm.controls.url.value);
          expect(toolsArray.controls[0].controls.description.value).toEqual(toolForm.controls.description.value);
        },
      },
      {
        name: 'should update form values when editing a pre-existing tool',
        setup: async (component, capabilityForm) => {
          // First create a new tool
          const toolForm = new AiToolForm({
            func: {
              id: 'existing-tool',
              description: 'Test Tool',
              methodType: 'GET',
              url: 'https://example.com',
              generatesAnswer: false,
            },
          });
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: toolForm });
          component.testHandleToolFormSubmit();

          // Then edit the same tool with new headers and parameters
          const updatedToolForm = toolForm.clone();
          updatedToolForm.controls.url.setValue('https://example.com/updated');
          updatedToolForm.controls.description.setValue('Updated Tool Description');
          updatedToolForm.controls.method.setValue('POST');
          updatedToolForm.controls.headers.clear();
          updatedToolForm.controls.headers.push(new AiToolHeaderForm({ key: 'Authorization', value: 'Bearer token' }));
          updatedToolForm.controls.parameters.clear();
          updatedToolForm.controls.parameters.push(new AiToolParameterForm({ name: 'param1', type: 'string' }));
          component.testHandleToolFormDisplay({ parentCapabilityForm: capabilityForm, form: updatedToolForm });
          component.testHandleToolFormSubmit();

          return { toolForm, updatedToolForm };
        },
        checkExpectations: (toolsArray, { updatedToolForm }) => {
          expect(toolsArray.controls.length).toBe(1);
          const toolControl = toolsArray.controls[0];
          expect(toolControl.controls.url.value).toEqual(updatedToolForm?.controls.url.value);
          expect(toolControl.controls.description.value).toEqual(updatedToolForm?.controls.description.value);
          expect(toolControl.controls.method.value).toEqual(updatedToolForm?.controls.method.value);
          expect(toolControl.controls.headers.length).toEqual(updatedToolForm?.controls.headers.length);
          expect(toolControl.controls.headers.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.headers.at(0)?.controls.value.value).toEqual(
            updatedToolForm?.controls.headers.at(0)?.controls.value.value,
          );
          expect(toolControl.controls.parameters.length).toEqual(updatedToolForm?.controls.parameters.length);
          expect(toolControl.controls.parameters.at(0)?.controls.name.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.name.value,
          );
          expect(toolControl.controls.parameters.at(0)?.controls.type.value).toEqual(
            updatedToolForm?.controls.parameters.at(0)?.controls.type.value,
          );
        },
      },
    ];

    testCases.forEach((testCase) => {
      it(testCase.name, async () => {
        const { capabilityForm, toolsArray } = await setupToolTestBed();
        const toolData = await testCase.setup(component, capabilityForm);
        testCase.checkExpectations(toolsArray, toolData);
      });
    });
  });

  it('should update the form when the image changes', async () => {
    await setupTestBed();
    component.testOnImageChanged('new-image-url');
    const form = component.getTestAssistantForm();
    expect(form?.get('avatarUrl')?.value).toBe('new-image-url');
    expect(form?.get('avatarUrl')?.dirty).toBe(true);
  });

  it('should navigate back when back() is called', async () => {
    await setupTestBed();
    component.testBack();
    expect(pageService.navigateToPrevious).toHaveBeenCalled();
  });
});
