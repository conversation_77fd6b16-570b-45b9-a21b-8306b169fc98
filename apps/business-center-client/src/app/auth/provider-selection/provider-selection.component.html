<glxy-page [disableShadow]="true">
  <glxy-page-toolbar>
    <glxy-page-nav>
      <glxy-page-nav-button [previousPageUrl]="'../landing'">Back</glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>Select your provider</glxy-page-title>
  </glxy-page-toolbar>

  <div class="container">
    <div class="title">Which provider would you like to continue with?</div>
    <ul>
      @for (namespace of namespaceDetails(); track $index) {
        <li>
          <button class="provider" (click)="selectProvider(namespace.namespace)">
            <glxy-avatar [src]="namespace.logoUrl" width="40" shape="square"> </glxy-avatar>
            <div class="provider-title">{{ namespace.name }}</div>
          </button>
        </li>
      }
    </ul>
  </div>
</glxy-page>
