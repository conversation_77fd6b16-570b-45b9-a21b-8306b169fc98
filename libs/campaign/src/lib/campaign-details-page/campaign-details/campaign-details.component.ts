import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import { Component, inject, Inject, OnDestroy, OnInit, Optional } from '@angular/core';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { DefaultCardAction, Template } from '@galaxy/email-ui/email-library/src/email-library-list/card/card.component';
import { FeatureFlagService } from '@galaxy/partner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  ActivationService,
  CampaignStatsService,
  EmailTemplateService,
  Feature,
  Focuses,
  GetterCampaignDataInterface,
  SenderInterface,
  Statuses,
  TemplateReferenceInterface,
} from '@vendasta/campaigns';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Breadcrumbs, DateRangeSelectorService, I18NDescriptor, UIKitModule } from '@vendasta/uikit';
import moment from 'moment';
import { BehaviorSubject, combineLatest, EMPTY, firstValueFrom, Observable, of, Subscription } from 'rxjs';
import { debounceTime, map, skipWhile, switchMap, take, tap, withLatestFrom } from 'rxjs/operators';
import { ConfirmationDialogComponent } from '../../campaign-list-page/column-components/dialog/confirmation-dialog.component';
import { PageComponent } from '@galaxy/email-ui/email-library/src/email-library-list/page/page.component';
import { Context } from '../../campaign-email-builder/page/shared/context';
import { EmailTemplateSaver, SaveResult } from '../../campaign-email-builder/page/shared/email-template-saver';
import { CampaignFocus, CampaignStatus, CampaignStepInterface, TemplateData } from './interface';
import { MarketingAutomationService } from './marketing-automation.service';

import { MatButtonModule } from '@angular/material/button';
import { MatOptionModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { AccountGroupService } from '@galaxy/account-group';
import { SENDER_ID_TOKEN } from '@galaxy/email-ui/email-activity/src/dependencies';
import { EmailContentData } from '@galaxy/email-ui/email-builder';
import { PreviewDialogComponent } from '@galaxy/email-ui/email-library/src/email-library-list/preview-dialog/preview-dialog.component';
import { AppNamespace, EmailBuilderTemplate, OwnerType, TemplatesService } from '@vendasta/email-builder';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { DynamicOpenCloseTemplateRefService, GalaxySideDrawerModule } from '@vendasta/galaxy/side-drawer';
import { MyCampaignsTableService } from '../../campaign-list-page/my-campaigns-table.service';
import { AddActionType, CampaignConfig, LOCALE_DATA_TYPE, SUPPORTED_LOCALES_TOKEN } from '../../dependencies';
import { PAGE_ROUTES } from '../../routing-constants';
import { AddActionsComponent } from './add-actions/add-actions.component';
import {
  CampaignDuplicateSuccessDialogComponent,
  Data,
} from './campaign-duplicate-success-dialog/campaign-duplicate-success-dialog.component';
import { CampaignID, CampaignOptionsComponent } from './campaign-options/campaign-options.component';
import { CampaignStateService } from './campaign-state.service';
import { CampaignStateComponent } from './campaign-state/campaign-state.component';
import { CampaignStatsComponent } from './campaign-stats/campaign-stats.component';
import { CampaignStepComponent } from './campaign-step/campaign-step.component';
import { CampaignsService } from './campaigns.service';
import { SchedulingComponent } from './scheduling/scheduling.component';
import { CONFIG_TOKEN } from '../../../../shared/src/tokens';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';

function focusToCampaignFocus(f: Focuses): CampaignFocus {
  switch (f) {
    case Focuses.FOCUSES_ACQUIRE:
      return CampaignFocus.ACQUIRE;
    case Focuses.FOCUSES_ADOPT:
      return CampaignFocus.ADOPT;
    case Focuses.FOCUSES_UPSELL:
      return CampaignFocus.UPSELL;
    case Focuses.FOCUSES_NONE:
      return CampaignFocus.NONE;
    default:
      return CampaignFocus.UNSPECIFIED;
  }
}

function statusToCampaignStatus(s: Statuses): CampaignStatus {
  switch (s) {
    case Statuses.STATUSES_DRAFT:
      return CampaignStatus.DRAFT;
    case Statuses.STATUSES_PUBLISHED:
      return CampaignStatus.PUBLISHED;
    case Statuses.STATUSES_ACTIVE:
      return CampaignStatus.ACTIVE;
    case Statuses.STATUSES_ARCHIVED:
      return CampaignStatus.ARCHIVED;
    default:
      return CampaignStatus.UNSPECIFIED;
  }
}

const EMAIL_LIBRARY_NAMESPACE = 'email_library';

@Component({
  selector: 'campaign-details',
  templateUrl: './campaign-details.component.html',
  styleUrls: ['./campaign-details.component.scss'],
  providers: [
    TranslateService,
    MarketingAutomationService,
    EmailTemplateService,
    SnackbarService,
    DateRangeSelectorService,
    FeatureFlagService,
    CampaignStatsService,
    CampaignsService,
    CampaignStateService,
    EmailTemplateSaver,
    TemplatesService,
    ActivationService,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    MatFormFieldModule,
    CampaignOptionsComponent,
    CampaignStateComponent,
    GalaxyLoadingSpinnerModule,
    CampaignStepComponent,
    CdkDrag,
    MatOptionModule,
    MatFormFieldModule,
    CdkDropList,
    MatSelectModule,
    UIKitModule,
    MatIconModule,
    MatButtonModule,
    GalaxyAlertModule,
    SchedulingComponent,
    MatInputModule,
    CampaignStatsComponent,
    GalaxySideDrawerModule,
    GalaxyPageModule,
    MatDialogModule,
    AddActionsComponent,
  ],
})
export class CampaignDetailsComponent implements OnInit, OnDestroy {
  private readonly bpObserver = inject(BreakpointObserver);

  breadcrumbs$: Observable<Breadcrumbs[]> = new Observable<Breadcrumbs[]>();
  private oldTitle = '';

  selectedMarketId$: Observable<string> = this.campaignStateService.selectedMarketId$;
  campaignSteps$: Observable<CampaignStepInterface[]> = this.campaignStateService.campaignSteps$;
  campaignState$: Observable<string> = this.campaignStateService.campaignState$;
  locale$: Observable<string> = this.campaignStateService.locale$;
  campaignId$: Observable<string> = new Observable<string>();
  businessId$: Observable<string> = new Observable<string>();

  sender$: Observable<SenderInterface> = this.campaignConfig.sender$;
  sender: SenderInterface;
  feature: Feature;
  campaignName$: Observable<string> = this.campaignStateService.name$;
  emailCategory$: Observable<string> = this.campaignStateService.emailCategory$;
  campaignDetailsV2$: Observable<GetterCampaignDataInterface> = this.campaignStateService.campaignDetailsV2$;

  // campaignDetails$: Observable<CampaignData>;
  templateDetails$: Observable<TemplateData[]> = new Observable<TemplateData[]>();

  // markets$: Observable<Market[]> = this.marketsService.userAccessibleMarkets$;
  // hasAccessToAllMarkets$: Observable<boolean> = this.marketsService.hasAccessToAllMarkets$;

  campaignScheduleVersion: number | undefined;
  campaignId: string;
  senderId: string;

  campaignFocus$: Observable<CampaignFocus> = this.campaignStateService.campaignDetailsV2$.pipe(
    map((cd) => focusToCampaignFocus(cd.focus || Focuses.FOCUSES_NONE)),
  );

  campaignStatus$: Observable<CampaignStatus> = this.campaignStateService.campaignStatus$.pipe(
    map((status) => statusToCampaignStatus(status || Statuses.STATUSES_UNSPECIFIED)),
  );

  emailCategoriesEnabled = false;

  draftMode$: Observable<boolean> = this.campaignStatus$.pipe(map((status) => status === CampaignStatus.DRAFT));

  disablePublishing$: Observable<boolean> = combineLatest(
    this.draftMode$,
    this.campaignSteps$,
    this.emailCategory$,
  ).pipe(
    map(
      ([draftMode, campaignSteps, emailCategory]) =>
        this.updatingStatus || (!draftMode && campaignSteps.length === 0 && emailCategory === ''),
    ),
  );

  readonly reload$$ = new BehaviorSubject(null);

  loadingStep = false;
  isSMSActive = false;
  isStateEmpty$: Observable<boolean>;
  dayList: number[] = [];
  shouldShowSnack = false;
  subscriptions: Subscription[] = [];
  editingTitle = false;
  cs: typeof CampaignStatus = CampaignStatus;
  updatingStatus = false;
  readonly hasAccessToMarkets$: Observable<boolean> = new Observable<boolean>();
  selectedLibraryTemplate$$: BehaviorSubject<Template>;
  previousPageUrl$: Observable<string>;

  nanDateFilter = true;
  dateFilterValue: I18NDescriptor = {
    key: '',
  };

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private marketingAutomationService: MarketingAutomationService,
    private mycampaignsTableService: MyCampaignsTableService,
    private emailTemplateService: EmailTemplateService,
    private templateService: TemplatesService,
    private emailTemplateSaver: EmailTemplateSaver,
    private translate: TranslateService,
    private snackbarService: SnackbarService,
    private dialog: MatDialog,
    private dateRangeService: DateRangeSelectorService,
    public readonly accountGroupService: AccountGroupService,
    private dynamicOpenCloseService: DynamicOpenCloseTemplateRefService,
    private readonly titleService: Title,
    private featureFlagService: FeatureFlagService,
    private readonly campaignsService: CampaignsService,
    @Inject('PARTNER_ID') readonly partnerId$: Observable<string>,
    @Inject(SENDER_ID_TOKEN) readonly senderId$: Observable<string>,
    private dialogRef: MatDialogRef<PreviewDialogComponent>,
    private readonly campaignStateService: CampaignStateService,
    @Inject(CONFIG_TOKEN) private readonly campaignConfig: CampaignConfig,
    private campaignStatsService: CampaignStatsService,
    private activation: ActivationService,
    @Optional() @Inject(SUPPORTED_LOCALES_TOKEN) public readonly supportedLocales$: LOCALE_DATA_TYPE,
    private confirmationModal: OpenConfirmationModalService,
    private posthogService: ProductAnalyticsService,
  ) {}

  ngOnInit(): void {
    this.isStateEmpty$ = this.campaignStateService.campaignSteps$.pipe(
      map((campaignSteps) => campaignSteps.length === 0),
    );

    this.campaignId$ = combineLatest([
      this.route.params.pipe(map((params) => params['campaignId'])),
      this.reload$$,
    ]).pipe(map(([campaignId]: [string, null]) => campaignId));

    this.subscriptions.push(
      this.campaignConfig.sender$.subscribe((sender) => {
        this.campaignStateService.setSender(sender);
      }),
    );

    this.subscriptions.push(
      this.campaignId$.subscribe((campaignId) => {
        this.campaignStateService.setCampaignId(campaignId);
        this.campaignId = campaignId;
      }),
    );
    this.subscriptions.push(
      this.senderId$.subscribe((senderId) => {
        this.senderId = senderId;
      }),
    );
    this.subscriptions.push(
      this.sender$.subscribe((sender) => {
        this.sender = sender;
      }),
    );

    const dateFilter$ = this.dateRangeService.getFilter();
    this.subscriptions.push(
      this.campaignSteps$.subscribe((campaignSchedule) => {
        this.dayList = [];
        if (campaignSchedule.length > 0) {
          this.dayList.push(Math.floor(campaignSchedule[0].seconds_after_last_email / 86400 + 1));
        }
        campaignSchedule.slice(1).forEach((step) => {
          const days = step.seconds_after_last_email / 86400;
          this.dayList.push(Math.floor(days + this.dayList[this.dayList.length - 1]));
        });
      }),
    );

    // Also, why are these separate subscriptions? Because we want to debounce but only for
    // saving the campaign steps to the server, not necessarily the calculation of which day
    // they will start on (i.e. the subscription above).
    this.subscriptions.push(
      combineLatest([this.campaignSteps$, this.campaignDetailsV2$])
        .pipe(
          skipWhile(
            // Don't update on the first one, but after this isn't true start letting everything through.
            ([steps, campaignDetails]) => JSON.stringify(steps) === JSON.stringify(campaignDetails.campaignSchedule),
          ),
          debounceTime(1000),
          switchMap(([campaignSchedule]) => {
            if (this.campaignScheduleVersion === undefined) {
              return of(undefined);
            }
            this.campaignStateService.updateCampaignSchedule(campaignSchedule);
            return of(this.campaignScheduleVersion || 0);
          }),
        )
        .subscribe({
          next: (campaignScheduleVersion) => {
            this.campaignScheduleVersion = campaignScheduleVersion || 0;
            if (this.shouldShowSnack) {
              this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.UPDATE.SUCCESS');
              this.shouldShowSnack = false;
            }
          },
          error: (err) => {
            console.error(err);
            if (this.shouldShowSnack) {
              this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.UPDATE.FAILURE');
              this.shouldShowSnack = false;
            }
          },
        }),
    );
    this.templateDetails$ = this.campaignSteps$.pipe(
      map((steps) => {
        return steps.map((step) => step.template_id).filter((templateId) => !!templateId);
      }),
      switchMap((templateIds) => {
        const ownerType = this.senderId.startsWith('AG-')
          ? OwnerType.OWNER_TYPE_ACCOUNT_GROUP
          : OwnerType.OWNER_TYPE_PARTNER;
        if (templateIds.length === 0) {
          return of([]);
        }
        return this.templateService.getTemplates(ownerType, this.senderId, AppNamespace.CAMPAIGNS, templateIds).pipe(
          map((emailBuilderTemplates: EmailBuilderTemplate[]) =>
            emailBuilderTemplates.map((emailTemplate) => ({
              templateId: emailTemplate.templateId,
              subject: emailTemplate.templateSubject,
              name: emailTemplate.templateDisplayName,
              fullHtml: emailTemplate.templateHtml,
              htmlBody: emailTemplate.templateHtml,
              useFullHtml: true,
            })),
          ),
        );
      }),
    );
    this.previousPageUrl$ = this.campaignConfig.basePath$;
    this.dateRangeService.setAsCustom({ start: moment(), end: moment() });
    this.subscriptions.push(
      dateFilter$.subscribe((filter) => {
        if (!filter) {
          return;
        }

        this.dateFilterValue.key = filter.i18nKey || '';
      }),
    );

    this.oldTitle = this.titleService.getTitle();
    this.titleService.setTitle(this.translate.instant('CAMPAIGN_DETAILS.TITLE'));
    this.subscriptions.push(
      this.mycampaignsTableService
        .getSender()
        .pipe(
          take(1),
          map((sender: SenderInterface) => {
            this.senderId = sender.id || '';
          }),
        )
        .subscribe(),
    );
  }

  async openSendPreviewPage() {
    const backUrl = encodeURIComponent(this.router.url);
    const campaignId = this.campaignId;
    const queryString = `backUrl=${backUrl}&campaignId=${campaignId}`;
    this.campaignConfig.basePath$.pipe(take(1)).subscribe((basePath) => {
      this.router.navigateByUrl(`${basePath}/preview-send?${queryString}`);
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.titleService.setTitle(this.oldTitle);
  }

  publishCampaign(): void {
    if (this.campaignStateService.getCampaignSteps() == 0) {
      this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.UPDATE.ERROR');
      return;
    }

    // Check if campaign is properly configured before proceeding
    this.checkCampaignConfiguration().then((isConfigured) => {
      if (!isConfigured) {
        this.snackbarService.openErrorSnack('CAMPAIGNS.CONTACTS.MODAL.ERRORS.CAMPAIGN_CONFIGURATION_ERROR');
        return;
      }

      this.posthogService.trackEvent('user-clicked-publish-campaign', 'create-campaign-workflow', 'click');
      this.campaignStateService.updateStatus(Statuses.STATUSES_PUBLISHED);
    });
  }

  /**
   * Checks if the campaign is properly configured.
   * If the configuration service is available, it checks the configuration.
   * If the configuration is not proper, it opens a configuration modal.
   *
   * @returns A promise that resolves to true if the campaign is properly configured or if the service is not available,
   * false otherwise.
   */
  private async checkCampaignConfiguration(): Promise<boolean> {
    if (!this.campaignConfig.campaignsConfigurationService$) {
      return true;
    }

    try {
      const service = await firstValueFrom(this.campaignConfig.campaignsConfigurationService$.pipe(take(1)));
      const isConfigured = await service.isProperlyConfigured();

      if (!isConfigured) {
        // Open the configuration modal and get the result
        const addressUpdated = await service.openConfigurationModal();

        // If the address was successfully updated, we can consider the campaign configured
        if (addressUpdated) {
          return true;
        }

        // Check again after modal is closed, in case configuration succeeded
        return await service.isProperlyConfigured();
      }

      return true;
    } catch (error) {
      // If there's an error checking configuration, allow the process to continue
      console.error('Error checking campaign configuration:', error);
      return true;
    }
  }

  async pauseCampaign(): Promise<void> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.PAUSE.TITLE', {
          title: this.campaignStateService.state.name,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.PAUSE.MESSAGE'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.PAUSE.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.PAUSE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const confirmed = await dialogRef.afterClosed().toPromise();
    if (confirmed) {
      try {
        await this.campaignStateService.pauseCampaign();
        this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.PAUSE.SUCCESS');
      } catch (error) {
        this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.PAUSE.FAILURE');
      }
    }
  }

  async resumeCampaign(): Promise<void> {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.RESUME.TITLE', {
          title: this.campaignStateService.state.name,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.RESUME.MESSAGE'),
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.RESUME.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.RESUME.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    const confirmed = await dialogRef.afterClosed().toPromise();
    if (confirmed) {
      try {
        await this.campaignStateService.unpauseCampaign();
        this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.RESUME.SUCCESS');
      } catch (error) {
        this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.RESUME.FAILURE');
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateSelectedMarket(selectionEvent: MatSelectChange): void {
    // TODO: put this back in
  }

  addRecommendedEmail(campaignId: string): void {
    // Takes you out of the angular app for now.
    this.router.navigateByUrl(`/campaign/details/${campaignId}/library`);
  }

  openPageComponent(senderId: string, userId: string): void {
    const dialogRef = this.dialog.open(PageComponent, {
      data: {
        openAsReadOnly: true,
        ownerId: senderId,
        userId: userId,
      },
      height: '75%',
      width: '75%',
    });
    dialogRef.afterClosed().subscribe((email: Template) => {
      if (email) {
        this.openPreviewDialog(email, senderId, userId);
      }
    });
  }

  openPreviewDialog(email: Template, senderId: string, userId: string): void {
    this.dialogRef = this.dialog.open(PreviewDialogComponent, {
      data: {
        primaryAction: DefaultCardAction.USE,
        template: email,
        userId: userId,
        ownerId: senderId,
      },
      height: '600px',
      width: '800px',
    });
    this.dialogRef
      .afterClosed()
      .pipe(
        map((useEmail) => {
          if (useEmail) {
            this.saveSelectedTemplate(email, senderId, this.campaignId);
          } else {
            this.openPageComponent(senderId, userId);
          }
        }),
      )
      .subscribe();
  }

  saveSelectedTemplate(email: Template, senderId: string, campaignId: string): void {
    this.loadingStep = true;
    this.emailTemplateSaver.updateContentData(JSON.parse(email.templateJson) as EmailContentData);
    this.emailTemplateSaver.updateHTML(email.templateHtml!);
    this.subscriptions.push(
      this.emailTemplateSaver
        .save$(senderId, Context.forNewTemplate(senderId), campaignId, '', {
          appNamespace: EMAIL_LIBRARY_NAMESPACE,
          templateId: email.templateId,
        } as TemplateReferenceInterface)
        .subscribe({
          next: (s: SaveResult | undefined) => {
            if (s !== undefined) {
              const templateName = this.emailTemplateSaver.getLatestData().name || '';
              const ctx = Context.forExistingTemplate(this.senderId, s.templateId);
              this.campaignStateService.addCampaignStep(ctx, this.campaignId, s.templateId, templateName);
            }
          },
          error: () => {
            this.loadingStep = false;
            this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.UPDATE.FAILURE');
          },
          complete: () => {
            this.loadingStep = false;
          },
        }),
    );
  }

  handleAddStep(action: AddActionType): void {
    switch (action) {
      case AddActionType.New:
        this.createNewEmail(this.campaignId);
        break;
      case AddActionType.Existing:
        this.handleSelectExistingEmail();
        break;
      case AddActionType.Snapshot:
        // TODO: add snapshot stuff to lib
        break;
      case AddActionType.SMS:
        this.handleSMSAction();
        break;
    }
  }

  handleSMSAction(): void {
    this.subscriptions.push(
      this.handleSMSFeatureActive().subscribe((isActive) => {
        if (isActive) {
          this.createNewSMS();
        }
      }),
    );
  }
  handleSMSFeatureActive(): Observable<boolean> {
    return this.activation.isFeatureActive(this.sender, Feature.SEND_SMS).pipe(
      tap((active) => {
        if (active) {
          this.isSMSActive = true;
        }
      }),
      map(() => this.isSMSActive),
    );
  }

  async createNewSMS(): Promise<void> {
    this.posthogService.trackEvent('user-clicked-create-sms-step', 'create-campaign-workflow', 'click');
    const basePath = await firstValueFrom(this.campaignConfig.basePath$);
    this.router.navigate([`/${basePath}/${PAGE_ROUTES.ROOT.SMS.TEMPLATE}`], {
      relativeTo: this.route,
      queryParams: { campaignId: this.campaignId },
    });
  }
  handleSelectExistingEmail(): void {
    this.posthogService.trackEvent('user-clicked-add-existing-email', 'create-campaign-workflow', 'click');
    this.subscriptions.push(
      combineLatest([this.senderId$, this.campaignConfig.userId$])
        .pipe(map(([senderId, userId]) => this.openPageComponent(senderId, userId)))
        .subscribe(),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  createNewEmail(campaignId: string): void {
    this.posthogService.trackEvent('user-clicked-create-new-email', 'create-campaign-workflow', 'click');
    this.subscriptions.push(
      this.campaignConfig.basePath$
        .pipe(take(1), withLatestFrom(this.locale$))
        .subscribe(([basePath, locale]: [string, string]) => {
          return this.router.navigate([`/${basePath}/${PAGE_ROUTES.ROOT.TEMPLATE_SUBROUTES.NEW}`], {
            relativeTo: this.route,
            queryParams: { campaignId: this.campaignId, locale: locale },
          });
        }),
    );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  addSnapshotStep(campaignId: string): void {
    //TODO: put this back in
  }

  editTitle(): void {
    this.editingTitle = true;
  }

  confirmEditing(newName: string): void {
    if (!newName) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.NAME.TOO_SHORT');
    } else if (newName.length > 100) {
      this.snackbarService.openErrorSnack('CAMPAIGNS.NAME.TOO_LONG');
    } else if (newName !== this.campaignStateService.state.name) {
      this.campaignStateService.updateName(newName);
    }
    this.editingTitle = false;
  }

  cancelEditing(): void {
    this.editingTitle = false;
  }

  getHumanReadableFocuses(focus: string): string {
    switch (focus) {
      case 'acquire':
        return this.translate.instant('CAMPAIGN_FOCUS.ACQUIRE');
      case 'adopt':
        return this.translate.instant('CAMPAIGN_FOCUS.ADOPT');
      case 'upsell':
        return this.translate.instant('CAMPAIGN_FOCUS.UPSELL');
      default:
        return this.translate.instant('CAMPAIGN_FOCUS.ACQUIRE');
    }
  }

  drop(event: CdkDragDrop<any>): void {
    // Nothing needs to update if they picked it up and put it back down.
    if (event.previousIndex === event.currentIndex) {
      return;
    }
    this.shouldShowSnack = true;
    const events = [...this.campaignStateService.state.campaignSteps];
    moveItemInArray(events, event.previousIndex, event.currentIndex);
    this.campaignStateService.updateCampaignDelay(events);
  }

  updateDelay([stepId, delayInSeconds]: [string, number]): void {
    // get the campaign steps
    const events = [...this.campaignStateService.state.campaignSteps].map((step) => {
      if (step.campaign_step_id !== stepId) {
        return step;
      }
      //mutate the delay in seconds of the step
      step = {
        campaign_step_id: step.campaign_step_id,
        name: step.name,
        seconds_after_last_email: delayInSeconds,
        step_type: step.step_type,
        template_id: step.template_id,
      };
      return step;
    });
    this.shouldShowSnack = true;
    // Re-set the steps and save to server
    this.campaignStateService.updateCampaignDelay(events);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateCampaignFocus(newFocus: string): void {
    // TODO: put this back in
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  updateCampaignEmailCategory(newCategory: string): void {
    // TODO: put this back in
  }

  updateCampaignLocale(newLocale: string): void {
    this.campaignStateService.updateLocale(newLocale);
  }

  removeStep([stepId, name]: [string, string]): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.EVENT.TITLE', {
          name: name,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.EVENT.MESSAGE', {
          name: name,
        }),
        warn: true,
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.DELETE_STEP.DECLINE'),
        },
      },
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.campaignStateService.removeCampaignStep(this.sender, this.campaignId, stepId);
      }
    });
  }

  goToEmailEdit([stepId, templateId]: [string, string]): void {
    this.subscriptions.push(
      this.campaignConfig.basePath$
        .pipe(take(1), withLatestFrom(this.locale$))
        .subscribe(([basePath, locale]: [string, string]) => {
          return this.router.navigate([`/${basePath}/${PAGE_ROUTES.ROOT.TEMPLATE_SUBROUTES.EDIT(templateId)}`], {
            relativeTo: this.route,
            queryParams: { campaignId: this.campaignId, campaignStepId: stepId, locale: locale },
          });
        }),
    );
  }

  goToSMSEdit([stepId, templateId]: [string, string]): void {
    this.subscriptions.push(
      this.campaignConfig.basePath$
        .pipe(take(1), withLatestFrom(this.locale$))
        .subscribe(([basePath, locale]: [string, string]) => {
          return this.router.navigate([`/${basePath}/${PAGE_ROUTES.ROOT.SMS.EDIT(templateId)}`], {
            relativeTo: this.route,
            queryParams: { campaignId: this.campaignId, campaignStepId: stepId, locale: locale },
          });
        }),
    );
  }

  openAddListToCampaignModal(): void {
    // TODO: put this back in
  }

  deleteCampaign(): void {
    this.confirmationModal
      .openModal({
        title: this.translate.instant('CAMPAIGN_DETAILS.DELETE.TITLE'),
        message: this.translate.instant('CAMPAIGN_DETAILS.DELETE.MESSAGE'),
        confirmButtonText: this.translate.instant('CAMPAIGN_DETAILS.DELETE.ACTION'),
        type: 'warn',
      })
      .pipe(
        take(1),
        switchMap((deleteConfirmed) => {
          if (!deleteConfirmed) {
            return EMPTY;
          }
          return this.campaignsService.delete(this.campaignId);
        }),
        withLatestFrom(this.campaignConfig.basePath$),
      )
      .subscribe(([, basePath]) => {
        this.mycampaignsTableService.reloadData();
        this.router.navigate([`/${basePath}`]);
      });
  }

  archiveCampaign(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.TITLE', {
          title: this.campaignStateService.state.name,
        }),
        message: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.MESSAGE'),
        warn: true,
        buttonText: {
          confirm: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.CONFIRM'),
          decline: this.translate.instant('CAMPAIGN_DETAILS.CONFIRMATION_DIALOG.ARCHIVE.DECLINE'),
        },
      },
      maxWidth: '600px',
    });
    dialogRef.afterClosed().subscribe((confirmed: boolean) => {
      if (confirmed) {
        this.updatingStatus = true;
        try {
          this.campaignStateService.updateStatus(Statuses.STATUSES_ARCHIVED);
          this.updatingStatus = false;
          this.snackbarService.openSuccessSnack('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_SUCCESS');
        } catch (error) {
          this.snackbarService.openErrorSnack('CAMPAIGN_DETAILS.ACTIONS.ARCHIVE_FAILURE');
        }
      }
    });
  }

  closePreview(): void {
    this.dynamicOpenCloseService.close();
  }

  goToOldPage(): void {
    this.campaignId$.subscribe((campaignId) => this.router.navigate(['campaign', 'details', campaignId]));
  }

  handleCampaignDuplicated(newCampaignId: CampaignID): void {
    const data: Data = {
      newCampaignId: newCampaignId,
    };
    this.dialog.open(CampaignDuplicateSuccessDialogComponent, { data: data });
  }

  isMobile(): boolean {
    return this.bpObserver.isMatched(Breakpoints.XSmall);
  }
}
