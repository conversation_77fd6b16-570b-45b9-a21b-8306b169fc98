import { Component, Input, OnInit, forwardRef } from '@angular/core';
import { UntypedFormControl, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ErrorControlDirective } from '../error-control.component';

@Component({
  selector: 'forms-phone-number-multi',
  templateUrl: './phone-number-multi.component.html',
  styleUrls: ['./phone-number-multi.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => PhoneNumberMultiComponent),
    },
  ],
  standalone: false,
})
export class PhoneNumberMultiComponent extends ErrorControlDirective implements OnInit, ControlValueAccessor {
  @Input() formControl: UntypedFormControl;
  @Input() maxMultiples = 1;
  @Input() required = false;
  @Input() placeholder = 'Phone Number(s)';
  @Input() set onChanged(onChangeFn: any) {
    this.registerOnChange(onChangeFn);
  }

  private _phoneNumbers: PhoneNumber[] = [{ number: '' }];
  disabled = false;

  // We do validation in backend. We just validate the user is putting number in phone number field here.
  phoneRegex = new RegExp(/^([0-9]+)?$/);
  extensionRegex: RegExp = this.phoneRegex;

  onChange: any = (): void => {
    return;
  };
  onTouched: any = (): void => {
    return;
  };
  ngOnInit(): void {
    this.phoneNumbers = this.parseNumbers(this.formControl.value);
  }

  get phoneNumbers(): PhoneNumber[] {
    return this._phoneNumbers;
  }

  set phoneNumbers(numbers: PhoneNumber[]) {
    this._phoneNumbers = numbers;
    this.handleChange(numbers);
  }

  handleChange(numbers: PhoneNumber[]): void {
    this.onChange(this.serializeNumbers(numbers));
    this.updateErrors(numbers);
  }

  parseNumbers(numbers: string[]): PhoneNumber[] {
    const nums: PhoneNumber[] = [];
    if (!numbers) {
      return [{ number: '' }];
    }
    numbers.forEach((n: string) => {
      const numParts = n.split(',');
      nums.push({
        number: numParts[0],
        extension: numParts.length > 1 && numParts[1].length > 0 ? numParts[1] : undefined,
      });
    });
    return nums;
  }

  serializeNumbers(numbers: PhoneNumber[]): string[] {
    const vals: string[] = [];
    numbers.forEach((number: PhoneNumber) => {
      vals.push(number.extension ? number.number + ',' + number.extension : number.number);
    });
    return vals;
  }

  updateNumber(index: number, value: string): void {
    if (this.phoneNumbers.length < index + 1) {
      return;
    }
    this.phoneNumbers[index].number = value;
    this.handleChange(this.phoneNumbers);
  }

  updateExtension(index: number, value: string): void {
    if (this.phoneNumbers.length < index + 1) {
      return;
    }
    value = value.length > 0 ? value : undefined;
    this.phoneNumbers[index].extension = value;
    this.handleChange(this.phoneNumbers);
  }

  addNumber(): void {
    if (this.phoneNumbers.length >= this.maxMultiples) {
      return;
    }
    this.phoneNumbers.push({ number: '' });
    this.handleChange(this.phoneNumbers);
  }

  deleteNumber(index: number): void {
    if (this.required && this.phoneNumbers.length <= 1) {
      return;
    }
    this.phoneNumbers.splice(index, 1);
    this.handleChange(this.phoneNumbers);
  }

  updateErrors(numbers: PhoneNumber[]): void {
    const errors = {};
    if (!numbers || numbers.length < 1 || numbers[0].number.length < 1) {
      if (this.required) {
        errors['required'] = true;
      }
    } else {
      numbers.forEach((number: PhoneNumber) => {
        const sanitizedNumber = number.number ? number.number.replace(/[- )(]/g, '') : number.number;
        const matchPhoneRegex = this.phoneRegex.test(sanitizedNumber);
        const matchExtensionRegex = this.extensionRegex.test(number.extension);
        if ((sanitizedNumber && !matchPhoneRegex) || (number.extension && !matchExtensionRegex)) {
          errors['phone'] = true;
        }
      });
    }
    this.formControl.setErrors(Object.keys(errors).length === 0 ? null : errors);
  }

  handleBlur(): void {
    this.onTouched();
  }

  writeValue(value: any): void {
    this.phoneNumbers = this.parseNumbers(value);
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}

export interface PhoneNumber {
  number: string;
  extension?: string;
}
