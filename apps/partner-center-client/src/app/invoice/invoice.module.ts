import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { IntervalMappingService } from '@galaxy/billing';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { BillingUiModule, MemoComponent } from '@vendasta/billing-ui';
import { BusinessSelectorDialogModule } from '@vendasta/businesses';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { GalaxyPipesModule } from '@vendasta/galaxy/pipes';
import { GalaxySnackbarModule } from '@vendasta/galaxy/snackbar-service';
import {
  InvoiceTableComponent,
  PadInvoiceNumberPipe,
  PaymentMethodSelectorComponent,
  SMBInvoicingModule,
  StatusBadgePipe,
} from '@vendasta/smb-invoicing';
import { EmptyStateModule, VaFilterModule, VaMaterialTableModule, VaStencilsModule } from '@vendasta/uikit';
import { FilterModule } from '@vendasta/va-filter2';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { BillingModule } from '../billing';
import { FormatDatePipe } from '../common/pipes/format-date.pipe';
import { FromNowPipe } from '../common/pipes/from-now.pipe';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { CreateAppDialogModule } from '../marketplace-app/create-app-workflow-dialog/create-app-dialog.module';
import { EditInvoicePageComponent } from './edit-invoice-page/edit-invoice-page.component';
import { EditInvoicePageService } from './edit-invoice-page/edit-invoice-page.service';
import { InvoiceFooterComponent } from './edit-invoice-page/invoice-footer/invoice-footer.component';
import { InvoiceHeaderComponent } from './edit-invoice-page/invoice-header/invoice-header.component';
import { SendEmailDialogComponent } from './edit-invoice-page/send-email-dialog/send-email-dialog.component';
import { InvoiceRoutingModule } from './invoice-routing.module';
import { InvoiceStoreService } from './invoice.store.service';
import { InvoicesPageComponent } from './invoices-page/invoices-page.component';
import { PreviewInvoiceComponent } from './preview/iframe/preview-invoice.component';
import { PaymentMethodTypesInputComponent } from './edit-invoice-page/payment-method-types-input/payment-method-types-input.component';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { WhiteLabelBannerComponent } from './edit-invoice-page/white-label-banner/white-label-banner.component';
import { RecipientDisplayNamePipe } from '../common/pipes/recipient-display-name';
import { RECIPIENT_PROVIDER } from './providers';
import { RecipientProviderService } from './recipients/recipient-provider.service';
import { LineItemFormComponent } from '@galaxy/partner-center-client/billing/static/billing-line-item/line-item-form.component';
import { CommonLineItemStoreService } from '@galaxy/partner-center-client/billing/static/billing-line-item/common-line-item-store.service';
import { RecipientChipsComponent } from '@galaxy/partner-center-client/billing/static/shared/recipients';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { CreditNoteListComponent } from '@galaxy/partner-center-client/billing/static/credit-note/credit-note-list/credit-note-list.component';
import { PaymentFacilitatorErrorStateComponent } from '@galaxy/partner-center-client/billing/static/payment-facilitator-error-state/payment-facilitator-error-state.component';
import { PaymentsTableComponent } from '../billing/payments/table/payments-table.component';
import { ButtonLoadingIndicatorComponent } from '@vendasta/galaxy/button-loading-indicator';

@NgModule({
  declarations: [
    EditInvoicePageComponent,
    InvoicesPageComponent,
    InvoiceHeaderComponent,
    SendEmailDialogComponent,
    PreviewInvoiceComponent,
    InvoiceFooterComponent,
  ],
  imports: [
    CommonModule,
    MatListModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatMenuModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatRadioModule,
    ReactiveFormsModule,
    MatCardModule,
    MatDatepickerModule,
    MatGridListModule,
    MatDialogModule,
    MatTooltipModule,
    FormsModule,
    GalaxyPageModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    InvoiceRoutingModule,
    GalaxySnackbarModule,
    BillingUiModule,
    BillingModule,
    EmptyStateModule,
    VaFilterModule,
    VaStencilsModule,
    VaMaterialTableModule,
    FilterModule,
    SMBInvoicingModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatChipsModule,
    CreateAppDialogModule,
    LineItemFormComponent,
    MatSlideToggleModule,
    TranslateModule,
    GalaxyEmptyStateModule,
    BusinessSelectorDialogModule,
    GalaxyPipesModule,
    GalaxyPageModule,
    GalaxyPageModule,
    MatButtonToggleModule,
    GalaxyFormFieldModule,
    FormatDatePipe,
    FromNowPipe,
    RecipientDisplayNamePipe,
    GalaxyBadgeModule,
    MatExpansionModule,
    PaymentMethodTypesInputComponent,
    GalaxyAlertModule,
    PaymentMethodSelectorComponent,
    WhiteLabelBannerComponent,
    MemoComponent,
    RecipientChipsComponent,
    GalaxyTooltipModule,
    CreditNoteListComponent,
    PaymentsTableComponent,
    PaymentFacilitatorErrorStateComponent,
    PadInvoiceNumberPipe,
    StatusBadgePipe,
    InvoiceTableComponent,
    ButtonLoadingIndicatorComponent,
  ],
  providers: [
    EditInvoicePageService,
    { provide: CommonLineItemStoreService, useClass: InvoiceStoreService },
    IntervalMappingService,
    { provide: RECIPIENT_PROVIDER, useExisting: RecipientProviderService },
  ],
})
export class InvoiceModule {}
