import { animate, state, style, transition, trigger } from '@angular/animations';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, OnDestroy, OnInit, Output } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { ExtendedDomain } from '@galaxy/email-ui/domain-authorization';
import { TranslateService } from '@ngx-translate/core';
import { DomainService as DomainSDKService, ListAllDomainsResponse, SenderType } from '@vendasta/email';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  BehaviorSubject,
  Observable,
  Subscription,
  combineLatest,
  distinctUntilChanged,
  forkJoin,
  interval,
  of,
  timer,
} from 'rxjs';
import { catchError, map, pluck, switchMap, takeUntil, tap } from 'rxjs/operators';
import { AccountsService } from '../../accounts.service';
import { ApiErrorFactory } from '../../core/api-errors';
import { DNSRecord, DNSRecordType, DNSService } from '../../core/dns';
import { SiteRouteParams } from '../../core/routeparams';
import { SiteService } from '../../core/site';
import { Domain, DomainStatus } from '../../core/site/domain';
import { ErrorDialog } from '../dialog';
import { DEFAULT_DOMAIN_SUFFIX, MULTISITE_DOMAIN_SUFFIX } from '../../../globals';
import { AddDomainDialogComponent } from './add-domain/add-domain.component';
import { DeleteDomainDialogComponent } from './delete-domain/delete-domain.component';
import { DnsInstruction } from './dns-instruction';
import { PrimaryDomainDialogComponent } from './primary-domain/primary-domain.component';
import { ProductAnalyticsService } from '@vendasta/product-analytics';

@Component({
  selector: 'app-domain-manager',
  templateUrl: './domain-manager.component.html',
  styleUrls: ['./domain-manager.styles.scss', './table-styles.scss'],
  animations: [
    trigger('detailExpand', [
      state('void', style({ height: '0px', minHeight: '0', visibility: 'hidden' })),
      state('*', style({ height: '*', visibility: 'visible' })),
      transition('void <=> *', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
  standalone: false,
})
export class DomainManagerComponent implements OnInit, OnDestroy {
  expandedElementMap: Map<string, boolean> = new Map();
  noAAAARecordWarning: string;
  recordDetectedAAAA: string;
  siteId: string;
  partnerId: string;
  areDomainsLoading = true;
  instructions: DnsInstruction[] = [];
  uniqueInstructions: DnsInstruction[] = [];
  domainRefresh$$ = new BehaviorSubject(null);
  timer$: Observable<any>;
  dataSource = new MatTableDataSource();
  displayedColumns = ['domain', 'ssl_cert', 'status', 'actions'];
  connected: string = DomainStatus.connected;
  pending: string = DomainStatus.pending;
  disconnected: string = DomainStatus.disconnected;
  deleting: string = DomainStatus.deleting;
  @Output() makePrimaryDomain = new EventEmitter();
  private dialogRef: MatDialogRef<ErrorDialog>;
  private subscriptions: Subscription[] = [];
  private recordTypes: DNSRecordType[];
  private domainList: Domain[] = [];
  isExpress$: Observable<boolean>;
  skipExpanding = false;

  senderType: SenderType = SenderType.SENDER_TYPE_BUSINESS;
  accountId$: Observable<string>;
  allDomains$: Observable<ExtendedDomain[]>;
  pollingInterval = 1500;
  pollingDuration = 20000;

  constructor(
    private accountsService: AccountsService,
    private translateService: TranslateService,
    private dnsService: DNSService,
    private siteService: SiteService,
    private route: ActivatedRoute,
    private dialog: MatDialog,
    private apiErrorFactory: ApiErrorFactory,
    private readonly domainsService: DomainSDKService,
    private readonly snack: SnackbarService,
    public readonly analyticsService: ProductAnalyticsService,
  ) {
    this.isExpress$ = this.accountsService.isExpress$;
    this.timer$ = timer(0, 20000);
  }

  private static removeDuplicates(instructions: DnsInstruction[]): DnsInstruction[] {
    const uniqueInstructions: DnsInstruction[] = [];
    uniqueInstructions.push(instructions[0]);
    for (let i = 1; i < instructions.length; i++) {
      if (
        instructions[i].host === instructions[i - 1].host &&
        instructions[i].recordType === instructions[i - 1].recordType
      ) {
        uniqueInstructions[uniqueInstructions.length - 1].currentValue = 'Multiple';
      } else {
        uniqueInstructions.push(instructions[i]);
      }
    }
    return uniqueInstructions;
  }

  ngOnInit(): void {
    this.siteId = (this.route.snapshot.params as SiteRouteParams).siteId;

    setTimeout(() => {
      this.accountsService.editionName$.subscribe((editionName: string) => {
        this.subscriptions.push(this.fetchDomains(editionName).subscribe());
      });
    }, 2000);

    this.noAAAARecordWarning = this.translateService.instant('PAGES.DOMAINS.SSL_STATUS.AAAA_WARNING');
    this.recordDetectedAAAA =
      this.translateService.instant('PAGES.DOMAINS.SSL_STATUS.AAAA') +
      // eslint-disable-next-line no-useless-escape
      ` \dnsRecord\dnsRecord ` +
      this.noAAAARecordWarning;

    this.accountId$ = this.siteService.site$.pipe(pluck('accountId'));
    this.allDomains$ = this.pollForChanges();
  }

  pollForChanges(): Observable<ExtendedDomain[]> {
    return interval(this.pollingInterval).pipe(
      switchMap(() => this.accountId$),
      switchMap((accountId) => this.domainsService.listAllDomains(this.senderType, accountId)),
      map((v: ListAllDomainsResponse) => {
        const returnDomains: ExtendedDomain[] = v.domains;
        for (const instruction of this.uniqueInstructions) {
          const domain = returnDomains.find((x) => instruction.host.includes(x.sendFromDomain));
          if (domain) {
            if (instruction.recordType === 'CNAME_RECORD') {
              domain.targetCNAME = instruction.targetValue;
            } else {
              domain.targetA = instruction.targetValue;
            }
          }
        }
        return returnDomains;
      }),
      distinctUntilChanged((a, b) => this.areDomainsEqual(a, b)),
      takeUntil(timer(this.pollingDuration)),
    );
  }

  private areDomainsEqual(arr1: ExtendedDomain[], arr2: ExtendedDomain[]): boolean {
    if (arr1.length !== arr2.length) return false;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i].sendFromDomain !== arr2[i].sendFromDomain) {
        return false;
      }
    }
    return true;
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((s) => s.unsubscribe());
  }

  getSkipExpanding(): boolean {
    return this.skipExpanding ? !(this.skipExpanding = !this.skipExpanding) : this.skipExpanding;
  }

  setSkipExpanding(): void {
    this.skipExpanding = true;
  }

  loadDomainsTable(data: Domain[]): void {
    this.sortDomainsByStatus(data);

    const filteredData = this.filterDomainsBasedOnSuffix(data);

    let notEqual: boolean;
    // only change datasource.data if the data has actually changed
    if (filteredData.length !== this.domainList.length) {
      notEqual = true;
    } else {
      notEqual = filteredData.some(
        (ele, i) =>
          ele.domain !== this.domainList[i].domain ||
          ele.status !== this.domainList[i].status ||
          ele.isPrimary !== this.domainList[i].isPrimary ||
          ele.isAAAADetected !== this.domainList[i].isAAAADetected ||
          ele.canPing !== this.domainList[i].canPing ||
          ele.hasSSLCert !== this.domainList[i].hasSSLCert,
      );
    }
    if (notEqual) {
      this.domainList = filteredData;
      this.dataSource.data = filteredData;
    }
  }

  private filterDomainsBasedOnSuffix(domains: Domain[]): Domain[] {
    return domains.some((d) => d.domain.endsWith(MULTISITE_DOMAIN_SUFFIX))
      ? domains.filter((d) => !d.domain.endsWith(DEFAULT_DOMAIN_SUFFIX))
      : domains;
  }

  fetchDomains(editionName: string): Observable<any> {
    return combineLatest([this.timer$, this.domainRefresh$$]).pipe(
      switchMap(() => this.siteService.getAssociatedDomains(this.siteId)),
      pluck('domainsList'),
      switchMap((domains: Domain[]) => {
        const dnsDomains = domains.filter((d) => d.status !== 'connected' && !d.canPing);
        if (dnsDomains.length === 0) {
          this.loadDomainsTable(domains);
          this.areDomainsLoading = false;
        }
        return forkJoin(
          dnsDomains.map((domain) => {
            this.recordTypes = DnsInstruction.lookupRecordType(domain.domain);
            return this.dnsService.lookup(this.siteId, domain.domain, this.recordTypes).pipe(
              map((dnsRecord) => {
                if (dnsRecord.aaaaRecord && dnsRecord.aaaaRecord.values && dnsRecord.aaaaRecord.values.length > 0) {
                  domain.isAAAADetected = true;
                  domain.translateSSLStatusKey = 'PAGES.DOMAINS.SSL_STATUS.AAAA';
                }
                return dnsRecord;
              }),
            );
          }),
        ).pipe(
          tap(() => {
            return this.loadDomainsTable(domains);
          }),
        );
      }),
      map((res) => {
        if (res) {
          this.handleDNSRecord(res, editionName);
        }
        this.areDomainsLoading = false;
      }),
      catchError(() => {
        this.handleDnsError();
        return of([]);
      }),
    );
  }

  showAddDomainDialog(): void {
    const addDomainDialog = this.dialog.open(AddDomainDialogComponent, {
      data: { siteId: this.siteId },
      disableClose: false,
      hasBackdrop: true,
      width: '500px',
    });
    this.subscriptions.push(
      addDomainDialog
        .afterClosed()
        .pipe(switchMap(() => this.pollForChanges()))
        .subscribe((domains) => {
          this.domainRefresh$$.next(null);
          this.allDomains$ = of(domains);
        }),
    );
  }

  public showDeleteDomainDialog(domain: string): void {
    if (domain === '') return;

    const deleteDomainDialog = this.dialog.open(DeleteDomainDialogComponent, {
      data: { siteId: this.siteId, domain: domain },
      disableClose: false,
      hasBackdrop: true,
    });

    this.subscriptions.push(
      deleteDomainDialog.afterClosed().subscribe(
        (res) => {
          if (res) {
            this.domainRefresh$$.next(null);
          }
        },
        (err) => this.displayError(err),
      ),
    );
  }

  handleDeleteEmailDomainRequested(domain: string): void {
    this.subscriptions.push(
      this.accountId$
        .pipe(
          switchMap((accountId) => this.domainsService.deletePendingDomain(this.senderType, accountId, domain)),
          switchMap(() => this.pollForChanges()),
        )
        .subscribe(
          (domains) => {
            this.allDomains$ = of(domains);
            this.snack.openSuccessSnack('Domain deleted');
          },
          () => {
            this.snack.openErrorSnack('There was a problem deleting the domain. Try again.');
          },
        ),
    );
  }

  public showMakePrimaryDialog(domain: string): void {
    domain = domain.trim();
    if (!domain) return;

    const makePrimaryDialog = this.dialog.open(PrimaryDomainDialogComponent, {
      data: { siteId: this.siteId, domain: domain },
      disableClose: false,
      hasBackdrop: true,
    });

    this.subscriptions.push(
      makePrimaryDialog.afterClosed().subscribe((res) => {
        if (res) {
          this.domainRefresh$$.next(null);
        }
      }),
    );
  }

  private handleDNSRecord(records: DNSRecord[], editionName: string): void {
    this.instructions = [];
    for (const record of records) {
      this.instructions.push(...DnsInstruction.buildFromDNSRecord(record, editionName));
    }
    this.instructions.sort((a, b) => {
      return a.host.length < b.host.length ? -1 : 1;
    });
    if (this.instructions.length >= 2) {
      // there can only be duplicates if the array has 2 or more elements in it
      this.uniqueInstructions = DomainManagerComponent.removeDuplicates(this.instructions);
    } else {
      this.uniqueInstructions = this.instructions;
    }
  }

  private handleDnsError(): void {
    for (const instruction of this.instructions) {
      instruction.clearCurrentValue();
    }
    for (const instruction of this.uniqueInstructions) {
      instruction.clearCurrentValue();
    }
  }

  private displayError(errorResponse: HttpErrorResponse): void {
    const parsedError = this.apiErrorFactory.parseErrorResponse(errorResponse);
    this.dialogRef = this.dialog.open(ErrorDialog, {
      data: {
        errMsg: parsedError.getUserMessage(),
      },
    });
  }

  private sortDomainsByStatus(domains: Domain[]): Domain[] {
    return domains.sort((a, b) => {
      return a.status < b.status ? -1 : a.status === b.status ? (a.domain < b.domain ? -1 : 1) : 1;
    });
  }
}
