import { createComponentFactory, Spectator } from '@ngneat/spectator/jest';
import { MessageUIComponentsComponent } from './message-ui-components.component';
import { CONVERSATION_HOST_APP_INTERFACE_TOKEN } from '../../../../../../../core/src/lib/tokens';
import { ConversationService } from '../../../../../../../core/src/lib/state/conversation.service';
import {
  conversationServiceMock,
  conversationApiServiceMock,
  environmentServiceMock,
  participantServiceMock,
  snackbarServiceMock,
  translateServiceMock,
} from '../../../../../../../core/src/lib/mocks';
import { ConversationMessage } from '../../../../../../../core/src/lib/interface/conversation.interface';
import {
  Participant,
  ConversationChannel,
  MessageType,
  PlatformLocation,
  UIComponentType,
  UIButtonInterface,
  ConversationApiService,
} from '@vendasta/conversation';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Router } from '@angular/router';
import { ParticipantService } from '../../../../../../../core/src/lib/participant.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  ESCALATE_TO_SUPPORT_ACTION_KEY,
  TOOL_CALL_ACTION_KEY,
} from '../../../../../../../core/src/lib/conversation.constants';
import { CONVERSATION_CONFIG_TOKEN } from '../../../../../../../core/src/lib/tokens';
import { ConversationConfig } from '../../../../../../../core/src/lib/interface/config.interface';
import { of, throwError } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

describe('MessageUIComponentsComponent', () => {
  let spectator: Spectator<MessageUIComponentsComponent>;
  let conversationApiService: jest.Mocked<ConversationApiService>;
  let snackbarService: jest.Mocked<SnackbarService>;
  let translateService: jest.Mocked<TranslateService>;

  const mockHostAppInterface = {
    isSenderFromOrganization: jest.fn().mockReturnValue(true),
  };

  const mockRouter = {
    navigate: jest.fn().mockResolvedValue(true),
  };

  const conversationConfigMock = {
    userId$: of('test-user-id'),
    accountGroupId$: of('test-account-group-id'),
    country$: of('US'),
    geographicalState$: of('CA'),
    partnerId$: of('test-partner-id'),
    marketId$: of('test-market-id'),
    platformLocation: PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER,
    openZendeskChat: jest.fn(),
  } as ConversationConfig;

  const createComponent = createComponentFactory({
    component: MessageUIComponentsComponent,
    providers: [
      { provide: ConversationService, useValue: conversationServiceMock },
      { provide: ConversationApiService, useValue: conversationApiServiceMock },
      { provide: CONVERSATION_HOST_APP_INTERFACE_TOKEN, useValue: mockHostAppInterface },
      { provide: ParticipantService, useValue: participantServiceMock },
      { provide: SnackbarService, useValue: snackbarServiceMock },
      { provide: EnvironmentService, useValue: environmentServiceMock },
      { provide: TranslateService, useValue: translateServiceMock },
      { provide: Router, useValue: mockRouter },
      { provide: CONVERSATION_CONFIG_TOKEN, useValue: conversationConfigMock },
    ],
  });

  beforeEach(() => {
    // Add the missing makeToolCall method to the existing mock
    (conversationApiServiceMock as any).makeToolCall = jest.fn().mockReturnValue(of({}));
    // Convert existing mock methods to jest spies
    (snackbarServiceMock as any).openErrorSnack = jest.fn();
    (translateServiceMock as any).instant = jest.fn().mockReturnValue('Something went wrong');

    spectator = createComponent({
      props: {
        inputMessage: {
          messageId: 'message-123',
          type: MessageType.MESSAGE_TYPE_MESSAGE,
          channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
          UIComponents: [],
        },
      },
    });

    conversationApiService = conversationApiServiceMock as any;
    snackbarService = snackbarServiceMock as any;
    translateService = translateServiceMock as any;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(spectator.component).toBeTruthy();
  });

  describe('escalateToSupport', () => {
    let mockZopim: any;
    let currentParticipant: Participant;

    beforeEach(() => {
      mockZopim = {
        livechat: {
          isChatting: jest.fn(),
          set: jest.fn(),
          say: jest.fn(),
          window: {
            show: jest.fn(),
          },
        },
      };

      Object.defineProperty(window, '$zopim', {
        value: mockZopim,
        writable: true,
      });

      currentParticipant = new Participant({
        name: 'John Doe',
        email: '<EMAIL>',
      });
    });

    afterEach(() => {
      jest.resetAllMocks();
    });

    it('should return early if escalate button is not found', () => {
      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [],
      } as ConversationMessage;

      spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.isChatting).not.toHaveBeenCalled();
    });

    it('should return early if escalate button has no payload', () => {
      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: 'escalate_to_support',
              payload: undefined,
            },
          },
        ],
      } as ConversationMessage;

      spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.isChatting).not.toHaveBeenCalled();
    });

    it('should handle invalid JSON payload and show chat window', async () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      mockZopim.livechat.isChatting.mockReturnValue(false);

      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: 'invalid json',
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(consoleWarnSpy).toHaveBeenCalledWith('Could not parse payload JSON:', 'invalid json', expect.any(Error));
      expect(mockZopim.livechat.isChatting).toHaveBeenCalled();
      expect(mockRouter.navigate).toHaveBeenCalled();
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();

      consoleWarnSpy.mockRestore();
    });

    it('should set user info and show chat window when not chatting and no conversation context', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);

      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: '{}',
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.set).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
      });
      expect(mockZopim.livechat.say).not.toHaveBeenCalled();
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();
    });

    it('should set user info and send conversation context in PROD environment', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);
      environmentServiceMock.getEnvironment = jest.fn().mockReturnValue(Environment.PROD);

      const conversationSummary = 'User needs help with billing issue';
      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: JSON.stringify({ conversationSummary }),
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.set).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
      });
      expect(mockZopim.livechat.say).toHaveBeenCalledWith(conversationSummary);
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();

      expect(conversationConfigMock.openZendeskChat).toHaveBeenCalled();
    });

    it('should send test message in DEMO environment', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);
      environmentServiceMock.getEnvironment = jest.fn().mockReturnValue(Environment.DEMO);

      const conversationSummary = 'User needs help with billing issue';
      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: JSON.stringify({ conversationSummary }),
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.say).toHaveBeenCalledWith(
        'Ignore this message. This is a test from Vendasta development team - ' + conversationSummary,
      );

      expect(conversationConfigMock.openZendeskChat).toHaveBeenCalled();
    });

    it('should send test message in LOCAL environment', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);
      environmentServiceMock.getEnvironment = jest.fn().mockReturnValue(Environment.LOCAL);

      const conversationSummary = 'User needs help with account setup';
      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: JSON.stringify({ conversationSummary }),
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.say).toHaveBeenCalledWith(
        'Ignore this message. This is a test from Vendasta development team - ' + conversationSummary,
      );

      expect(conversationConfigMock.openZendeskChat).toHaveBeenCalled();
    });

    it('should only show chat window when already chatting', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(true);

      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: JSON.stringify({ conversationSummary: 'Some context' }),
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.set).not.toHaveBeenCalled();
      expect(mockZopim.livechat.say).not.toHaveBeenCalled();
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();
    });

    it('should handle empty conversation summary', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);
      environmentServiceMock.getEnvironment = jest.fn().mockReturnValue(Environment.PROD);

      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: JSON.stringify({ conversationSummary: '' }),
            },
          },
        ],
      } as ConversationMessage;

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.set).toHaveBeenCalled();
      expect(mockZopim.livechat.say).not.toHaveBeenCalled();
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();

      expect(conversationConfigMock.openZendeskChat).toHaveBeenCalled();
    });

    it('should handle missing participant name and email', async () => {
      mockZopim.livechat.isChatting.mockReturnValue(false);

      const message = {
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          {
            button: {
              action: ESCALATE_TO_SUPPORT_ACTION_KEY,
              payload: '{}',
            },
          },
        ],
      } as ConversationMessage;

      const currentParticipant = new Participant({});

      await spectator.component.escalateToSupport(message, currentParticipant);

      expect(mockZopim.livechat.set).toHaveBeenCalledWith({
        name: '',
        email: '',
      });
      expect(mockZopim.livechat.window.show).toHaveBeenCalled();
    });
  });

  describe('messageButtons', () => {
    it('should return only button type UI components', () => {
      spectator.setInput('inputMessage', {
        messageId: 'message-123',
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: [
          { type: UIComponentType.UI_COMPONENT_TYPE_BUTTON },
          { type: UIComponentType.UI_COMPONENT_TYPE_BUTTON },
        ],
      });

      const messageButtons = spectator.component.messageButtons;

      expect(messageButtons).toHaveLength(2);
    });

    it('should return empty array when no UI components exist', () => {
      spectator.setInput('inputMessage', {
        messageId: 'message-123',
        type: MessageType.MESSAGE_TYPE_MESSAGE,
        channel: ConversationChannel.CONVERSATION_CHANNEL_AI_ASSISTANT,
        UIComponents: undefined,
      });

      const messageButtons = spectator.component.messageButtons;

      expect(messageButtons).toEqual([]);
    });
  });

  describe('handleButtonClick', () => {
    it('should call escalateToSupport when button action is ESCALATE_TO_SUPPORT_ACTION_KEY', async () => {
      const button: UIButtonInterface = {
        action: ESCALATE_TO_SUPPORT_ACTION_KEY,
        id: 'escalate-button',
      };

      jest.spyOn(spectator.component, 'escalateToSupport').mockResolvedValue();

      await spectator.component.handleButtonClick(button);

      expect(spectator.component.escalateToSupport).toHaveBeenCalled();
    });

    it('should call makeToolCall when button action is TOOL_CALL_ACTION_KEY', async () => {
      const button: UIButtonInterface = {
        action: TOOL_CALL_ACTION_KEY,
        id: 'tool-call-button',
      };

      const toolCallStartedSpy = jest.spyOn(spectator.component.toolCallStarted, 'emit');

      await spectator.component.handleButtonClick(button);

      expect(toolCallStartedSpy).toHaveBeenCalled();
      expect(conversationApiService.makeToolCall).toHaveBeenCalledWith({
        messageOption: {
          messageId: 'message-123',
          selectedOptionIds: ['tool-call-button'],
        },
      });
    });

    it('should handle tool call with button without id', async () => {
      const button: UIButtonInterface = {
        action: TOOL_CALL_ACTION_KEY,
      };

      const toolCallStartedSpy = jest.spyOn(spectator.component.toolCallStarted, 'emit');

      await spectator.component.handleButtonClick(button);

      expect(toolCallStartedSpy).toHaveBeenCalled();
      expect(conversationApiService.makeToolCall).toHaveBeenCalledWith({
        messageOption: {
          messageId: 'message-123',
          selectedOptionIds: [''],
        },
      });
    });

    it('should show error snackbar when tool call fails', async () => {
      const button: UIButtonInterface = {
        action: TOOL_CALL_ACTION_KEY,
        id: 'tool-call-button',
      };

      conversationApiService.makeToolCall.mockReturnValue(throwError(() => new Error('API Error')));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const toolCallStartedSpy = jest.spyOn(spectator.component.toolCallStarted, 'emit');

      await spectator.component.handleButtonClick(button);

      expect(toolCallStartedSpy).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Error making tool call:', expect.any(Error));
      expect(snackbarService.openErrorSnack).toHaveBeenCalledWith('Something went wrong');
      expect(translateService.instant).toHaveBeenCalledWith('INBOX.SOMETHING_WENT_WRONG');

      consoleSpy.mockRestore();
    });

    it('should do nothing for unknown button actions', async () => {
      const button: UIButtonInterface = {
        action: 'UNKNOWN_ACTION',
        id: 'unknown-button',
      };

      jest.spyOn(spectator.component, 'escalateToSupport').mockResolvedValue();
      const toolCallStartedSpy = jest.spyOn(spectator.component.toolCallStarted, 'emit');

      await spectator.component.handleButtonClick(button);

      expect(spectator.component.escalateToSupport).not.toHaveBeenCalled();
      expect(conversationApiService.makeToolCall).not.toHaveBeenCalled();
      expect(toolCallStartedSpy).not.toHaveBeenCalled();
    });
  });
});
