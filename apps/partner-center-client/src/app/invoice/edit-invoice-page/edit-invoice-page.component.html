<glxy-page [isLoading]="loading$$ | async">
  <glxy-page-toolbar *ngIf="invoice$ | async as invoice">
    <glxy-page-nav>
      <glxy-page-nav-button
        [previousPageTitle]="'BILLING.INVOICES_PAGE.PAGE_TITLE' | translate"
        [useHistory]="true"
        [historyBackButtonTitle]="'COMMON.ACTION_LABELS.BACK' | translate"
      ></glxy-page-nav-button>
    </glxy-page-nav>
    <glxy-page-title>
      {{ 'BILLING.INVOICE_PAGE.PAGE_TITLE' | translate }} {{ invoice.number | padInvoiceNumber }}
      <glxy-badge *ngIf="invoice | statusBadgePipe as statusBadge" [color]="statusBadge.glxyBadgeColor">
        {{ statusBadge.text }}
      </glxy-badge>
    </glxy-page-title>
    <glxy-page-actions class="row header-container">
      <div class="col col-xs-12 col-sm-3 last-saved-container">
        <div class="spinner-container" *ngIf="(isSaving$ | async) === true">
          <mat-spinner diameter="16"></mat-spinner>
        </div>
        <div [title]="invoice.updated">
          {{
            (isSaving$ | async) === true
              ? ('BILLING.INVOICE_PAGE.SAVING_INVOICE' | translate)
              : ('BILLING.INVOICE_PAGE.INVOICE_SAVED' | translate: { timeAgo: invoice.updated | fromNow })
          }}
        </div>
      </div>
      <div class="col col-xs-12 col-sm-3 button-container">
        <!-- Preview invoice -->
        @if (invoice.status === invoiceStatus.DRAFT || invoice.status === invoiceStatus.DUE) {
          <button
            mat-stroked-button
            color="secondary"
            [disabled]="(isSaving$ | async) === true || previewLoading() === true"
            (click)="preview()"
          >
            <glxy-button-loading-indicator [isLoading]="previewLoading()">
              {{ 'BILLING.INVOICE_PAGE.PREVIEW' | translate }}
            </glxy-button-loading-indicator>
          </button>
        }
        <ng-container *ngIf="invoice.status !== invoiceStatus.VOID">
          <span
            [matTooltip]="'BILLING.INVOICE_PAGE.CHARGE_INVOICE_TOOLTIP' | translate"
            [matTooltipDisabled]="!!invoice.defaultPaymentMethod && !!invoice.contactId && hasLineItems"
          >
            <button
              mat-flat-button
              color="primary"
              *ngIf="
                invoice.collectionMethod === collectionMethod.CHARGE_AUTOMATICALLY &&
                invoice.status !== invoiceStatus.PAID
              "
              (click)="charge()"
              [disabled]="
                !invoice.defaultPaymentMethod ||
                !invoice.contactId ||
                !hasLineItems ||
                (isSaving$ | async) === true ||
                chargeLoading() === true
              "
            >
              <glxy-button-loading-indicator [isLoading]="chargeLoading()">{{
                'BILLING.INVOICE_PAGE.CHARGE_INVOICE' | translate
              }}</glxy-button-loading-indicator>
            </button>
          </span>
          <!--Send invoice-->
          <button
            mat-flat-button
            color="primary"
            [disabled]="(isSaving$ | async) === true || !invoice.contactId"
            *ngIf="
              (invoice.collectionMethod !== collectionMethod.CHARGE_AUTOMATICALLY &&
                (invoice.status === invoiceStatus.DUE || invoice.status === invoiceStatus.DRAFT)) ||
              invoice.status === invoiceStatus.PAID
            "
            (click)="send()"
          >
            {{ 'BILLING.INVOICE_PAGE.SEND_INVOICE' | translate }}
          </button>
          <button mat-icon-button color="primary" [matMenuTriggerFor]="actionMenu">
            <mat-icon>more_vert</mat-icon>
          </button>
          <mat-menu #actionMenu="matMenu">
            @if (invoice.status === invoiceStatus.PAID) {
              <button
                mat-menu-item
                [disabled]="(isSaving$ | async) === true"
                (click)="generateAndDownloadPDF(InvoicePDFType.RECEIPT)"
              >
                {{ 'BILLING.INVOICE_PAGE.DOWNLOAD_RECEIPT' | translate }}
              </button>
            }
            <button
              mat-menu-item
              [disabled]="(isSaving$ | async) === true"
              (click)="generateAndDownloadPDF(InvoicePDFType.INVOICE)"
            >
              {{ 'BILLING.INVOICE_PAGE.DOWNLOAD_INVOICE' | translate }}
            </button>
            @if (invoice.status === invoiceStatus.DUE) {
              <button mat-menu-item (click)="changeInvoiceStatus()" [disabled]="(isSaving$ | async) === true">
                {{ 'BILLING.INVOICE_PAGE.CHANGE_STATUS' | translate }}
              </button>
            }
            @if (invoice.status === invoiceStatus.DRAFT) {
              <button mat-menu-item (click)="delete()" [disabled]="(isSaving$ | async) === true">
                {{ 'BILLING.INVOICE_PAGE.DELETE_DRAFT' | translate }}
              </button>
            }
            <button mat-menu-item (click)="duplicateInvoice()" [disabled]="(isSaving$ | async) === true">
              {{ 'BILLING.INVOICE_PAGE.DUPLICATE_INVOICE' | translate }}
            </button>
            @if (invoice.status !== invoiceStatus.DRAFT) {
              <button mat-menu-item (click)="copyPaymentLink()" [disabled]="(isSaving$ | async) === true">
                {{ 'BILLING.INVOICE_PAGE.COPY_PAYMENT_LINK' | translate }}
              </button>
            }
            @if (invoice.status === invoiceStatus.DUE || invoice.status === invoiceStatus.PAID) {
              <button
                mat-menu-item
                (click)="navToCreateCreditNote()"
                [disabled]="(isSaving$ | async) === true || invoice.lastPaymentStatus === paymentStatus.PENDING"
              >
                {{ 'BILLING.INVOICE_PAGE.ISSUE_CREDIT_NOTE' | translate }}
              </button>
            }
          </mat-menu>
        </ng-container>
      </div>
    </glxy-page-actions>
  </glxy-page-toolbar>

  <glxy-page-wrapper [widthPreset]="'wide'">
    <ng-container *ngIf="(loading$$ | async) === false">
      <ng-container *ngIf="(errorCode$$ | async) === null; else errorState">
        <ng-container
          *ngIf="{
            partnerId: partnerId$ | async,
            marketId: marketId$ | async,
            invoice: invoice$ | async,
            isDraft: isDraft$ | async,
            isDraftOrDue: isDraftOrDue$ | async,
            retailStatus: retailStatus$ | async,
            retailProvider: retailProvider$ | async,
          } as invoiceData"
        >
          <ng-container
            *ngIf="
              invoiceData.partnerId && invoiceData.invoice && invoiceData.retailStatus && invoiceData.retailProvider
            "
          >
            <app-pc-invoice-header
              class="invoice-header"
              (emitSelectRecipient)="selectRecipient($event)"
              (emitSelectAdditionalRecipients)="selectAdditionalRecipients($event)"
              (valid)="setValidity($event)"
              (addRecipientDialogOpen)="presentAddRecipientsDialog()"
              [invoice]="invoiceData.invoice"
              [isDraft]="isDraft$ | async"
              [updatingContact]="updatingContact$ | async"
              [updatingAdditionalRecipients]="updatingAdditionalRecipients$ | async"
              [invoiceRecipients]="invoiceRecipients$$ | async"
              [loadingRecipients]="loadingRecipients$ | async"
              [recipients]="recipients$ | async"
            ></app-pc-invoice-header>
            <mat-expansion-panel *ngIf="invoiceData?.invoice?.notes?.length > 0">
              <mat-expansion-panel-header>
                <mat-panel-title class="internal-notes-title">
                  {{ 'BILLING.INVOICE_PAGE.INTERNAL_NOTES_TITLE' | translate }}
                </mat-panel-title>
              </mat-expansion-panel-header>
              <mat-list class="notes" role="list" *ngFor="let note of invoiceData.invoice.notes">
                <mat-list-item role="listitem">{{ note.message }}</mat-list-item>
              </mat-list>
            </mat-expansion-panel>
            <mat-card appearance="outlined">
              <mat-card-content class="invoice-card">
                <billing-line-item-form
                  [merchantId]="invoiceData.partnerId"
                  [marketId]="invoiceData.marketId"
                  [customerId]="invoiceData.invoice.customerId"
                  [invoiceId]="invoiceId$ | async"
                  [canEdit]="isDraft$ | async"
                  [hasPackageRetailPriceAccess]="true"
                  [hasServicePeriodAccess]="hasServicePeriodAccess$ | async"
                  (itemsChange)="itemsUpdated($event)"
                ></billing-line-item-form>
              </mat-card-content>
            </mat-card>
            @if (appliedCreditNoteIds$ | async; as creditNoteIds) {
              <mat-card appearance="outlined" class="table-card">
                <mat-card-header>
                  <mat-card-title> {{ 'BUSINESS.CREDIT_NOTES' | translate }} </mat-card-title>
                </mat-card-header>
                <mat-card-content class="credit-note-card-content">
                  <billing-credit-note-list
                    class="credit-note-table"
                    [creditNoteIds]="creditNoteIds"
                    [columnsToDisplay]="creditNoteColumnsToDisplay"
                    [displayTableContentsOnly]="true"
                  ></billing-credit-note-list>
                </mat-card-content>
              </mat-card>
            }

            @if (invoiceData.invoice.lastPaymentStatus !== '') {
              <mat-card appearance="outlined" class="table-card">
                <mat-card-header>
                  <mat-card-title>{{ 'BUSINESS.PAYMENTS' | translate }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  @if (paymentFacilitatorError$$ | async; as paymentFacilitatorError) {
                    <div class="error-state-container">
                      <billing-payment-facilitator-error-state
                        class="error-state"
                        [error]="paymentFacilitatorError"
                      ></billing-payment-facilitator-error-state>
                    </div>
                  } @else {
                    <app-payments-table
                      [merchantId]="partnerId$ | async"
                      [displayTableContentsOnly]="true"
                      [filters]="{
                        invoiceId: invoiceData.invoice.id,
                      }"
                      [displayedColumns]="['description', 'created', 'payment-method', 'status', 'amount']"
                    ></app-payments-table>
                  }
                </mat-card-content>
              </mat-card>
            }
            <app-pc-invoice-footer
              (valid)="setValidity($event)"
              [invoice]="invoiceData.invoice"
              [partnerId]="invoiceData.partnerId"
              [isDraft]="invoiceData.isDraft"
              [isDraftOrDue]="invoiceData.isDraftOrDue"
              [retailStatus]="invoiceData.retailStatus"
              [retailProvider]="invoiceData.retailProvider"
            ></app-pc-invoice-footer>
          </ng-container>
        </ng-container>
      </ng-container>
      <ng-template #errorState>
        <mat-card appearance="outlined">
          <glxy-empty-state [size]="'small'">
            <glxy-empty-state-hero>
              <mat-icon>receipt</mat-icon>
            </glxy-empty-state-hero>
            <glxy-empty-state-title>
              {{ 'BILLING.INVOICE_PAGE.ERROR_TITLE' | translate }}
            </glxy-empty-state-title>
            <p *ngIf="(errorCode$$ | async) === 404">
              {{ 'BILLING.INVOICE_PAGE.NOT_FOUND' | translate }}
            </p>
            <p *ngIf="(errorCode$$ | async) !== 404">
              {{ 'BILLING.INVOICE_PAGE.GENERAL_ERROR' | translate }}
            </p>
            <glxy-empty-state-actions>
              <a mat-stroked-button routerLink="/invoices">
                {{ 'BILLING.INVOICES_PAGE.PAGE_TITLE' | translate }}
              </a>
            </glxy-empty-state-actions>
          </glxy-empty-state>
        </mat-card>
      </ng-template>
    </ng-container>
  </glxy-page-wrapper>
</glxy-page>
