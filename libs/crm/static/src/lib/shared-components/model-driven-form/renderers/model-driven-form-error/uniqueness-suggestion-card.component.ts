import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnChanges, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CrmObjectInterface } from '@vendasta/crm';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, switchMap, withLatestFrom } from 'rxjs/operators';
import { ValidatorFactoryService } from '../../validator-factory.service';
import { CrmInjectionToken, CrmDependencies, ObjectType, CardData } from '../../../../tokens-and-interfaces';
import { PAGE_ROUTES } from '../../../../constants';
import { TranslateByObjectTypePipe } from '../../../../i18n/translate-by-object-type/translate-by-object-type.pipe';
import {
  CrmFieldService,
  StandardExternalIds,
  StandardIds,
} from '../../../../shared-services/crm-services/field.service';
import { AssociationCardComponent } from '../../../association-panel-components/association-card/association-card.component';
import { CrmObjectDisplayService, CrmObjectService } from '../../../../shared-services';
import { formatAsReadonlyPhone, parsePhoneNumber } from '../phone/utils';

@Component({
  selector: 'crm-uniqueness-suggestion-card',
  templateUrl: './uniqueness-suggestion-card.component.html',
  styleUrls: ['./uniqueness-suggestion-card.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    TranslateByObjectTypePipe,
    GalaxyLoadingSpinnerModule,
    MatButtonModule,
    MatIconModule,
    AssociationCardComponent,
  ],
})
export class CrmUniquenessSuggestionCardComponent implements OnChanges {
  @Input({ required: true }) displayCard: boolean;
  @Input({ required: true }) objectType: ObjectType;
  @Input({ required: true }) fieldId: string;
  @Input() label: string;
  @Output() displayError = new EventEmitter<boolean>(true);

  permissionDenied$$ = new BehaviorSubject<boolean>(false);
  private uniquenessDetailsForField$ = this.validatorFactory.latestValidationErrors$$.pipe(
    map((validation) => {
      const uniquenessDetailsFound = (validation?.errors ?? []).filter(
        (e) => e.details?.uniquenessCheck?.fieldId === this.fieldId,
      );
      if (uniquenessDetailsFound.length > 0) {
        return uniquenessDetailsFound[0];
      }
      return null;
    }),
  );

  ngOnChanges(): void {
    this.permissionDenied$$.next(false);
    this.displayError.emit(true);
  }

  private crmObject$ = this.uniquenessDetailsForField$.pipe(
    map((uniquenessDetails) => {
      const existingObjectIds = uniquenessDetails?.details?.uniquenessCheck?.existingCrmObjectIds;
      if (!existingObjectIds?.length) {
        return '';
      }
      return existingObjectIds[0];
    }),
    switchMap((objectId) => {
      if (!objectId) {
        return [];
      }
      return this.objectService.getMultiObject(this.objectType, [objectId]);
    }),
    map((response) => (response.crmObjects ? response.crmObjects[0] : { fields: [] })),
    catchError((error) => {
      if (error.status === 403) {
        this.permissionDenied$$.next(true);
        this.displayError.emit(false);
        return of({} as CrmObjectInterface);
      } else {
        console.error(error);
        this.snackbar.openErrorSnack('ERRORS.GENERIC_MESSAGE');
        return of({} as CrmObjectInterface);
      }
    }),
  );
  objectCardInfo$: Observable<CardData | null> = this.crmObject$.pipe(
    switchMap((crmObject) => {
      return this.objectCardLink$.pipe(
        map((link) => {
          switch (this.objectType) {
            case 'Contact':
              return this.buildCardForContact(crmObject, link);
            case 'Company':
              return this.buildCardForCompany(crmObject, link);
            default:
              console.warn('Unsupported object type for uniqueness suggestion card');
              return null;
          }
        }),
      );
    }),
  );
  objectCardLink$: Observable<string> = this.crmObject$.pipe(
    withLatestFrom(this.config.routePrefix$),
    map(([{ crmObjectId }, routePrefix]) => {
      let templateLink = '';
      switch (this.objectType) {
        case 'Contact':
          templateLink = `${routePrefix}/${PAGE_ROUTES.CONTACT.ROOT}/${PAGE_ROUTES.CONTACT.SUBROUTES.PROFILE}`;
          return templateLink.replace(':crmObjectId', crmObjectId ?? '');
        case 'Company':
          templateLink = `${routePrefix}/${PAGE_ROUTES.COMPANY.ROOT}/${PAGE_ROUTES.COMPANY.SUBROUTES.PROFILE}`;
          return templateLink.replace(':crmObjectId', crmObjectId ?? '');
        default:
          console.warn('Unsupported object type for uniqueness suggestion card');
          return '';
      }
    }),
  );

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly validatorFactory: ValidatorFactoryService,
    private readonly objectService: CrmObjectService,
    private readonly crmFieldService: CrmFieldService,
    private readonly objectDisplayService: CrmObjectDisplayService,
    private readonly snackbar: SnackbarService,
  ) {}

  private buildCardForContact(crmObject: CrmObjectInterface, profileButtonUrl: string): CardData {
    const email = this.crmFieldService.getFieldId(StandardExternalIds.Email);
    const emailData = crmObject.fields?.find((f) => f.fieldId === email);
    const emailValue = emailData?.stringValue || '';
    const phone = this.crmFieldService.getFieldId(StandardExternalIds.PhoneNumber);
    const phoneData = crmObject.fields?.find((f) => f.fieldId === phone);
    let phoneValue = '';
    if (phoneData?.phoneFieldValues) {
      phoneValue = formatAsReadonlyPhone(phoneData.phoneFieldValues);
    }
    if (phoneData?.stringValue) {
      phoneValue = formatAsReadonlyPhone(parsePhoneNumber(phoneData?.stringValue));
    }
    return {
      type: 'user',
      userName: this.objectDisplayService.getObjectDisplayName(this.objectType, crmObject),
      email: emailValue,
      phone: phoneValue,
      profileButtonUrl: profileButtonUrl,
    } as CardData;
  }

  private buildCardForCompany(crmObject: CrmObjectInterface, profileButtonUrl: string): CardData {
    const companyNameData = crmObject.fields?.find((f) => f.fieldId === StandardIds.CompanyName);
    const companyNameValue = companyNameData?.stringValue || '';

    return {
      type: 'business',
      businessName: companyNameValue,
      profileButtonUrl: profileButtonUrl,
    } as CardData;
  }
}
