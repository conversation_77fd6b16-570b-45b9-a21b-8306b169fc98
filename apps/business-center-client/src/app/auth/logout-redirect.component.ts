import { Component, inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { PushNotifications } from '@capacitor/push-notifications';
import { StorageBackend } from '@openid/appauth';
import { MOBILE_PARTNER_ID, MOBILE_USER_EMAIL, REGISTRATION_CACHE_KEY } from '../constants';

@Component({
  selector: 'app-logout-redirect',
  template: `
    <glxy-empty-state>
      <p>Logging out...</p>
    </glxy-empty-state>
  `,
  styles: [
    `
      :host {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100vw;
      }
    `,
  ],
  imports: [GalaxyEmptyStateModule, MatButtonModule, MatIconModule],
})
export default class LogoutComponent {
  private readonly storageBackend = inject(StorageBackend);

  constructor() {
    this.cleanupSession();
  }

  async cleanupSession() {
    await PushNotifications.unregister();
    await this.storageBackend.removeItem(REGISTRATION_CACHE_KEY);
    await this.storageBackend.removeItem('token_response');
    localStorage.removeItem(MOBILE_PARTNER_ID);
    localStorage.removeItem(MOBILE_USER_EMAIL);
    window.location.href = '/';
  }
}
