<div>
  <h1 mat-dialog-title class="dialog-title">
    <mat-icon color="primary">info</mat-icon>
    <b class="dialog-title-content">{{ titleKey | translate }}</b>
  </h1>
</div>

<div mat-dialog-content class="dialog-content">
  <p class="dialog-content">
    {{ 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_CONTENT' | translate }}
  </p>
</div>

<mat-dialog-actions align="end">
  <button mat-stroked-button (click)="onCancelDisconnect()">
    {{ 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_CANCEL' | translate }}
  </button>

  <button mat-flat-button color="primary" (click)="onConfirmDisconnect()" [disabled]="working">
    <span [ngClass]="{ loading: working }">{{ disconnectLabel | translate }}</span>
    <div class="spinner-container" *ngIf="working">
      <mat-spinner [diameter]="20"></mat-spinner>
    </div>
  </button>
</mat-dialog-actions>
