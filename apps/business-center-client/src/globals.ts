const PROD_PRODUCT_IDS = {
  advertisingIntelligence: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
  socialMarketing: 'SM',
  listingBuilder: 'MS',
  reputationManagement: 'RM',
  websitePro: 'MP-ee4ea04e553a4b1780caf7aad7be07cd',
  customerVoice: 'MP-c4974d390a044c28aec31e421aa662b2',
  localAds: 'MP-SPFJ58V2NJHN8Z542JM76XR4G4QW62DG',
  inboxPro: 'MP-********************************',
  inboxExp: 'MP-********************************',
  aiWebchat: 'MP-H58MV5X2ZXLLHCQ727JBVD8QF4T8XM5P',
  campaignsPro: 'MP-ZGJ6V4QRP77WPMDKXS6VDRNX58Q42P7P',
  yesware: 'MP-NLMSDX765QS7QVWH44VCZJ2CFGKH58W2',
  constantContact: 'MP-745V5GBKVRZ3DHP6D8P8RT58KM7SHDZS',
};

const DEMO_PRODUCT_IDS = {
  advertisingIntelligence: 'MP-94072e44d5364872b672d7ab4fc7a7e8',
  socialMarketing: 'SM',
  listingBuilder: 'MS',
  reputationManagement: 'RM',
  websitePro: 'MP-9cc9f21f0a234a46ad78087fc09f16bc',
  customerVoice: 'MP-fba21121b71148c9bb33e11fcd92d520',
  inboxPro: 'MP-********************************',
  inboxExp: 'MP-********************************',
  aiWebchat: 'MP-MWXQBQGNVQHQ3J4NKS76MWG6S452D6G7',
  campaignsPro: 'MP-WF5KS7FHTGV4LR5DBQ4D6XKCCDB5B6G7',
  yesware: 'MP-TQ8X7CVNXV752KGXB4XVRJR3BT5SVMMS',
  constantContact: 'MP-TCG3QC4PZJTVV46QVRT342JHQ64XBL8Z',
};

const PROD_EDITION_IDS = {
  reputationManagementPremium: 'EDITION-JFRPLQPN',
};

const DEMO_EDITION_IDS = {
  reputationManagementPremium: 'EDITION-BFXF8W8Q',
};

const PROD_ADDON_IDS = {
  citationBuilder: ['A-GMXXNQ4ZGD', 'A-SX5MP2FB2L'], // yearly, monthly
};

const DEMO_ADDON_IDS = {
  citationBuilder: ['A-TWFC53JDQX', 'A-FR5P5ND7KN'], // yearly, monthly
};

export const GOOGLE_MAPS_API_KEY = 'AIzaSyDqWGgrDbTRHlVwo_ViFD05agVFY7Do7zI';

export const environment: string = globalThis.environment || 'prod';
export const deployment: string = globalThis.deployment || null;
export const partnerId: string = globalThis.partnerId?.toUpperCase() || null;
export const multiPartnerApps: string[] = globalThis.multiPartnerApps || [];
export const isGreyLabel: boolean = globalThis.isGreyLabel || false;
export const devServer: boolean = globalThis.devServer || false;
export const productIds: { [key: string]: string } = environment === 'demo' ? DEMO_PRODUCT_IDS : PROD_PRODUCT_IDS;
export const editionIds: { [key: string]: string } = environment === 'demo' ? DEMO_EDITION_IDS : PROD_EDITION_IDS;
export const addOnIds: { [key: string]: string[] } = environment === 'demo' ? DEMO_ADDON_IDS : PROD_ADDON_IDS;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const wootric: any = globalThis.wootric || null;
