import { GreyLabelService } from './grey-label.service';
import { UserService } from '@vendasta/business-center';
import { of } from 'rxjs';
import { MOBILE_PARTNER_ID } from '../constants';
import { NamespaceDetails } from '@vendasta/business-center/lib/_internal';
import { TestBed } from '@angular/core/testing';

describe('GreyLabelService', () => {
  let service: GreyLabelService;
  let userServiceMock: UserService;
  let originalLocationHref: string;

  beforeEach(() => {
    // Mock UserService
    userServiceMock = {
      requestShortOtpCode: jest.fn().mockReturnValue(of({})),
      listNamespaceDetails: jest.fn().mockReturnValue(of({ namespaces: [] })),
    } as unknown as UserService;

    // Save original window.location.href
    originalLocationHref = window.location.href;

    // Mock window.location.href (not directly mutable)
    Object.defineProperty(window, 'location', {
      value: {
        href: originalLocationHref,
      },
      writable: true,
    });

    // Set up spies for localStorage
    jest.spyOn(window.localStorage, 'setItem');
    jest.spyOn(window.localStorage, 'removeItem');

    // Configure TestBed with our service and mocks
    TestBed.configureTestingModule({
      providers: [GreyLabelService, { provide: UserService, useValue: userServiceMock }],
    });

    // Get the service from the TestBed
    service = TestBed.inject(GreyLabelService);
  });

  afterEach(() => {
    jest.clearAllMocks();

    // Restore window.location.href
    Object.defineProperty(window, 'location', {
      value: {
        href: originalLocationHref,
      },
      writable: true,
    });
  });

  describe('sendOTP', () => {
    it('should throw an error if no email is provided', async () => {
      await expect(service.sendOTP('')).rejects.toThrow('Please enter your email address.');
    });

    it('should set email, reset namespace details, and call requestShortOtpCode', async () => {
      const email = '<EMAIL>';

      await service.sendOTP(email);

      expect(service.userEmail()).toBe(email);
      expect(service.namespaceDetails()).toEqual([]);
      expect(userServiceMock.requestShortOtpCode).toHaveBeenCalledWith({ email });
    });
  });

  describe('resendOTP', () => {
    it('should reset namespace details and call sendOTP with current email', async () => {
      // Set up initial state
      await service.sendOTP('<EMAIL>');
      jest.clearAllMocks();

      await service.resendOTP();

      expect(service.namespaceDetails()).toEqual([]);
      expect(userServiceMock.requestShortOtpCode).toHaveBeenCalledWith({
        email: '<EMAIL>',
      });
    });
  });

  describe('loadNamespacesForOTP', () => {
    it('should throw an error if OTP is not valid', async () => {
      await expect(service.loadNamespacesForOTP('')).rejects.toThrow('Please enter a valid 6-digit code.');
      await expect(service.loadNamespacesForOTP('12345')).rejects.toThrow('Please enter a valid 6-digit code.');
    });

    it('should throw an error if no namespaces are found', async () => {
      // Set up initial state
      await service.sendOTP('<EMAIL>');
      jest.clearAllMocks();

      // Mock empty namespaces response
      userServiceMock.listNamespaceDetails = jest.fn().mockReturnValue(of({ namespaces: [] }));

      await expect(service.loadNamespacesForOTP('123456')).rejects.toThrow('No namespaces found for the provided OTP.');

      expect(userServiceMock.listNamespaceDetails).toHaveBeenCalledWith({
        email: '<EMAIL>',
        otpCode: '123456',
      });
    });

    it('should set namespaces when OTP is valid and namespaces exist', async () => {
      // Set up initial state
      await service.sendOTP('<EMAIL>');

      // Mock namespaces response
      const mockNamespaces: NamespaceDetails[] = [
        { id: 'namespace1', name: 'Namespace 1' } as unknown as NamespaceDetails,
        { id: 'namespace2', name: 'Namespace 2' } as unknown as NamespaceDetails,
      ];
      userServiceMock.listNamespaceDetails = jest.fn().mockReturnValue(of({ namespaces: mockNamespaces }));

      await service.loadNamespacesForOTP('123456');

      expect(userServiceMock.listNamespaceDetails).toHaveBeenCalledWith({
        email: '<EMAIL>',
        otpCode: '123456',
      });
      expect(service.namespaceDetails()).toEqual(mockNamespaces);
    });
  });

  describe('navigateToProvider', () => {
    it('should set provider ID in localStorage and navigate to landing page', () => {
      // Set up initial state
      service['_userEmail'].set('<EMAIL>');

      service.navigateToProvider('ABC123');

      expect(localStorage.setItem).toHaveBeenCalledWith(MOBILE_PARTNER_ID, 'ABC123');
      expect(window.location.href).toBe('/landing?email=test%40example.com&autoPromptLogin=false');
    });

    it('should include autoPromptLogin parameter when true', () => {
      // Set up initial state
      service['_userEmail'].set('<EMAIL>');

      service.navigateToProvider('ABC123', true);

      expect(localStorage.setItem).toHaveBeenCalledWith(MOBILE_PARTNER_ID, 'ABC123');
      expect(window.location.href).toBe('/landing?email=test%40example.com&autoPromptLogin=true');
    });
  });

  describe('resetProvider', () => {
    it('should remove provider ID from localStorage and navigate to landing page', () => {
      service.resetProvider();

      expect(localStorage.removeItem).toHaveBeenCalledWith(MOBILE_PARTNER_ID);
      expect(window.location.href).toBe('/landing');
    });
  });
});
