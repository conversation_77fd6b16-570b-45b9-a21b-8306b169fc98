<integrations-connect-to-service [service]="meet" [connectionInfoKey]="serviceKey" [useAlternateStyling]="true">
  <integrations-connect-element>
    <ng-container [ngTemplateOutlet]="googleButton" [ngTemplateOutletContext]="{ label: signInText }"></ng-container>
  </integrations-connect-element>
  <!-- Copied from default disconnect button in @vendasta/integrations npm package -->
  <integrations-disconnect-element *ngIf="primaryDisconnect">
    <ng-container
      [ngTemplateOutlet]="googleButton"
      [ngTemplateOutletContext]="{ label: disconnectText }"
    ></ng-container>
  </integrations-disconnect-element>
</integrations-connect-to-service>

<ng-container
  *ngIf="!primaryDisconnect && isConnected$ | async"
  [ngTemplateOutlet]="googleButtonSecondaryDisconnect"
></ng-container>

<ng-template #googleButtonSecondaryDisconnect>
  <integrations-connect-to-service [service]="meet" [connectionInfoKey]="serviceKey" [useAlternateStyling]="true">
    <integrations-disconnect-element>
      <ng-container [ngTemplateOutlet]="googleDisconnectButton"></ng-container>
    </integrations-disconnect-element>
  </integrations-connect-to-service>
</ng-template>

<ng-template #googleButton let-label="label" let-cursor="cursor">
  <button
    class="google-button-container"
    matStepperNext
    [disabled]="!primaryDisconnect && (isConnected$ | async)"
    [ngStyle]="{ cursor: cursor || 'pointer' }"
  >
    <div class="google-icon-container">
      <img
        class="google-icon"
        src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/google_logo.svg"
      />
    </div>
    <div class="sign-in-text">
      <span>{{ label }}</span>
    </div>
  </button>
</ng-template>
<ng-template #googleDisconnectButton>
  <button mat-stroked-button class="secondary-disconnect" matStepperNext (click)="openDisconnectDialog($event)">
    <div class="google-icon-container">
      <img
        class="google-icon"
        src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/google_logo.svg"
      />
    </div>
    <div class="disconnect-text">
      <span>{{ disconnectSecondaryText }}</span>
    </div>
  </button>
</ng-template>
