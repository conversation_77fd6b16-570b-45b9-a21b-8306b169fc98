import { <PERSON>a, StoryObj, moduleMetadata, componentWrapperDecorator, AngularRenderer } from '@storybook/angular';
import { DecoratorFunction } from '@storybook/types';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { Component, Inject, Optional, inject } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogConfig, MatDialogModule } from '@angular/material/dialog';
import * as i18n from '../../assets/i18n/en_devel.json';
import { TermsOfServiceData } from './interface/terms-of-service-data.interface';
import { TermsOfServiceDialogComponent } from './terms-of-service.component';
import { complexTosHTMLJsonEscaped } from './complex_tos_example';
import { tosWithVariables } from './olst_tos_with_variables';
import { tosWithoutVariables } from './olst_tos_without_variables';
import { olst2024Example } from './olst_2024_example';
import { olstJune2025Example } from './olst_june_2025_example';

const TOS_TOKEN = 'TOS_TOKEN';
const PARTNER_NAME_TOKEN = 'PARTNER_NAME';

@Component({
  template: ` <button mat-stroked-button type="button" color="primary" (click)="launch()">Launch</button> `,
  imports: [MatButtonModule, MatDialogModule, TermsOfServiceDialogComponent],
})
export class LaunchComponent {
  private readonly bpObserver = inject(BreakpointObserver);

  constructor(
    private readonly dialog: MatDialog,
    @Inject(TOS_TOKEN) private tos: string,
    @Optional() @Inject(PARTNER_NAME_TOKEN) private partnerName: string,
  ) {}

  launch(): void {
    // this is attempting to match parent logic snippet referenced below
    // https://github.com/vendasta/galaxy/blob/06b1c0aa78bdab5818af7d40f53743586ea69d99/apps/business-center-client/src/app/dashboard/dashboard.component.ts#L286-L299
    const data = {} as TermsOfServiceData;
    data.termsOfService = this.tos;
    if (this.partnerName) {
      data.partner = { name: this.partnerName };
    }
    const conf: MatDialogConfig = {
      width: '600px',
      disableClose: true,
      data,
    };
    const isSmallScreen = this.bpObserver.isMatched(Breakpoints.XSmall);
    if (isSmallScreen) {
      conf.maxWidth = '100vw';
      conf.maxHeight = '100vh';
    }
    this.dialog.open(TermsOfServiceDialogComponent, conf);
  }
}

function withDecorators(o: {
  tosOverride?: string;
  tosPDFOverride?: string;
  partnerNameOverride?: string;
}): DecoratorFunction<AngularRenderer, LaunchComponent>[] {
  const tosTok = { provide: TOS_TOKEN, useValue: o.tosOverride };
  const providers = [tosTok];
  if (o.partnerNameOverride) {
    providers.push({ provide: PARTNER_NAME_TOKEN, useValue: o.partnerNameOverride });
  }
  return [
    moduleMetadata({
      imports: [LaunchComponent, TranslateTestingModule.withTranslations('en', i18n)],
      providers: providers,
    }),
    componentWrapperDecorator((story) => `<div style="margin: 3em">${story}</div>`),
  ];
}

const meta: Meta<LaunchComponent> = {
  title: 'Terms of Service Modal',
  component: LaunchComponent,
};

export default meta;

type Story = StoryObj<LaunchComponent>;

export const Default: Story = {
  decorators: withDecorators({ tosOverride: complexTosHTMLJsonEscaped }),
};

export const WithoutVariables: Story = {
  decorators: withDecorators({ tosOverride: tosWithoutVariables }),
};

export const ReplaceVariables: Story = {
  // not used in production yet for dashboard TOS in business app but we could get the partner name and support changes to the TOS
  // Providing a partner name will leverage the existing logic to include the partner name in the TOS
  // https://github.com/vendasta/galaxy/blob/93bb9693e94de71f66a57bc0211f07e052de399b/apps/business-center-client/src/app/terms-of-service/terms-of-service.component.ts#L24-L26
  decorators: withDecorators({ tosOverride: tosWithVariables, partnerNameOverride: 'Morning Moon' }),
};

// taken from https://vstore-prod.vendasta-internal.com/namespace/partner/table/TermsOfService/entity/OLST%251FNULL%251F1708041780
export const Olst2024: Story = {
  decorators: withDecorators({ tosOverride: olst2024Example }),
};

export const OlstJune2025Example: Story = {
  decorators: withDecorators({ tosOverride: olstJune2025Example }),
};
