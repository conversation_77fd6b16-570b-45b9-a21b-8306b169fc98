import { Injectable } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import {
  GetTermsOfServiceResponse,
  GetUserAgreementTermsOfServiceResponse,
  PartnerTermsOfApiService,
  PartnerUserAgreementTermsOfApiService,
  TermsOfService,
  UserAgreementTermsOfService,
} from '@galaxy/partner';
import { Observable, combineLatest, of } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { partnerId } from '../../../globals';
import { AccountGroupService } from '../../account-group';
import { AuthService } from '../../auth.service';
import { FeatureFlagService } from '../../core/feature-flag.service';

@Injectable({ providedIn: 'root' })
export class TermsOfServiceService {
  private readonly accountGroup$ = this.accountGroupService.currentAccountGroup$;
  private readonly featureFlags$ = this.accountGroup$.pipe(
    switchMap((ag) =>
      this.featureFlagService.checkFeatureFlagsMulti(ag.partnerId, ag.marketId, ['terms_of_service_pop_up']),
    ),
  );
  private readonly isImpersonating$ = toObservable(this.authService.isImpersonating);

  readonly showTermsOfService$ = combineLatest([this.isImpersonating$, this.featureFlags$]).pipe(
    map(([isImpersonating, featureFlag]) => !isImpersonating && featureFlag['terms_of_service_pop_up']),
  );

  constructor(
    private accountGroupService: AccountGroupService,
    private featureFlagService: FeatureFlagService,
    private partnerTermsOfApiService: PartnerTermsOfApiService,
    private partnerUserAgreementTermsOfApiService: PartnerUserAgreementTermsOfApiService,
    private authService: AuthService,
  ) {}

  getTermsOfService(termsOfService: {
    partnerId?: string;
    marketId?: string;
    feature?: string;
  }): Observable<GetTermsOfServiceResponse> {
    if (termsOfService.marketId === undefined || termsOfService.marketId === '') {
      termsOfService.marketId = 'NULL';
    }

    if (termsOfService.partnerId === undefined || termsOfService.partnerId === '') {
      termsOfService.partnerId = partnerId;
    }

    return this.partnerTermsOfApiService.getTermsOfService(termsOfService);
  }

  getUserAgreement(userAgreement: {
    partnerId?: string;
    userId?: string;
    feature: string;
  }): Observable<GetUserAgreementTermsOfServiceResponse> {
    if (userAgreement.partnerId === undefined || userAgreement.partnerId === '') {
      userAgreement.partnerId = partnerId;
    }

    if (userAgreement.userId === undefined || userAgreement.userId === '') {
      userAgreement.userId = this.authService.userId();
    }

    return this.partnerUserAgreementTermsOfApiService.getUserAgreementTermsOfService(userAgreement).pipe(
      catchError((e) => {
        console.warn(e);
        return of({} as GetUserAgreementTermsOfServiceResponse);
      }),
    );
  }

  hasTermsOfService(check: { terms: TermsOfService }): boolean {
    return !!(check.terms && check.terms.termsOfService);
  }

  userAgreementIsAccepted(check: { userAgreement: UserAgreementTermsOfService }): boolean {
    return check.userAgreement && check.userAgreement.accepted;
  }

  isNewTermsOfService(check: { userAgreement: UserAgreementTermsOfService; terms: TermsOfService }): boolean {
    return check.userAgreement && check.userAgreement.acceptedAt < check.terms.createdOn;
  }
}
