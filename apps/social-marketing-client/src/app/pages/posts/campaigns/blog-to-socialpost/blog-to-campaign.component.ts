import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, ViewChild } from '@angular/core';

import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatSidenavModule } from '@angular/material/sidenav';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConfirmationModalComponent } from '@vendasta/galaxy/confirmation-modal';
import { AIInstruction } from '../../../../core/post/post';
import { AiSettingButtonComponent } from '../../../../shared/settings-button/ai-setting-button.component';
import { ComposerModule } from './../../../../composer/composer.module';
import { BulkUploadPostsService } from './../../../../composer/components/bulk-upload/bulk-upload-posts.service';
import { BlogToCampaignService } from './blog-to-campaign-service';
import { PostCategory } from '@vendasta/social-posts';
import { UiLoaderGenerateComponent } from './../../../../../app/shared/ui-loader-generate/ui-loader-generate.component';
import { forkJoin, take } from 'rxjs';
import { SocialConnection } from '../../ai-bundle-posts/model';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Posts } from './../../../../../app/shared/dynamic-posts/dynamic-posts-models';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-blog-to-campaign',
  imports: [
    CommonModule,
    MatIcon,
    MatButtonModule,
    MatDialogModule,
    TranslateModule,
    AiSettingButtonComponent,
    ComposerModule,
    MatSidenavModule,
    MatCardModule,
    UiLoaderGenerateComponent,
  ],
  providers: [BulkUploadPostsService],
  templateUrl: './blog-to-campaign.component.html',
  styleUrl: './blog-to-campaign.component.scss',
})
export class BlogToCampaignComponent implements OnInit {
  selectedNetworks: SocialConnection[] = [];
  socialPosts: Posts[];
  generatedCampaignTitle: string;
  iscontentGenerating = true;
  @ViewChild('aiSettings') aiSettings;
  constructor(
    private modal: MatDialogRef<BlogToCampaignComponent>,
    protected dialog: MatDialog,
    private blogToCampaignService: BlogToCampaignService,
    private snackBarService: SnackbarService,
    private translateService: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(MAT_DIALOG_DATA)
    private inputData: {
      postType: PostCategory;
      selectedNetworks: SocialConnection[];
      postId: string;
      blogPostTitle: string;
    },
  ) {}
  toggleSetting(): void {
    this.aiSettings.toggle();
  }
  ngOnInit() {
    this.selectedNetworks = this.inputData?.selectedNetworks;
    forkJoin({
      postTitle: this.blogToCampaignService.generateCampaignTitle(this.inputData.blogPostTitle).pipe(take(1)),
      postData: this.blogToCampaignService
        .generateBlogPost(this.inputData?.postType, this.inputData?.selectedNetworks, this.inputData?.postId, 5)
        .pipe(take(1)),
    }).subscribe(({ postTitle, postData }) => {
      const popupSnackbar = (type: 'title' | 'content' | 'image') => {
        this.snackBarService.openErrorSnack(
          this.translateService.instant('SNACKBAR.SOCIAL_CAMPAIGN_GENERATION', { type }),
        );
        this.modal.close();
      };
      if (postTitle === null) {
        popupSnackbar('title');
        return;
      }
      if (postData?.blogContent === null) {
        popupSnackbar('content');
        return;
      }
      if (postData?.images === null) {
        popupSnackbar('image');
        return;
      }
      this.socialPosts = this.blogToCampaignService.parseGeneratedBlogPostResponse(
        this.inputData?.postType,
        postData,
        this.selectedNetworks,
      );
      this.setScheduledDates(this.socialPosts);
      this.generatedCampaignTitle = postTitle?.generatedTitle || '';
      this.iscontentGenerating = false;
    });
  }

  addAnotherPost() {
    this.blogToCampaignService
      .generateBlogPost(this.inputData?.postType, this.inputData?.selectedNetworks, this.inputData?.postId, 1)
      .pipe(take(1))
      .subscribe((postData) => {
        if (postData?.blogContent === null) {
          this.snackBarService.openErrorSnack(
            this.translateService.instant('SNACKBAR.SOCIAL_CAMPAIGN_GENERATION', { type: 'content' }),
          );
          return;
        }
        if (postData?.images === null) {
          this.snackBarService.openErrorSnack(
            this.translateService.instant('SNACKBAR.SOCIAL_CAMPAIGN_GENERATION', { type: 'image' }),
          );
          return;
        }
        const newPosts = this.blogToCampaignService.parseGeneratedBlogPostResponse(
          this.inputData?.postType,
          postData,
          this.selectedNetworks,
        );
        this.socialPosts = [...this.socialPosts, newPosts[0]];
        this.setScheduledDates(this.socialPosts);
      });
  }

  private setScheduledDates(posts: Posts[]) {
    posts.forEach((post, index) => {
      const baseDate = new Date();
      baseDate.setDate(baseDate.getDate() + index + 1); // Index + 1 to start from tomorrow
      post.postContent.forEach((content) => {
        content.scheduleDate = baseDate;
      });
    });
  }

  handleCloseCampaign(): void {
    const data = {
      type: 'confirm',
      title: 'Warning',
      message: 'CAMPAIGN.FROM_BLOG.DIALOG.CONTENT',
      hideCancel: false,
      confirmButtonText: 'CAMPAIGN.FROM_BLOG.DIALOG.GO_BACK',
      cancelButtonText: 'CAMPAIGN.FROM_BLOG.DIALOG.CANCEL',
      actionOnEnterKey: false,
      cancelOnEscapeKeyOrBackgroundClick: true,
    };
    this.dialog
      .open(ConfirmationModalComponent, {
        data: data,
        width: '540px',
        maxWidth: 'calc( 100vw - 8px )',
        autoFocus: false,
        disableClose: true,
      })
      .afterClosed()
      .subscribe((response: string) => {
        if (!response || response === data.cancelButtonText) {
          return;
        } else {
          this.modal.close();
        }
      });
    return;
  }

  onPostSubmitted(): void {
    this.modal.close();
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { view: 'scheduled' },
      queryParamsHandling: 'merge',
    });
  }

  protected readonly AIInstruction = AIInstruction;
}
