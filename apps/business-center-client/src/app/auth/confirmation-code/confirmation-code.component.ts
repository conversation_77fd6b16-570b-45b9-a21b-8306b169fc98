import { Component, inject, signal } from '@angular/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { Router, RouterModule } from '@angular/router';
import { CodeInputComponent } from '../code-input/code-input.component';
import { MatButtonModule } from '@angular/material/button';
import { GreyLabelService } from '../grey-label.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';

@Component({
  selector: 'app-confirmation-code',
  templateUrl: './confirmation-code.component.html',
  styleUrls: ['./confirmation-code.component.scss'],
  imports: [
    GalaxyPageModule,
    GalaxyFormFieldModule,
    RouterModule,
    CodeInputComponent,
    MatButtonModule,
    GalaxyButtonLoadingIndicatorModule,
    GalaxyLoadingSpinnerModule,
  ],
})
export default class ConfirmationCodeComponent {
  private readonly router = inject(Router);
  private readonly greyLabelService = inject(GreyLabelService);
  private readonly snackbar = inject(SnackbarService);

  readonly submitted = signal(false);
  readonly resendingOTP = signal(false);
  readonly email = this.greyLabelService.userEmail;

  async onSubmit(magicCode: string) {
    if (!magicCode || magicCode.length !== 6) {
      this.snackbar.openErrorSnack('Please enter a valid 6-digit code.');
      return;
    }

    this.submitted.set(true);
    try {
      await this.greyLabelService.loadNamespacesForOTP(magicCode);
    } catch (error) {
      this.snackbar.openErrorSnack('Failing to confirm email.');
      console.error('Error sending OTP:', error);
      this.submitted.set(false);
      return;
    }

    this.submitted.set(false);

    if (this.greyLabelService.namespaceDetails().length === 1) {
      const providerId = this.greyLabelService.namespaceDetails()[0].namespace;
      this.greyLabelService.navigateToProvider(providerId, true);
    } else {
      await this.router.navigate(['../provider-selection']);
    }
  }

  async resendCode() {
    this.resendingOTP.set(true);
    try {
      await this.greyLabelService.resendOTP();
      this.snackbar.openSuccessSnack('Code resent successfully.');
    } catch (error) {
      this.snackbar.openErrorSnack('Failed to resend code.');
      console.error('Error resending code:', error);
    }

    this.resendingOTP.set(false);
  }
}
