import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  WritableSignal,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import {
  CollectionMethod,
  Invoice,
  InvoicePDFType,
  InvoiceService as BillingInvoiceService,
  InvoiceStatus,
  LastPaymentStatus,
  PaymentFacilitatorError,
  PaymentService,
  RetailProvider,
  RetailStatus,
  StripeService,
} from '@galaxy/billing';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import {
  BehaviorSubject,
  combineLatest,
  EMPTY,
  firstValueFrom,
  Observable,
  of,
  ReplaySubject,
  Subscription,
} from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap, take, tap } from 'rxjs/operators';
import { EditInvoicePageService } from './edit-invoice-page.service';
import {
  SendEmailDialogComponent,
  SendEmailDialogData,
  SendEmailDialogResp,
} from './send-email-dialog/send-email-dialog.component';
import { InvoiceRecipients } from '../interface';
import { RECIPIENT_PROVIDER } from '../providers';
import { LineItem } from '@galaxy/partner-center-client/billing/static/billing-line-item/line-item-form.component';
import { Recipient, RecipientProvider } from '@galaxy/partner-center-client/billing/static/core/recipients';
import { FeatureFlags } from '../../core/features';
import { FeatureFlagService } from '@vendasta/businesses';
import { TranslateService } from '@ngx-translate/core';
import { OpenConfirmationModalService } from '@vendasta/galaxy/confirmation-modal';
import { ChangeInvoiceStatusDialogComponent } from '@vendasta/smb-invoicing';
import { CreditNoteListColumn } from '@galaxy/partner-center-client/billing/static/credit-note/credit-note-list/credit-note-list.component';
import { PARTNER_CENTER_BILLING_CONFIG_TOKEN } from '@galaxy/partner-center-client/billing/static/core';
import {
  PreviewDialogComponent,
  PreviewInvoiceDialogData,
} from '@galaxy/partner-center-client/billing/static/invoices/preview-dialog';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './edit-invoice-page.component.html',
  styleUrls: ['./edit-invoice-page.component.scss'],
  standalone: false,
})
export class EditInvoicePageComponent implements OnInit, OnDestroy {
  confirmationModal = inject(OpenConfirmationModalService);
  invoiceStatus = InvoiceStatus;
  collectionMethod = CollectionMethod;
  paymentStatus = LastPaymentStatus;

  customerService = inject(PARTNER_CENTER_BILLING_CONFIG_TOKEN).customerService;
  marketId$: Observable<string>;

  constructor(
    private translate: TranslateService,
    private route: ActivatedRoute,
    private router: Router,
    private editInvoicePageService: EditInvoicePageService,
    @Inject('PARTNER_ID') public readonly partnerId$: Observable<string>,
    private invoiceService: BillingInvoiceService,
    private paymentService: PaymentService,
    private snackbarService: SnackbarService,
    private readonly stripeService: StripeService,
    public dialog: MatDialog,
    private feature: FeatureFlagService,
    @Inject(RECIPIENT_PROVIDER) private readonly recipientProvider: RecipientProvider<Recipient>,
    private translateService: TranslateService,
  ) {}

  invoiceId$: Observable<string>;
  loadingInvoice$: Observable<boolean> = this.editInvoicePageService.loadingInvoice$;
  invoice$: Observable<Invoice> = this.editInvoicePageService.invoice$;
  isSaving$: Observable<boolean> = this.editInvoicePageService.savingInvoice$;
  updatingContact$: Observable<boolean> = this.editInvoicePageService.updatingContact$;
  updatingAdditionalRecipients$: Observable<boolean> = this.editInvoicePageService.updatingAdditionalRecipients$;
  recipients$: Observable<Recipient[]> = this.editInvoicePageService.recipients$;
  loadingRecipients$: Observable<boolean> = this.editInvoicePageService.loadingRecipients$;
  isDraft$: Observable<boolean> = this.editInvoicePageService.isDraft$;
  isDraftOrDue$: Observable<boolean> = this.editInvoicePageService.isDraftOrDue$;
  retailStatus$: Observable<RetailStatus>;
  retailProvider$: Observable<RetailProvider>;
  hasServicePeriodAccess$: Observable<boolean>;
  chargeLoading: WritableSignal<boolean> = signal(false);
  previewLoading: WritableSignal<boolean> = signal(false);

  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  errorCode$$: BehaviorSubject<number> = new BehaviorSubject<number>(null);
  paymentFacilitatorError$$: ReplaySubject<PaymentFacilitatorError> = new ReplaySubject(1);
  invoiceRecipients$$: ReplaySubject<InvoiceRecipients> = new ReplaySubject(1);

  hasLineItems = false;
  isValid: boolean;
  private subscriptions: Subscription[] = [];

  appliedCreditNoteIds$: Observable<string[]>;
  public readonly creditNoteColumnsToDisplay: CreditNoteListColumn[] = [
    CreditNoteListColumn.CREDIT_NOTE_NUMBER,
    CreditNoteListColumn.CREATED,
    CreditNoteListColumn.REASON,
    CreditNoteListColumn.AMOUNT,
  ];

  ngOnInit(): void {
    this.invoiceId$ = this.route.params.pipe(map((p) => p.id));

    this.subscriptions.push(
      this.invoiceId$.subscribe(
        (invoiceId) => this.editInvoicePageService.loadInvoice(invoiceId),
        (error) => this.errorCode$$.next(error.status),
      ),
    );

    this.marketId$ = this.invoice$.pipe(
      map((invoice) => invoice.customerId),
      switchMap((customerId) => {
        return this.customerService.getCustomer(customerId);
      }),
      map((customer) => customer?.marketId || 'default'),
    );

    this.hasServicePeriodAccess$ = combineLatest([this.partnerId$, this.marketId$]).pipe(
      switchMap(([partnerId, marketId]) =>
        this.feature.checkFeatureFlag(partnerId, marketId, FeatureFlags.BILLING_SERVICE_PERIODS),
      ),
      catchError(() => of(false)),
      shareReplay({ bufferSize: 1, refCount: true }),
    );

    this.subscriptions.push(
      combineLatest([this.hasServicePeriodAccess$, this.loadingInvoice$]).subscribe(
        ([hasServicePeriodAccess, loadingInvoice]) => this.loading$$.next(loadingInvoice && !!hasServicePeriodAccess),
      ),
    );

    this.retailStatus$ = this.partnerId$.pipe(
      switchMap((pid) => this.paymentService.retailStatus(pid)),
      catchError((err) => {
        if (err?.error?.details?.length > 0) {
          this.paymentFacilitatorError$$.next(err.error.details[0].reason);
        }
        return of({ canAcceptPayment: false });
      }),
      shareReplay(1),
    );

    this.retailProvider$ = this.partnerId$.pipe(
      switchMap((pid) => this.paymentService.getRetailProvider(pid)),
      catchError(() => of({} as RetailProvider)),
    );

    this.subscriptions.push(
      combineLatest([this.recipients$, this.invoice$])
        .pipe(
          filter(([_, invoice]) => !!invoice),
          map(([recipients, invoice]) => {
            if (invoice.status !== InvoiceStatus.DRAFT && invoice.contactId === '') {
              /*
              This conditional is necessary since there are non-draft invoices that existed before the recipient field was introduced. We need to pipe null
              through here so that we can support the 'Resend' functionality for these legacy invoices.
              */
              return null;
            }
            const additionalRecipients: Recipient[] = [];
            invoice.additionalContactIds.map((id) => {
              const recipient = recipients.find((u) => u.userID === id);
              if (recipient) {
                additionalRecipients.push(recipient);
              }
            });

            let recipient: Recipient;

            if (invoice.contactId) {
              recipient = (recipients || []).find((recipient) => recipient.userID === invoice.contactId);
            }

            return <InvoiceRecipients>{
              invoiceRecipient: recipient,
              additionalRecipients: additionalRecipients,
            };
          }),
        )
        .subscribe((recipients: InvoiceRecipients) => this.invoiceRecipients$$.next(recipients)),
    );

    this.appliedCreditNoteIds$ = this.invoice$.pipe(
      map((invoice) => {
        return invoice?.appliedCreditNotes?.map((cn) => cn.creditNoteId);
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  async send(): Promise<void> {
    if (!this.isValid) {
      this.snackbarService.openErrorSnack('INVOICE.REQUIRED_FIELDS_ERROR');
      return;
    }

    const invoice = await firstValueFrom(this.invoice$);
    if (invoice.items.length === 0) {
      this.snackbarService.openErrorSnack('INVOICE.LINE_ITEMS_REQUIRED_ERROR');
      return;
    }

    const afterClosed$: Observable<SendEmailDialogResp> = combineLatest([
      this.invoiceRecipients$$,
      this.recipients$,
      this.partnerId$,
      this.invoiceId$,
    ]).pipe(
      take(1),
      switchMap(([invoiceRecipients, allRecipients, merchantId, invoiceId]) => {
        const invoiceRecipient = invoiceRecipients?.invoiceRecipient;

        const data: SendEmailDialogData = {
          recipient: invoiceRecipient,
          additionalRecipients: invoiceRecipients?.additionalRecipients || [],
          allRecipients: allRecipients,
          merchantId: merchantId,
          invoiceId: invoiceId,
        };

        return this.dialog
          .open(SendEmailDialogComponent, {
            width: '660px',
            data: data,
          })
          .afterClosed();
      }),
    );
    combineLatest([this.partnerId$, this.invoiceId$, afterClosed$])
      .pipe(
        switchMap(([pid, id, resp]) => {
          if (!resp?.emailAddresses || resp?.emailAddresses?.length === 0) {
            return of(undefined);
          }
          return this.invoiceService.send(pid, id, resp?.emailAddresses);
        }),
        take(1),
      )
      .subscribe(
        (resp) => {
          if (resp) {
            this.snackbarService.openSuccessSnack('INVOICE.SEND_SUCCESS');
            this.router.navigateByUrl(`/invoices`);
          }
        },
        (_) => this.snackbarService.openErrorSnack('INVOICE.SEND_FAILURE'),
      );
  }

  delete(): void {
    combineLatest([this.partnerId$, this.invoice$])
      .pipe(
        take(1),
        switchMap(([partnerId, invoice]) =>
          this.confirmationModal
            .openModal({
              type: 'warn',
              title: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.TITLE', { invoiceNumber: invoice.number }),
              message: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.SUBTITLE'),
              confirmButtonText: this.translate.instant('INVOICE.DELETE_INVOICE_MODAL.DELETE_INVOICE'),
              actionOnEnterKey: false,
            })
            .pipe(switchMap((action) => (action ? this.invoiceService.delete(partnerId, invoice.id) : EMPTY))),
        ),
        switchMap((isSuccess) => {
          if (isSuccess) {
            return of(this.router.navigateByUrl(`/invoices`));
          }
        }),
      )
      .subscribe();
  }

  async preview(): Promise<void> {
    const invoice = await firstValueFrom(this.invoice$);
    if (invoice.items.length === 0) {
      this.snackbarService.openErrorSnack('INVOICE.LINE_ITEMS_REQUIRED_ERROR');
      return;
    }

    this.previewLoading.set(true);
    combineLatest([this.retailStatus$, this.retailProvider$, this.invoiceWithCalculatedTaxes(invoice)])
      .pipe(take(1))
      .subscribe({
        next: ([status, provider, invoiceWithTaxes]) => {
          this.previewLoading.set(false);
          this.dialog.open(PreviewDialogComponent, {
            width: '660px',
            data: {
              invoice: invoiceWithTaxes,
              hasVendastaPayments: status.canAcceptPayment,
              stripeKey: this.stripeService.getConnectPublicKey(),
              companyName: invoiceWithTaxes.customerName,
              stripeAccountId: provider.stripeConnectId,
            } as PreviewInvoiceDialogData,
          });
        },
        error: () => {
          this.snackbarService.openErrorSnack('BILLING.INVOICE_PAGE.ERROR.PREVIEW_FAILED');
          this.previewLoading.set(false);
        },
      });
  }

  async charge(): Promise<void> {
    const invoice = await firstValueFrom(this.invoice$);
    if (!this.isChargeable(invoice)) {
      this.snackbarService.openErrorSnack('BILLING.INVOICE_PAGE.ERROR.PENDING_PAYMENT');
      return;
    }
    this.chargeLoading.set(true);
    combineLatest([this.retailStatus$, this.retailProvider$, this.invoiceWithCalculatedTaxes(invoice)])
      .pipe(
        take(1),
        switchMap(([status, provider, invoiceWithTaxes]) => {
          this.chargeLoading.set(false);
          return this.dialog
            .open(PreviewDialogComponent, {
              width: '660px',
              data: {
                invoice: invoiceWithTaxes,
                hasVendastaPayments: status.canAcceptPayment,
                stripeKey: this.stripeService.getConnectPublicKey(),
                companyName: invoiceWithTaxes.customerName,
                stripeAccountId: provider.stripeConnectId,
                confirmButtonText: this.translateService.instant('BILLING.INVOICE_PAGE.CHARGE_INVOICE'),
              } as PreviewInvoiceDialogData,
            })
            .afterClosed();
        }),
        filter((confirmed) => confirmed),
        tap(() => this.chargeLoading.set(true)),
        switchMap(() => this.partnerId$.pipe(take(1))),
        switchMap((partnerId) =>
          this.invoiceService.charge(partnerId, invoice.customerId, invoice.id, invoice.defaultPaymentMethod),
        ),
      )
      .subscribe({
        next: (didCharge) => {
          if (didCharge) {
            this.snackbarService.openSuccessSnack('BILLING.INVOICE_PAGE.SUCCESSFULLY_CHARGED');
            this.router.navigateByUrl(`/invoices`);
          } else {
            this.snackbarService.openErrorSnack('BILLING.INVOICE_PAGE.ERROR.PLEASE_TRY_AGAIN');
            this.chargeLoading.set(false);
          }
        },
        error: () => {
          this.snackbarService.openErrorSnack('BILLING.INVOICE_PAGE.ERROR.PREVIEW_FAILED');
          this.chargeLoading.set(false);
        },
      });
  }

  private isChargeable(invoice: Invoice): boolean {
    return !invoice.lastPaymentStatus || invoice.lastPaymentStatus === LastPaymentStatus.FAILED;
  }

  public selectRecipient(recipient: Recipient): void {
    this.editInvoicePageService.selectRecipient(recipient);
  }

  public selectAdditionalRecipients(recipients: Recipient[]): void {
    this.editInvoicePageService.selectAdditionalRecipients(recipients);
  }

  public setValidity(isValid: boolean): void {
    this.isValid = isValid;
  }

  itemsUpdated(items: LineItem[]): void {
    this.hasLineItems = items.length > 0;
  }

  presentAddRecipientsDialog(): void {
    combineLatest([this.invoice$, this.recipients$, this.invoiceId$, this.partnerId$])
      .pipe(take(1))
      .subscribe(([invoice, recipients, _invoiceId, partnerId]) => {
        this.recipientProvider.addRecipientsToAccount(invoice.customerId, partnerId, recipients).subscribe((r) => {
          if (r) {
            this.editInvoicePageService.pollForCreatedRecipient(r);
          }
        });
      });
  }

  navToCreateCreditNote(): void {
    this.router.navigate(['billing', 'credit-note', 'create'], {
      queryParams: { invoiceId: this.route.snapshot.params.id },
    });
  }

  duplicateInvoice(): void {
    combineLatest([this.partnerId$, this.invoiceId$])
      .pipe(
        take(1),
        switchMap(([partnerId, invoiceId]) => this.invoiceService.duplicate(partnerId, invoiceId)),
      )
      .subscribe({
        next: (id) => {
          if (id) {
            this.router.navigateByUrl(`/invoices/${id}/edit`);
          }
        },
      });
  }

  generateAndDownloadPDF(pdfType: InvoicePDFType): void {
    combineLatest([this.partnerId$, this.invoiceId$])
      .pipe(
        take(1),
        switchMap(([partnerId, invoiceId]) => this.invoiceService.generatePDF(partnerId, invoiceId, pdfType)),
      )
      .subscribe((fileResponse) => {
        const filename = `${pdfType}.pdf`;
        this.downloadFile(fileResponse, filename);
      });
  }

  copyPaymentLink(): void {
    combineLatest([this.partnerId$, this.invoiceId$])
      .pipe(
        take(1),
        switchMap(([partnerId, invoiceId]) => this.invoiceService.generateShortLink(partnerId, invoiceId)),
      )
      .subscribe((shortLink) => {
        if (shortLink) {
          this.copyToClipboard(shortLink);
        }
      });
  }

  private downloadFile(file: Blob, filename: string): void {
    const url = URL.createObjectURL(file);
    const link = document.createElement('a');

    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  copyToClipboard(text: string): void {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        this.snackbarService.openSuccessSnack(
          this.translateService.instant('INVOICE.TABLE.SNACK_BAR.PAYMENT_LINK_COPIED'),
        );
      })
      .catch(() => {
        this.snackbarService.openErrorSnack(
          this.translateService.instant('INVOICE.TABLE.SNACK_BAR.FAILED_TO_GENERATE'),
        );
      });
  }

  changeInvoiceStatus(): void {
    combineLatest([this.partnerId$, this.invoice$])
      .pipe(
        take(1),
        switchMap(([merchantId, invoice]) => {
          return this.dialog
            .open(ChangeInvoiceStatusDialogComponent, {
              width: '650px',
              data: {
                merchantId: merchantId,
                invoiceId: invoice.id,
                invoiceNumber: invoice.number,
              },
            })
            .afterClosed()
            .pipe(
              filter((result) => result === true),
              switchMap(() => {
                this.editInvoicePageService.loadInvoice(invoice.id);
                return this.invoice$;
              }),
            );
        }),
      )
      .subscribe();
  }

  invoiceWithCalculatedTaxes(invoice: Invoice): Observable<Invoice> {
    return this.partnerId$.pipe(
      switchMap((merchantId) => this.invoiceService.getWithCalculatedTaxes(merchantId, invoice.id)),
    );
  }

  protected readonly InvoiceStatus = InvoiceStatus;
  protected readonly InvoicePDFType = InvoicePDFType;
}
