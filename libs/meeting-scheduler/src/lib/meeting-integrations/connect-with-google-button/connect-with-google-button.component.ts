import { Component, Input, OnInit } from '@angular/core';
import { AvailableServices, MeetingViewModelService } from '@vendasta/integrations';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { DisconnectMeetingIntegrationConfirmationDialogComponent } from '../disconnect-meeting-integration-confirmation-dialog/disconnect-meeting-integration-confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { take } from 'rxjs/operators';

@Component({
  selector: 'meeting-scheduler-connect-with-google-button',
  templateUrl: './connect-with-google-button.component.html',
  styleUrls: ['./connect-with-google-button.component.scss'],
  standalone: false,
})
export class ConnectWithGoogleButtonComponent implements OnInit {
  @Input() serviceKey: string;
  @Input() primaryDisconnect = true;
  shouldTriggerDisconnect = false;
  signInText = this.translate.instant('MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_SIGN_IN');
  disconnectText = this.translate.instant('MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT_SERVICE');
  disconnectSecondaryText = this.translate.instant('MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT');
  alreadyConnectedText = this.translate.instant('MEETING_SCHEDULER.MEETING_INTEGRATIONS.CONNECTED');
  public readonly meet = AvailableServices.GOOGLE_MEET;
  isConnected$: Observable<boolean>;

  constructor(
    private readonly translate: TranslateService,
    private readonly meetingViewModelService: MeetingViewModelService,
    public dialog: MatDialog,
  ) {}

  ngOnInit(): void {
    this.isConnected$ = this.meetingViewModelService.isServiceConnected$(AvailableServices.GOOGLE_MEET);
  }

  openDisconnectDialog(event: MouseEvent): void {
    if (!this.shouldTriggerDisconnect) {
      event.preventDefault();
      event.stopImmediatePropagation();

      const dialogRef = this.dialog.open(DisconnectMeetingIntegrationConfirmationDialogComponent, {
        maxWidth: '600px',
        data: {
          titleKey: 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.GOOGLE_DISCONNECT',
          disconnectLabel: 'MEETING_SCHEDULER.MEETING_INTEGRATIONS.DISCONNECT',
        },
      });

      dialogRef
        .afterClosed()
        .pipe(take(1))
        .subscribe((confirmed: boolean) => {
          if (confirmed) {
            this.shouldTriggerDisconnect = true;

            setTimeout(() => {
              (event.target as HTMLElement).dispatchEvent(new MouseEvent('click', { bubbles: true }));
            });
          }
        });

      return;
    }
  }
}
