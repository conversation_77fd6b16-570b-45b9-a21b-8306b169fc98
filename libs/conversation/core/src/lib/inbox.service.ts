import { Inject, inject, Injectable } from '@angular/core';
import { FeatureFlagService } from '@galaxy/partner';
import { AddressAPIService } from '@vendasta/address';
import { Configuration, GlobalParticipantType, InboxApiService, PlatformLocation } from '@vendasta/conversation';
import { IAMService, Role, UserIdentifier } from '@vendasta/iamv2';
import { ViewAccessApiService } from '@vendasta/platform-users';
import { GetAccountInfoResponse, ParticipantType, SmsService } from '@vendasta/smsv2';
import { AccountsService } from '@vendasta/accounts/legacy';
import { Environment, EnvironmentService } from '@galaxy/core';
import {
  catchError,
  combineLatest,
  distinctUntilChanged,
  firstValueFrom,
  map,
  Observable,
  of,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  take,
} from 'rxjs';
import { MARKETING_SERVICES_PIDS } from './conversation.constants';
import { isWideScreen } from './inbox-utils';
import {
  ADDITIONAL_PROMPT_FEATURE,
  CUSTOM_AI_ASSISTANTS,
  EXCLUDED_GEOGRAPHICAL_US_STATES,
  FULLSCREEN_INBOX_PC,
  INBOX_AI_RESPONSE,
  INBOX_ONE_ON_ONE,
  INBOX_OPEN_DEFAULT,
  INBOX_PLATFORM_CHAT,
  INBOX_SIDE_BAR,
  INBOX_SMS_A2P,
  PION_PARTNER_INBOX_SMS,
  VOICE_AI,
} from './inbox.constants';
import { ParticipantService } from './participant.service';
import {
  ACCOUNT_GROUP_ID_TOKEN,
  CONVERSATION_CONFIG_TOKEN,
  CONVERSATION_COUNTRY_TOKEN,
  CONVERSATION_GEOGRAPHICAL_STATE_TOKEN,
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_PLATFORM_LOCATION_TOKEN,
  CONVERSATION_SMS_ENABLED_TOKEN,
  CONVERSATION_WEB_CHAT_ENABLED_TOKEN,
  FEATURE_FLAG_TOKEN,
  GROUP_ID_TOKEN,
  PARTNER_ID_TOKEN,
  USER_ID_TOKEN,
} from './tokens';
import { InboxNamespace } from './interface/inbox.interface';
import { A2PRegistrationService, RegistrationStage } from '@galaxy/sms';

const isSuperAdminFn = (partner: Role): boolean => !!partner?.attributes?.attributes['is_super_admin']?.boolAttribute;

@Injectable()
export class InboxService {
  private readonly smsService = inject(SmsService);
  private readonly hostAppInterface = inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN);
  private readonly refreshSMSNumber$$ = new Subject<void>();
  // TODO: MEGA-791 - Remove this once SMS registration state check is implemented on backend
  private readonly smsRegistrationService = this.isBusinessApp ? inject(A2PRegistrationService) : null;

  readonly iamUser$ = this.userId$.pipe(
    switchMap((userId) => this.iamService.getUser({ userId: userId } as UserIdentifier)),
  );

  readonly userIsPartnerAdmin$ = this.partnerId$.pipe(
    switchMap((partnerId) => this.viewAccessService.hasViewAccess({ partnerId, viewIds: ['partner-views'] })),
    map((response) => !!response?.access?.[0]?.hasAccess),
    catchError(() => of(false)),
    shareReplay(1),
  );

  readonly canAccessEmbeddedSettings$ = combineLatest([this.iamUser$, this.partnerId$]).pipe(
    map(([user, currentPartnerId]) => {
      if (!this.isPartnerCenter) return false;
      const { partnerId: userPartnerID, roles: { partner } = {} } = user;
      const isSuperAdmin = isSuperAdminFn(partner);
      return (currentPartnerId === userPartnerID && !!partner) || isSuperAdmin;
    }),
    shareReplay(1),
  );

  readonly showAvailabilityMessageSettings$ = combineLatest([this.iamUser$, this.partnerId$]).pipe(
    map(([user, currentPartnerId]) => {
      const { partnerId: userPartnerID, roles: { partner } = {} } = user;
      const isSuperAdmin = isSuperAdminFn(partner);
      return (
        ((currentPartnerId === userPartnerID && !!partner) || isSuperAdmin) &&
        MARKETING_SERVICES_PIDS.includes(currentPartnerId)
      );
    }),
    shareReplay(1),
  );

  private readonly featureFlags$ = this.partnerId$.pipe(
    switchMap((partnerId) => {
      return this.featureFlagService
        .batchGetStatus(partnerId, '', [
          INBOX_OPEN_DEFAULT,
          INBOX_SMS_A2P,
          INBOX_ONE_ON_ONE,
          PION_PARTNER_INBOX_SMS,
          INBOX_PLATFORM_CHAT,
          INBOX_SIDE_BAR,
          INBOX_AI_RESPONSE,
          ADDITIONAL_PROMPT_FEATURE,
          VOICE_AI,
          CUSTOM_AI_ASSISTANTS,
          FULLSCREEN_INBOX_PC,
        ])
        .pipe(take(1), shareReplay(1));
    }),
  );

  readonly partnerHasA2PAccess$ = this.featureFlags$.pipe(
    map((res) => res[INBOX_SMS_A2P]),
    distinctUntilChanged(),
    shareReplay(1),
  );

  readonly featureFlagShowNewMessagePC$ = this.featureFlags$.pipe(
    map((res) => res[PION_PARTNER_INBOX_SMS]),
    distinctUntilChanged(),
    shareReplay(1),
  );

  readonly featureFlagFullscreenInboxPC$ = this.featureFlags$.pipe(
    map((res) => res[FULLSCREEN_INBOX_PC] && this.isPartnerCenter),
    distinctUntilChanged(),
    shareReplay(1),
  );

  readonly SMSNumber$ = combineLatest([
    this.accountGroupId$,
    this.partnerId$,
    this.refreshSMSNumber$$.pipe(startWith(undefined)),
  ]).pipe(
    switchMap(([accountGroupId, partnerId, _]) => {
      let ownerId = '',
        participantType = ParticipantType.PARTICIPANT_TYPE_UNKNOWN;

      if (accountGroupId) {
        ownerId = accountGroupId;
        participantType = ParticipantType.PARTICIPANT_TYPE_ACCOUNT_GROUP;
      } else if (partnerId) {
        ownerId = partnerId;
        participantType = ParticipantType.PARTICIPANT_TYPE_PARTNER;
      }

      return this.smsService
        .getAccountInfo({
          internalId: ownerId,
          type: participantType,
        })
        .pipe(
          catchError(() => of({} as GetAccountInfoResponse)),
          map((accountInfo) => accountInfo?.phoneNumber),
        );
    }),
    distinctUntilChanged(),
    shareReplay(1),
  );

  readonly isVoiceAiFeatureEnabled$ = this.featureFlags$.pipe(
    map((featureFlag) => featureFlag[VOICE_AI]),
    distinctUntilChanged(),
    shareReplay(1),
  );

  hasAccessToInboxPage$ =
    inject(CONVERSATION_CONFIG_TOKEN)?.hasAccessToInboxPage$?.pipe(map((result) => !!result)) || of(true);
  allowSmbChatWithPartner$ =
    inject(CONVERSATION_CONFIG_TOKEN)?.allowSmbChatWithPartner$?.pipe(map((result) => !!result)) || of(false);

  private readonly _displayA2pCard$ = combineLatest([
    this.hasAccessToInboxPage$,
    this.geographicalState$.pipe(switchMap((geographicalState) => this.hasAccessByCountry(geographicalState))),
    this.partnerHasA2PAccess$,
    this.smsEnabled$,
    this.featureFlagShowNewMessagePC$,
  ]).pipe(
    map(
      ([
        hasAccessToInboxPage,
        hasAccessByCountry,
        partnerHasA2PAccess,
        smsEnable,
        smsFeaturePartnerCenter,
      ]): boolean => {
        const isValidLocation = this.isBusinessApp || this.isPartnerCenter;

        const baseRequirements =
          isValidLocation && hasAccessToInboxPage && hasAccessByCountry && partnerHasA2PAccess && smsEnable;

        return baseRequirements && (this.isBusinessApp || smsFeaturePartnerCenter);
      },
    ),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  constructor(
    private readonly featureFlagService: FeatureFlagService,
    private readonly participantService: ParticipantService,
    @Inject(ACCOUNT_GROUP_ID_TOKEN) private readonly accountGroupId$: Observable<string>,
    @Inject(CONVERSATION_COUNTRY_TOKEN) readonly country$: Observable<string>,
    @Inject(CONVERSATION_GEOGRAPHICAL_STATE_TOKEN) private readonly geographicalState$: Observable<string>,
    @Inject(PARTNER_ID_TOKEN) private readonly partnerId$: Observable<string>,
    @Inject(FEATURE_FLAG_TOKEN)
    @Inject(CONVERSATION_PLATFORM_LOCATION_TOKEN)
    readonly platformLocation: PlatformLocation,
    private readonly addressService: AddressAPIService,
    private readonly accountsService: AccountsService,
    private readonly environmentService: EnvironmentService,
    @Inject(CONVERSATION_SMS_ENABLED_TOKEN) private smsEnabled$: Observable<boolean>,
    @Inject(CONVERSATION_WEB_CHAT_ENABLED_TOKEN) private webChatEnabled$: Observable<boolean>,
    @Inject(USER_ID_TOKEN) private readonly userId$: Observable<string>,
    private iamService: IAMService,
    private readonly inboxApiService: InboxApiService,
    @Inject(GROUP_ID_TOKEN) private readonly groupId$: Observable<string>,
    private readonly viewAccessService: ViewAccessApiService,
  ) {}

  /**
   * Check if account can access SMS feature.
   * SMS is only accessible for US/CA accounts.
   */
  get canAccessSMS$(): Observable<boolean> {
    const hasAccessByCountry$ = this.geographicalState$.pipe(
      switchMap((geographicalState) => this.hasAccessByCountry(geographicalState)),
    );
    return combineLatest([this.hasAccessToInboxPage$, hasAccessByCountry$, this.smsEnabled$]).pipe(
      map(([hasAccessToInboxPage, hasAccessByCountry, smsEnabled]) => {
        return hasAccessToInboxPage && hasAccessByCountry && smsEnabled;
      }),
      distinctUntilChanged(),
    );
  }

  // TODO: MEGA-791 - Remove this once SMS registration state check is implemented on backend
  readonly isSMSChannelAvailableForOrg$: Observable<boolean> = this.canAccessSMS$.pipe(
    switchMap((canAccessSMS) => {
      if (!canAccessSMS) {
        return of(false);
      }
      if (!this.smsRegistrationService) {
        return of(canAccessSMS);
      }

      return combineLatest([this.country$, this.smsRegistrationService.registrationStage$, this.SMSNumber$]).pipe(
        map(([accountGroupCountry, stage, smsNumber]) => {
          const country = accountGroupCountry?.toLowerCase() || '';
          // Canadian account case
          if (country != 'us' && smsNumber) {
            return true;
          }
          // US account case
          return stage === RegistrationStage.RegistrationComplete;
        }),
      );
    }),
  );

  getInboxConfigByLoggedNamespace$(): Observable<Configuration | undefined> {
    return this.hostAppInterface.getNamespace().pipe(
      switchMap((namespace) => {
        return this.inboxApiService.getConfiguration({
          subjectParticipant: {
            participantType: namespace.namespaceType,
            internalParticipantId: namespace.id,
          },
        });
      }),
      catchError(() => of(undefined)),
      map((config) => config?.configuration),
    );
  }

  getInboxConfigByNamespace$(namespace: InboxNamespace): Observable<Configuration | undefined> {
    return this.inboxApiService
      .getConfiguration({
        subjectParticipant: {
          participantType: namespace.namespaceType,
          internalParticipantId: namespace.id,
        },
      })
      .pipe(
        catchError(() => of(undefined)),
        map((config) => config?.configuration),
      );
  }

  get canAccess$(): Observable<boolean> {
    return combineLatest([this.hasAccessToInboxPage$, this.hasAccessByCountry()]).pipe(
      map(([hasAccess, hasAccessByCountry]) => hasAccess && hasAccessByCountry),
      take(1),
      shareReplay(1),
    );
  }

  hasAccessByCountry(orgGeographicalState?: string): Observable<boolean> {
    // https://address-demo.apigateway.co/address.v1.Address/ListAllCountryOptions
    // Australia - AU / United Kingdom - GB
    const availableCountries = ['ca', 'us'];
    return this.country$.pipe(
      switchMap((orgCountry) => {
        const country = orgCountry?.toLowerCase() || '';
        if (!availableCountries?.includes(country)) {
          return of(false);
        }
        if (country === 'us' && orgGeographicalState) {
          return this.hasAccessByUSState(orgGeographicalState).pipe(
            take(1),
            map((hasAccessByUSState) => hasAccessByUSState),
          );
        }
        return of(true);
      }),
    );
  }

  private hasAccessByUSState(orgState: string): Observable<boolean> {
    const state = orgState?.toLowerCase() || '';
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.addressService.getCountryConfiguration('US', undefined!).pipe(
      take(1),
      map((countryInfo) => {
        const filteredStateList = countryInfo?.zones?.reduce((accumulator: string[], currentValue) => {
          if (!EXCLUDED_GEOGRAPHICAL_US_STATES.includes(currentValue?.code)) {
            return [...accumulator, currentValue?.code?.toLocaleLowerCase()];
          }
          return accumulator;
        }, []);
        return filteredStateList?.includes(state);
      }),
      catchError((error) => {
        console.error('Error getting country configuration:', error.message);
        return of(false);
      }),
    );
  }

  private getRMPremiumEditionId(): string {
    switch (this.environmentService.getEnvironment()) {
      case Environment.DEMO:
        return 'EDITION-BFXF8W8Q';
      case Environment.PROD:
        return 'EDITION-JFRPLQPN';
    }
    return 'EDITION-BFXF8W8Q';
  }

  isRMPremiumActive(): Observable<boolean> {
    return combineLatest([this.accountGroupId$, this.partnerId$]).pipe(
      switchMap(([accountGroupId, partnerId]) => this.accountsService.list(accountGroupId, partnerId)),
      map((res) => res.accounts || []),
      map((accounts) => accounts.filter((a) => !a.trial && !a.deactivation)),
      map((accounts) =>
        accounts.some((account) => account.productId === 'RM' && account.editionId === this.getRMPremiumEditionId()),
      ),
    );
  }

  readonly showInboxViews$ = of(!this.isBusinessApp);
  readonly showInboxEmpty$ = of(!this.isBusinessApp && !this.isPartnerCenter);

  readonly openInboxDefault$ = this.featureFlags$.pipe(
    map((featureFlags) => {
      return featureFlags[INBOX_OPEN_DEFAULT] && this.isPartnerCenter && !isWideScreen();
    }),
    shareReplay(1),
  );

  readonly showInboxBCSideBar$ = this.featureFlags$.pipe(
    map((featureFlags) => {
      return featureFlags[INBOX_SIDE_BAR] && this.isBusinessApp;
    }),
  );

  readonly canAccessWebChatSettings$ = this.webChatEnabled$.pipe(shareReplay(1));

  readonly hasPlatformChatAccess$ = this.featureFlags$.pipe(
    map((res) => res[INBOX_PLATFORM_CHAT] && this.isPartnerCenter),
    distinctUntilChanged(),
    shareReplay(1),
  );

  // One on one chat is currently available in Business App only for the first slice.
  // For the future, other centers will have to implement 'getUsers', except Partner Center since we currently have a http endpoint to fetch users for partner already
  readonly canAccessPrivateChat$ = this.featureFlags$.pipe(
    map((featureFlags) => {
      return featureFlags[INBOX_ONE_ON_ONE] && this.isBusinessApp;
    }),
  );

  readonly multiLocationEnabled$ = this.groupId$.pipe(
    switchMap((groupId) => {
      if (!groupId || !this.isBusinessApp) {
        return of({ configuration: { multiLocationEnabled: false } });
      }
      return this.inboxApiService
        .getConfiguration({
          subjectParticipant: {
            participantType: GlobalParticipantType.GLOBAL_PARTICIPANT_TYPE_GROUP,
            internalParticipantId: groupId,
          },
        })
        .pipe(catchError(() => of({ configuration: { multiLocationEnabled: false } })));
    }),
    map((config) => {
      return config.configuration.multiLocationEnabled ?? false;
    }),
    catchError(() => of(false)),
    shareReplay(1),
  );

  /**
   * check if the current platform location is the business app
   * @return {boolean}
   */
  get isBusinessApp(): boolean {
    return this.platformLocation === PlatformLocation.PLATFORM_LOCATION_BUSINESS_APP;
  }

  /**
   * check if the current platform location is the partner center
   * @return {boolean}
   */
  get isPartnerCenter(): boolean {
    return this.platformLocation === PlatformLocation.PLATFORM_LOCATION_PARTNER_CENTER;
  }

  /**
   * check if the current platform location is the sales center
   * @return {boolean}
   */
  get isSalesCenter(): boolean {
    return this.platformLocation === PlatformLocation.PLATFORM_LOCATION_SALES_CENTER;
  }

  /**
   * check if displaying the A2P card in business app inbox setting page
   * @return {Promise<boolean>}
   */
  displayA2pCard$(): Observable<boolean> {
    return this._displayA2pCard$;
  }

  async buildTemplateTrackProperties() {
    return {
      accountGroupId: await firstValueFrom(this.accountGroupId$),
      participantName: (await firstValueFrom(this.participantService.currentParticipant$)).name || '',
      partnerId: await firstValueFrom(this.partnerId$),
      location: PlatformLocation[this.platformLocation],
    };
  }

  async buildAiKnowledgeAppConfigurationUrl(widgetId: string): Promise<string> {
    const accountGroupId = await firstValueFrom(this.accountGroupId$);
    if (accountGroupId) {
      return `/account/location/${accountGroupId}/settings/widgets/${widgetId}/edit`;
    }
    return `/(inbox:inbox/widgets/${widgetId}/edit)`;
  }

  buildAppId(widgetId: string): string {
    return widgetId ? `APP-WEBCHAT-${widgetId}` : '';
  }

  /**
   * Refreshes the SMS number by forcing a new request to the SMS service.
   */
  refreshSMSNumber(): void {
    this.refreshSMSNumber$$.next();
  }
}
