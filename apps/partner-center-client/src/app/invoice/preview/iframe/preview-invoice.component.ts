import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { InvoiceService } from '@galaxy/billing';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';

@Component({
  selector: 'app-pc-preview-invoice',
  styleUrls: ['./preview-invoice.component.scss'],
  template:
    '<iframe id="invoiceIframe" [srcdoc]="html$ | async" scrolling="no" onload="this.height=this.contentWindow.document.body.scrollHeight;"></iframe>',
  standalone: false,
})
export class PreviewInvoiceComponent implements OnInit {
  @Input() merchantId: string;
  @Input() invoiceId: string;
  @Output() invoiceLoaded = new EventEmitter<boolean>();
  html$: Observable<SafeHtml>;

  constructor(
    private invoiceService: InvoiceService,
    private sanitizer: DomSanitizer,
    private snackbarService: SnackbarService,
  ) {}

  ngOnInit(): void {
    this.html$ = this.invoiceService.preview(this.merchantId, this.invoiceId).pipe(
      map((renderedTemplate) => {
        this.invoiceLoaded.emit(true);
        return this.sanitizer.bypassSecurityTrustHtml(renderedTemplate);
      }),
      catchError(() => {
        this.snackbarService.openErrorSnack('BILLING.INVOICE_PAGE.ERROR.PREVIEW_FAILED');
        return of(this.sanitizer.bypassSecurityTrustHtml('<p></p>'));
      }),
    );
  }
}
