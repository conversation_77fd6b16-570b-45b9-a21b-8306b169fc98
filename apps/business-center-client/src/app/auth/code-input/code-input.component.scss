@use 'design-tokens' as *;

.code-input-container {
  display: flex;
  justify-content: space-evenly;
  gap: $spacing-2;
  padding: 0 $spacing-2; /* spacing at edges */
  width: 100%;
}

.single-input-box {
  width: 42px;
  height: 52px;
  font-size: $font-preset-2-size;
  text-align: center;
  border: 1px solid $border-color; // There is also $contrast-border-color and $weak-border-color if that doesn't look right
  border-radius: $default-border-radius;
  outline: none;
  min-width: 0; /* Prevents overflow */
}

.single-input-box:focus {
  border-color: $primary-color;
}
