@use 'design-tokens' as *;

.ui-button-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  margin-bottom: $spacing-1;
  .ui-button {
    text-align: start;
  }

  &.align-self-end {
    align-items: flex-end;
    .ui-button {
      text-align: end;
    }
  }
}

.ui-button {
  border-radius: 16px;
  padding: $spacing-2 $spacing-3;
  color: $primary-text-color;
  font-size: $font-preset-4-size;
  background-color: $info-background-color;
  margin: $spacing-1 0;
  border: 1px solid rgba(0, 0, 0, 0.04);
  font-weight: 500;
  width: fit-content;
  flex-basis: fit-content;
}
