@use 'design-tokens' as *;

.voice-select-container {
  margin-top: $spacing-3;
}

.voice-select-label {
  margin-bottom: $spacing-2;
  font-weight: 500;
}

.voice-select-and-preview {
  display: flex;
  align-items: center;
}

.select-form {
  flex: 1;
}

.preview-player {
  margin-left: $spacing-2;
}

.accordian-expansion-icon {
  vertical-align: middle;
  margin-right: $spacing-1;
}

.advanced-voice-options-toggle {
  margin-top: $spacing-2;
  display: flex;
  align-items: center;
}

.advanced-voice-options-content {
  margin-top: $spacing-3;
  padding-left: $spacing-4;
}
