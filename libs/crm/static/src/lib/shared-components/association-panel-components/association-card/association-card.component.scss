@use 'design-tokens' as dt;

.card {
  min-width: 200px;

  margin-bottom: dt.$spacing-2;
  .card-header {
    border-bottom: unset;
  }
  .content {
    margin: 0;
  }

  ::ng-deep .mat-mdc-card-header-text {
    display: flex;
    flex-direction: column;
    min-width: 0px;
  }
}

.name {
  color: dt.$blue;
  font-weight: 500;
  padding-bottom: dt.$spacing-1;
}

.account-id {
  font-weight: 500;
  margin: 0;
}

.account-name {
  margin: 0;
  p {
    margin: 0;
  }
}

.email {
  display: inline-block;
  a {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.website,
.phone {
  a {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.kebab-menu {
  margin-left: auto;
  transform: translateY(-10px);
  .menu-icon {
    color: dt.$icon-color;
  }
}

.link {
  color: dt.$secondary-text-color;
  &:hover {
    color: dt.$blue;
  }
}

mat-card-header.mat-mdc-card-header mat-card-title + mat-card-subtitle {
  margin-bottom: 0;
}

.copy-icon {
  font-size: dt.$font-preset-4-size;
  color: dt.$icon-color;
  padding: 0;
  margin: 0;
  justify-content: center;
  vertical-align: middle;
}

.custom-icon-button {
  width: dt.$spacing-4;
  height: dt.$spacing-4;
  color: dt.$icon-color;
  justify-content: center;
  padding: 0;
  vertical-align: sub;
}

.card-contents {
  align-items: center;
  display: flex;
}

.business-info {
  display: flex;
  flex-direction: row;
  align-items: center;

  mat-icon {
    font-size: dt.$font-preset-3-size;
    color: dt.$icon-color;
  }

  .copy-card-icon {
    cursor: pointer;
  }
}

.full-business-address {
  display: inline-block;
  vertical-align: top;
  padding-left: dt.$spacing-1;
  max-width: 360px;
  white-space: pre-line;
}

.action-button {
  display: flex;
  justify-content: space-between;
  align-items: center;

  mat-icon {
    font-size: dt.$font-preset-2-size;
    line-height: 1.2;
    margin: 0;
    color: dt.$icon-color;
  }
}
