import { FormControl, FormGroup } from '@angular/forms';
import { Model, ModelInterface } from '@vendasta/ai-assistants';

export const DEFAULT_MODEL = new Model({
  id: undefined,
  vendor: undefined,
});

export type AiModelConfigFormControls = {
  model: FormControl<Model | null>;
};

export class AiModelConfigurationForm extends FormGroup<AiModelConfigFormControls> {
  constructor(model?: ModelInterface) {
    super({
      model: new FormControl(model ? new Model(model) : DEFAULT_MODEL),
    });
  }

  toAssistantModel(): ModelInterface | null {
    const model = this.controls.model.getRawValue();
    if (model == null || (model?.id === undefined && model?.vendor === undefined)) {
      return null;
    }
    return {
      id: model.id,
      vendor: model.vendor,
      type: model.type,
      name: model.name,
      recommended: model.recommended,
    };
  }
}
