import { Inject, Injectable } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { ActivatedRoute } from '@angular/router';
import { AUTOMATION_NAMESPACE_INJECTION_TOKEN$, CurrentAutomationService } from '@galaxy/automata/shared';
import { TranslateService } from '@ngx-translate/core';
import {
  Activity,
  AutomataService,
  ListActivitiesRequestFiltersInterface,
  PagedResponse,
  Stage,
} from '@vendasta/automata';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterDefinitionInterface,
  GalaxyFilterInterface,
  GalaxyFilterOperator,
} from '@vendasta/galaxy/filter/chips';
import {
  GalaxyColumnDef,
  GalaxyDataSource,
  PagedListRequestInterface,
  PagedResponseInterface,
  Row,
  RowData,
} from '@vendasta/galaxy/table';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { map, shareReplay, switchMap, take } from 'rxjs/operators';
import {
  ActivityRow,
  buildGalaxyTableEntityDisplayColumns,
  DynamicColumn,
  entityTypeRequirements,
  EntityTypeService,
} from '@galaxy/automata';
import { ConvertedFilters, ENTITY_ID, FILTERS, MAIN_RUN_ID, TRIGGER_OPTIONS } from './filters';
import { DEFAULT_COLUMNS } from './table-model';

@Injectable()
export class ActivitiesTableService implements GalaxyFilterChipDependencies {
  automationId$: Observable<string> = this.route.paramMap.pipe(map((params) => params.get('automationId') || ''));

  // Table user input variables
  data: Record<string, unknown> = {};
  pageSize = 25;
  searchText = '';

  // Table column variables
  private filters$$ = new BehaviorSubject<GalaxyFilterInterface[]>([]);
  columns: GalaxyColumnDef[] = [];

  constructor(
    @Inject(AUTOMATION_NAMESPACE_INJECTION_TOKEN$) private readonly namespace$: Observable<string>,
    private readonly route: ActivatedRoute,
    private readonly automataService: AutomataService,
    private readonly currentAutomationService: CurrentAutomationService,
    private readonly entityTypesService: EntityTypeService,
    private readonly translateService: TranslateService,
  ) {}

  // Gets full list of columns for the activity table
  getColumn$(): Observable<GalaxyColumnDef[]> {
    return this.automationId$.pipe(
      take(1),
      switchMap((id) => this.automataService.getAutomation(id)),
      map((automation) => {
        const dynamicColumns = buildGalaxyTableEntityDisplayColumns(automation.entityType) as GalaxyColumnDef[];
        return dynamicColumns.concat(DEFAULT_COLUMNS);
      }),
    );
  }

  // Gets the dynamic columns based off of the entity (e.x. account, user, etc.)
  getDynamicColumns$(): Observable<DynamicColumn[]> {
    return this.automationId$.pipe(
      take(1),
      switchMap((id) => this.automataService.getAutomation(id)),
      map((automation) => {
        return buildGalaxyTableEntityDisplayColumns(automation.entityType);
      }),
    );
  }

  // Builds the data that will be used in the table
  getDataSource(): GalaxyDataSource<Row, GalaxyFilterInterface, MatSort> {
    const paginatedAPI = {
      get: (
        req: PagedListRequestInterface<GalaxyFilterInterface, MatSort>,
      ): Observable<PagedResponseInterface<Row>> => {
        const cursor = req.pagingOptions?.cursor || '';
        const pageSize = req.pagingOptions?.pageSize || 25;

        const resp$ = this.filters$$.pipe(
          switchMap((filters) => {
            return this.listActivities(cursor, this.convertFilters(filters), pageSize);
          }),
          shareReplay({ bufferSize: 1, refCount: true }),
        );

        const activities$ = resp$.pipe(map((resp) => resp?.results || []));

        const entities$ = this.currentAutomationService.automation$.pipe(
          map((a) =>
            this.entityTypesService.getEntityObservablesFromActivitiesBasedOnType(
              this.namespace$,
              activities$,
              a.entityType,
            ),
          ),
          shareReplay({ bufferSize: 1, refCount: true }),
        );

        const activityRows$ = combineLatest([
          activities$,
          entities$.pipe(switchMap((e) => e.accountGroups$)),
          entities$.pipe(switchMap((e) => e.orders$)),
          entities$.pipe(switchMap((e) => e.users$)),
          entities$.pipe(switchMap((e) => e.contacts$)),
          entities$.pipe(switchMap((e) => e.companies$)),
        ]).pipe(
          map(([activities, accountGroups, orders, users, contacts, companies]) => {
            return activities.map((a, i, _) => {
              return {
                activity: a,
                accountGroup: accountGroups[i] || null,
                order: orders[i] || null,
                user: users[i] || null,
                contact: contacts[i] || null,
                company: companies[i] || null,
              };
            });
          }),
        );

        return combineLatest([activityRows$, resp$]).pipe(
          map(([rows, resp]) => {
            let sortedRows: ActivityRow[] = [];

            // Sort the rows
            if (req.sorting) {
              sortedRows = this.sortData(req.sorting[0], rows);
            }

            const searchFilteredRows = this.getSearchFilteredRows(sortedRows, this.searchText);
            const pageRows: Row[] = [];
            const cursorAsNumber = Number(atob(req.pagingOptions?.cursor || ''));
            const pageSize = req.pagingOptions?.pageSize || 0;
            const totalResults = resp.totalResults || 0;

            // Go over each row and put it together
            for (
              let rowIndex = cursorAsNumber;
              rowIndex < Math.min(cursorAsNumber + pageSize, totalResults);
              rowIndex++
            ) {
              const rowData: RowData = {};
              // Go over every column, adding the data to the row
              for (let columnIndex = 0; columnIndex < this.columns.length; columnIndex++) {
                // Exclude columns that don't have data
                if (['actions'].includes(this.columns[columnIndex].id)) {
                  continue;
                }
                rowData[this.columns[columnIndex].id] = (searchFilteredRows as Record<string, any>)[
                  rowIndex % pageSize
                ][this.columns[columnIndex].id];
              }
              const row = {
                id: `ROW-${rowIndex}`,
                data: rowData,
                activity_row: searchFilteredRows[rowIndex % pageSize],
              };

              pageRows.push(row);
            }

            return {
              data: pageRows,
              pagingMetadata: {
                hasMore: resp.hasMore,
                nextCursor: resp.nextCursor,
                totalResults: resp.totalResults,
              },
            };
          }),
        );
      },
    };

    return new GalaxyDataSource<Row, GalaxyFilterInterface, MatSort>(paginatedAPI);
  }

  sortData(sort: Sort, rows: ActivityRow[]): ActivityRow[] {
    if (!sort) {
      return rows;
    }
    if (!sort.active || sort.direction === '') {
      return rows;
    }
    const isAsc = sort.direction === 'asc';
    return customSort(rows, sort.active, isAsc);
  }

  // Given a text, will show you just the rows you're looking for
  getSearchFilteredRows(rows: ActivityRow[], searchText: string): ActivityRow[] {
    return rows.filter((row) => {
      if (!searchText) {
        return true;
      }

      searchText = searchText.toLowerCase();

      return Object.keys(row).some((key: string) => {
        const prop = (row as Record<string, any>)[key];
        // check if model-driven cell (discriminate for CellData interface)
        if (typeof prop === 'object' && 'value' in prop && 'cellType' in prop) {
          return prop['value'].toString().toLowerCase().includes(searchText);
        }
        return prop.toString().toLowerCase().includes(searchText);
      });
    });
  }

  updateFilters(filters: GalaxyFilterInterface[]): void {
    this.filters$$.next(filters);
  }

  // getFieldOptions allows you to return values for autocompleting a filter.
  // Just return of([]) if you have no autocomplete.
  // This could come from an API or be filtered on the frontend for small, known, datasets.
  getFieldOptions(fieldId: string, _: string): Observable<string[]> {
    const eventsDidntStart = this.translateService.instant(
      'AUTOMATIONS.ACTIVITY.TABLE.FILTERS.EVENTS.SHOW_EVENTS_THAT_DIDNT_START',
    );
    const eventsWithErrors = this.translateService.instant(
      'AUTOMATIONS.ACTIVITY.TABLE.FILTERS.EVENTS.ONLY_SHOW_ERRORS',
    );
    if (fieldId.includes(TRIGGER_OPTIONS)) {
      return of([eventsDidntStart, eventsWithErrors]);
    }
    return of([]);
  }

  initFilters: GalaxyFilterInterface[] = [];

  // getInitialAppliedFilters lets you specify what filters should be shown when they are initially loaded.
  // To provide a 'preset' filter that does not have a default value, specify preset:true in the filter definition.
  // The filters must be given unique ids.
  getInitialAppliedFilters(): Promise<GalaxyFilterInterface[]> {
    let showOnlyErrorFilters: GalaxyFilterInterface | null = null;
    const queryShowOnlyErrors =
      JSON.parse(decodeURIComponent(this.route.snapshot.queryParamMap.get('activities') || '{}'))?.showOnlyErrors ||
      false;
    if (queryShowOnlyErrors) {
      const onlyShowErrorsLabel = this.translateService.instant(
        'AUTOMATIONS.ACTIVITY.TABLE.FILTERS.EVENTS.ONLY_SHOW_ERRORS',
      );
      showOnlyErrorFilters = {
        fieldId: TRIGGER_OPTIONS,
        operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
        values: [{ string: onlyShowErrorsLabel }],
      };
    }
    const combinedFilters = showOnlyErrorFilters ? [...this.initFilters, showOnlyErrorFilters] : this.initFilters;
    return Promise.resolve(combinedFilters);
  }

  setInitialAppliedFilters(filters: GalaxyFilterInterface[]): void {
    this.initFilters = filters;
  }

  // listObjectFilters is used to return the filter options. A searchTerm may be given to limit the filters to show.
  // This can be filtered via API or simply in the frontend.
  listObjectFilters(searchTerm: string): Observable<GalaxyFilterDefinitionInterface[]> {
    return this.currentAutomationService.automation$.pipe(
      map((a) => {
        return FILTERS.filter((filter) => {
          if (filter.fieldId === ENTITY_ID) {
            filter.fieldName = this.translateService.instant(entityTypeRequirements[a.entityType].singularName);
          } else {
            filter.fieldName = this.translateService.instant(filter.fieldName || '');
          }
          return filter.fieldName?.toLowerCase().includes(searchTerm.toLowerCase());
        });
      }),
    );
  }

  listActivities(nextCursor: string, filters: ConvertedFilters, pageSize: number): Observable<PagedResponse<Activity>> {
    return combineLatest([this.namespace$, this.currentAutomationService.automation$]).pipe(
      switchMap(([partnerId, automation]) => {
        const f: ListActivitiesRequestFiltersInterface = {
          automationId: automation.id,
          stage: filters.stages,
          partnerId: partnerId,
          marketIds: undefined,
          onlyErrors: filters.onlyErrors,
          mainRunId: filters.mainRunId,
          entityId: filters.entityId,
        } as ListActivitiesRequestFiltersInterface;
        return this.automataService.listActivities(f, nextCursor, pageSize);
      }),
      take(1),
      shareReplay({ bufferSize: 1, refCount: true }),
    );
  }

  // convertFilters is used to convert GalaxyFilterInterface[] to ConvertedFilters for our API calls
  convertFilters(filters: GalaxyFilterInterface[]): ConvertedFilters {
    const f: any = {
      stages: [
        Stage.STAGE_WORKFLOW_STEP,
        Stage.STAGE_WORKFLOW_COMPLETED,
        Stage.STAGE_WORKFLOW_CANCELED,
        Stage.STAGE_WORKFLOW_COMPLETED_FROM_ERROR,
        Stage.STAGE_WORKFLOW_CONTINUED,
        Stage.STAGE_WORKFLOW_CONTINUED_FROM_ERROR,
        Stage.STAGE_WORKFLOW_COMPLETED_FROM_GOAL,
      ],
      entityId: undefined,
      mainRunId: '',
    };

    filters.forEach((filter) => {
      const onlyShowErrors = this.translateService.instant(
        'AUTOMATIONS.ACTIVITY.TABLE.FILTERS.EVENTS.ONLY_SHOW_ERRORS',
      );
      const eventsDidntStart = this.translateService.instant(
        'AUTOMATIONS.ACTIVITY.TABLE.FILTERS.EVENTS.SHOW_EVENTS_THAT_DIDNT_START',
      );

      switch (filter.fieldId) {
        case TRIGGER_OPTIONS:
          for (const value of filter?.values || []) {
            if (value.string === onlyShowErrors) {
              f.onlyErrors = true;
            }
            if (value.string === eventsDidntStart) {
              f.stages = [
                Stage.STAGE_WORKFLOW_STEP,
                Stage.STAGE_WORKFLOW_COMPLETED,
                Stage.STAGE_WORKFLOW_CANCELED,
                Stage.STAGE_WORKFLOW_COMPLETED_FROM_ERROR,
                Stage.STAGE_WORKFLOW_DID_NOT_RUN,
                Stage.STAGE_WORKFLOW_CONTINUED,
                Stage.STAGE_WORKFLOW_CONTINUED_FROM_ERROR,
                Stage.STAGE_WORKFLOW_COMPLETED_FROM_GOAL,
              ];
            }
          }
          break;
        case MAIN_RUN_ID:
          f.mainRunId = filter.values?.[0]?.string;
          break;
        case ENTITY_ID:
          f.entityId = filter.values?.[0]?.string;
          break;
      }
    });

    return f;
  }
}

function customSort(data: any[], sortColumn: string, asc: boolean): any[] {
  return data.slice().sort((a, b) => {
    const aValue = a[sortColumn];
    const bValue = b[sortColumn];
    if (['first_name', 'last_name', 'country'].includes(sortColumn)) {
      return asc ? aValue?.localeCompare(bValue) : bValue?.localeCompare(aValue);
    }
    if (!aValue?.cellType) {
      return data;
    }
    switch (aValue?.cellType) {
      case 'date':
      case 'timestamp':
        return asc
          ? new Date(aValue?.value).getTime() - new Date(bValue?.value).getTime()
          : new Date(bValue?.value).getTime() - new Date(aValue?.value).getTime();
      case 'phone':
      case 'text':
        return asc ? aValue?.value.localeCompare(bValue?.value) : bValue?.value?.localeCompare(aValue?.value);
      case 'boolean':
      case 'integer':
        return asc ? aValue?.value - bValue?.value : bValue?.value - aValue?.value;
      default:
        return 0;
    }
  });
}
