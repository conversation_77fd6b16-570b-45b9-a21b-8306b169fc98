import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ElementRef, AfterViewInit, OnInit, input, output } from '@angular/core';

@Component({
  selector: 'app-code-input',
  templateUrl: './code-input.component.html',
  styleUrls: ['./code-input.component.scss'],
})
export class CodeInputComponent implements OnInit, AfterViewInit {
  /**
   * Pa<PERSON> will set the number of digits to be entered. Based on that the input fields will be created.
   * Also we will declare an empty array of the same size to hold the values. This is just to make sure
   * that if the parent sets a different number of digits, we will not have any issues.
   */
  totalDigits = input<number>(6);
  disabled = input<boolean>(false);

  codeComplete = output<string>();

  digits: string[] = [];

  @ViewChildren('inputField') inputFields!: QueryList<ElementRef>;

  ngOnInit() {
    this.digits = new Array(this.totalDigits()).fill('');
  }

  ngAfterViewInit() {
    if (this.inputFields.first) {
      // Focusing on the first input when the view loaded
      this.inputFields.first.nativeElement.focus();
    }
  }

  onInput(event: Event, index: number) {
    const input = event.target as HTMLInputElement;
    const value = input.value;
    this.digits[index] = value;

    // focusing on the next input field
    if (index < this.totalDigits() - 1 && input.value) {
      const nextInput = this.inputFields.toArray()[index + 1];
      nextInput.nativeElement.focus();
    }

    // sending the value to the parent component
    if (this.digits.every((d) => d !== '')) {
      this.codeComplete.emit(this.digits.join(''));
    }
  }

  onKeyDown(event: KeyboardEvent, index: number) {
    const key = event.key;
    if (key === 'Backspace') {
      if (!this.digits[index] && index > 0) {
        const prevInput = this.inputFields.toArray()[index - 1];
        prevInput.nativeElement.focus();
      }
    }
  }
}
