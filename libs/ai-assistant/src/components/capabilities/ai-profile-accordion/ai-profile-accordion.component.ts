import { Component, computed, input, output } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';
import { AiAssistantForm } from '../../../core/forms/ai-assistant.form';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { AssistantType, GoalType, Model } from '@vendasta/ai-assistants';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { AiModelConfigurationFormComponent } from '../../ai-model-configuration-form/ai-model-configuration-form.component';
import { AiVoiceConfigurationFormComponent } from '../../ai-voice-configuration-form/ai-voice-configuration-form.component';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { MatIconButton } from '@angular/material/button';

@Component({
  selector: 'ai-profile-accordion',
  imports: [
    AiModelConfigurationFormComponent,
    AiVoiceConfigurationFormComponent,
    MatCardModule,
    MatDividerModule,
    MatExpansionModule,
    MatIconButton,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatOptionModule,
    GalaxyBadgeModule,
    GalaxyFormFieldModule,
    GalaxyTooltipModule,
    TranslateModule,
    ReactiveFormsModule,
    TranslateModule,
  ],
  templateUrl: './ai-profile-accordion.component.html',
  styleUrl: './ai-profile-accordion.component.scss',
})
export class AiProfileAccordionComponent {
  assistantForm = input.required<AiAssistantForm>();
  assistantSupportsVoice = input<boolean>(false);
  isLangChainEnabled = input<boolean | undefined>(undefined);
  availableModels = input.required<Model[] | undefined>();

  rollback = output<string>();

  personalityCapabilityForms = computed(() => {
    return this.assistantForm().controls.capabilities.controls.filter(
      (capabilityForm) => capabilityForm.controls.type.value === GoalType.GOAL_TYPE_PERSONALITY,
    );
  });

  personalityPromptModules = computed(() => {
    return (
      this.assistantForm().controls.capabilities.controls.find(
        (capabilityForm) => capabilityForm.controls.type.value === GoalType.GOAL_TYPE_PERSONALITY,
      )?.controls.promptModules.controls || []
    );
  });

  voiceConfigForm = computed(() => {
    if (this.assistantForm().controls.type.value !== AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST) {
      return undefined;
    }
    return this.assistantForm().controls.voiceConfig;
  });

  modelConfigForm = computed(() => {
    if (this.assistantForm().controls.type.value == AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST) {
      return undefined;
    }
    return this.assistantForm().controls.model;
  });

  handleRollbackClicked(capabilityId: string, event: Event) {
    event.stopPropagation();
    this.rollback.emit(capabilityId);
  }
}
