@use 'design-tokens' as *;

glxy-page {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.confirmation-code {
  text-align: center;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 $spacing-2;
}

.code-input {
  position: relative;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background-color: rgba($light-gray, 0.85);
  border-radius: $default-border-radius;
}

.resend-container {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;
  width: 100%;
}

.btn {
  height: 42px;
  font-size: $font-preset-3-size;
  width: 100%;
  max-width: $media--phone-minimum;
}

.resend-text {
  font-size: $font-preset-3-size;
  color: $secondary-text-color;
}
