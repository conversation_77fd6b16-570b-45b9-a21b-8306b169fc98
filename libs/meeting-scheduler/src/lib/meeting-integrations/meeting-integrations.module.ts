import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConnectWithGoogleButtonComponent } from './connect-with-google-button/connect-with-google-button.component';
import { ConnectWithMicrosoftButtonComponent } from './connect-with-microsoft-button/connect-with-microsoft-button.component';
import { ConnectWithZoomButtonComponent } from './connect-with-zoom-button/connect-with-zoom-button.component';
import { ConnectionStatusModule, ConnectToServiceModule, CustomElementsModule } from '@vendasta/integrations';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { IntegrationStatusComponent } from './integration-status/integration-status.component';
import { GoogleIntegrationStatusComponent } from './google-integration-status/google-integration-status.component';
import { MicrosoftIntegrationStatusComponent } from './microsoft-integration-status/microsoft-integration-status.component';
import { ZoomIntegrationStatusComponent } from './zoom-integration-status/zoom-integration-status.component';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { IntegrationSelectionComponent } from './integration-selection/integration-selection.component';
import { CalendarSelectionComponent } from './calendar-selection/calendar-selection.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GalaxyTooltipModule } from '@vendasta/galaxy/tooltip';
import { DisconnectMeetingIntegrationConfirmationDialogComponent } from './disconnect-meeting-integration-confirmation-dialog/disconnect-meeting-integration-confirmation-dialog.component';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { MatDialogActions, MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { MatProgressSpinner } from '@angular/material/progress-spinner';

@NgModule({
  declarations: [
    ConnectWithGoogleButtonComponent,
    ConnectWithMicrosoftButtonComponent,
    ConnectWithZoomButtonComponent,
    IntegrationStatusComponent,
    GoogleIntegrationStatusComponent,
    MicrosoftIntegrationStatusComponent,
    ZoomIntegrationStatusComponent,
    IntegrationSelectionComponent,
    CalendarSelectionComponent,
    DisconnectMeetingIntegrationConfirmationDialogComponent,
  ],
  exports: [
    ConnectWithGoogleButtonComponent,
    ConnectWithZoomButtonComponent,
    ConnectWithMicrosoftButtonComponent,
    GoogleIntegrationStatusComponent,
    MicrosoftIntegrationStatusComponent,
    ZoomIntegrationStatusComponent,
    IntegrationSelectionComponent,
    CalendarSelectionComponent,
  ],
  imports: [
    CommonModule,
    CustomElementsModule,
    ConnectToServiceModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    ConnectionStatusModule,
    MatRadioModule,
    ReactiveFormsModule,
    MatTooltipModule,
    FormsModule,
    GalaxyTooltipModule,
    GalaxyLoadingSpinnerModule,
    MatDialogActions,
    MatProgressSpinner,
    MatDialogContent,
    MatDialogTitle,
  ],
})
export class MeetingIntegrationsModule {}
