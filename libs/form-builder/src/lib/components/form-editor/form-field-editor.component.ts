import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  computed,
  DestroyRef,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { FieldOptionInterface, FieldType } from '@vendasta/forms_microservice';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { BehaviorSubject, Observable, of, Subject } from 'rxjs';
import { distinctUntilChanged, map, shareReplay, startWith, switchMap, tap, withLatestFrom } from 'rxjs/operators';
import { CustomFormsOptionsComponent } from './form-builder-options.component';
import { FormFieldEditorService } from './form-field-editor.service';
import {
  DefaultValue,
  defaultValueProperty,
  EditableFormField,
  FORM_FIELD_ZERO_VALUES,
  getLabelTranslationKey,
  labelProperty,
  queryParamInvalid,
} from '../../interface';
import { TranslationModule } from '../../translation-module';
import { GalaxyRichTextEditorModule } from '@vendasta/galaxy/rich-text-editor';
import { CanChangeTypePipe } from './canchangetype.pipe';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FieldTypeHintComponent } from './field-type-hint/field-type-hint.component';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import {
  CountryCodeService,
  CountryOption,
  countryOptionsFactoryProvider,
} from '@vendasta/galaxy/utility/country-codes';
import { CountryCode } from 'libphonenumber-js';
import { GalaxyTagsModule } from '@vendasta/galaxy/tags';

export const DEFAULT_COUNTRY_CODE = 'US';

@Component({
  selector: 'form-builder-field-editor',
  templateUrl: 'form-field-editor.component.html',
  styleUrls: ['./form-field-editor.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatDividerModule,
    TranslateModule,
    TranslationModule,
    GalaxyFormFieldModule,
    CustomFormsOptionsComponent,
    GalaxyRichTextEditorModule,
    ReactiveFormsModule,
    CanChangeTypePipe,
    FieldTypeHintComponent,
    NgxMatSelectSearchModule,
    GalaxyTagsModule,
  ],
  providers: [CountryCodeService, countryOptionsFactoryProvider],
})
export class FormFieldEditorComponent implements OnInit, AfterViewInit {
  // indicates if this component is actively in view (used to fix the rich text editor when it comes in and out of view)
  // mat-tab breaks the rich text editor, so we need to pass information to destroy/init it when we change tabs
  // https://github.com/tinymce/tinymce-angular/issues/105
  // https://github.com/tinymce/tinymce-angular/blob/main/stories/materialtabs/MaterialTabs.component.html
  @Input() isActive = false;
  @Input() formChanges$$ = new Subject<void>();
  @Input() field = new EditableFormField(FORM_FIELD_ZERO_VALUES);

  @Output() fieldChanged = new EventEmitter<EditableFormField>();

  fieldEditorForm = new FormGroup({
    fieldType: new FormControl<number>(0),
    label: new FormControl<string>(''),
    placeholder: new FormControl<string>(''),
    defaultValue: new FormControl<DefaultValue>(''),
    required: new FormControl<boolean>(false),
    hidden: new FormControl<boolean>(false),
    dynamic: new FormControl<boolean>(false),
    prefillUrl: new FormControl<string>(''),
    phoneCountryCode: new FormControl(DEFAULT_COUNTRY_CODE),
  });

  defaultControlType: 'text' | 'checkbox' | 'number' | 'date' = 'text';
  fieldType: FieldType = FieldType.FIELD_TYPE_INVALID;
  fieldType$$ = new BehaviorSubject<FieldType | null>(null);
  fieldTypes = FieldType;
  fieldTypeLabel = '';

  showOptions = false;
  options: FieldOptionInterface[] = [];
  showPlaceholder = false;

  isLabelRequired$: Observable<boolean> = of(false);
  labelErrorMsg$: Observable<string> = of('');
  isDefaultRequired$: Observable<boolean> = of(false);
  defaultErrorMsg$: Observable<string> = of('');
  showQueryParam$: Observable<boolean> = of(false);
  queryParamErrorMsg$: Observable<string | null> = of(null);

  searchCountryControl = new FormControl('');

  constructor(
    private readonly formFieldEditorService: FormFieldEditorService,
    private readonly countryCodeService: CountryCodeService,
  ) {}

  private readonly destroyRef = inject(DestroyRef);

  private phoneCountryCodeVal = signal<string>(DEFAULT_COUNTRY_CODE);

  ngOnInit() {
    this.fieldEditorForm.setValue({
      fieldType: this.field.getFieldType(),
      label: this.field.formField.label ? this.field.formField.label : '',
      placeholder: this.field.formField.placeholder ? this.field.formField.placeholder : '',
      defaultValue: this.field.defaultValue || '',
      required: this.field.formField.required ? this.field.formField.required : false,
      hidden: this.field.formField.hidden ? this.field.formField.hidden : false,
      dynamic: this.field.populateFieldDynamically ? this.field.populateFieldDynamically : false,
      prefillUrl: this.field.formField.preFillByUrlQueryParameter
        ? this.field.formField.preFillByUrlQueryParameter
        : '',
      phoneCountryCode: this.field.formField.defaultPhoneIsoCountryCode
        ? this.field.formField.defaultPhoneIsoCountryCode
        : DEFAULT_COUNTRY_CODE,
    });
    this.phoneCountryCodeVal.set(this.field.formField.defaultPhoneIsoCountryCode || DEFAULT_COUNTRY_CODE);

    if (this.field.formField.schema?.mappedField?.systemDefined?.required) {
      this.fieldEditorForm.controls.required.setValue(true);
      this.fieldEditorForm.controls.required.disable();
    }

    if (this.field.formField.schema?.mappedField?.systemDefined?.hidden) {
      this.fieldEditorForm.controls.hidden.setValue(true);
      this.fieldEditorForm.controls.hidden.disable();
    }

    this.setFieldType(this.field.getFieldType());

    this.fieldEditorForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.formUpdated();
    });

    const latestFieldChanges$ = this.formChanges$$.pipe(
      startWith(this.field),
      switchMap(() => of(this.field)),
    );
    const fieldsToValidate$ = latestFieldChanges$.pipe(
      map((field) => field.fieldsToValidate() || new Map<string, boolean>()),
    );
    const formValidationErrors$ = latestFieldChanges$.pipe(
      map((field) => field.fieldValidationErrors() || new Map<string, string>()),
    );

    this.isLabelRequired$ = fieldsToValidate$.pipe(map((fieldsToValidate) => !!fieldsToValidate.get(labelProperty)));
    this.isDefaultRequired$ = fieldsToValidate$.pipe(
      map((fieldsToValidate) => !!fieldsToValidate.get(defaultValueProperty)),
    );

    this.labelErrorMsg$ = formValidationErrors$.pipe(
      map((formValidationErrors) => formValidationErrors.get(labelProperty) || ''),
    );
    this.defaultErrorMsg$ = formValidationErrors$.pipe(
      map((formValidationErrors) => formValidationErrors.get(defaultValueProperty) || ''),
    );
    this.showQueryParam$ = latestFieldChanges$.pipe(map((field) => !!field && field.populateFieldDynamically));
    this.queryParamErrorMsg$ = formValidationErrors$.pipe(
      map((formValidationErrors) => formValidationErrors.get(queryParamInvalid) || null),
    );

    this.fieldEditorForm.controls.phoneCountryCode.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef),
        tap((cc: string | null) => this.phoneCountryCodeVal.set(cc || DEFAULT_COUNTRY_CODE)),
      )
      .subscribe();
  }

  ngAfterViewInit(): void {
    // tinyMCE has problems due to use of iframe, a workaround is to add the
    // *ngIf="isActive === true" on the wrapper element glxy-rich-text-editor.
    // However, this must wait angular before we render it. So we are changing the
    // value here so that state changes occurs in a predictable moment.
    requestAnimationFrame(() => {
      this.isActive = true;
    });
  }

  setFieldType(fieldType: FieldType): void {
    this.fieldType = fieldType;
    this.fieldType$$.next(fieldType);
    this.fieldTypeLabel = getLabelTranslationKey(fieldType);
    this.showOptions =
      this.fieldType === FieldType.FIELD_TYPE_DROPDOWN || this.fieldType === FieldType.FIELD_TYPE_RADIO;
    this.options = this.field.options || [];
    this.showPlaceholder =
      this.fieldType === FieldType.FIELD_TYPE_STRING ||
      this.fieldType === FieldType.FIELD_TYPE_INTEGER ||
      this.fieldType === FieldType.FIELD_TYPE_CURRENCY ||
      this.fieldType === FieldType.FIELD_TYPE_EMAIL ||
      this.fieldType === FieldType.FIELD_TYPE_PHONE ||
      this.fieldType === FieldType.FIELD_TYPE_BUSINESS_SEARCH ||
      this.fieldType === FieldType.FIELD_TYPE_TEXT_AREA;

    switch (this.field.getFieldType()) {
      case FieldType.FIELD_TYPE_INTEGER:
      case FieldType.FIELD_TYPE_CURRENCY:
        this.defaultControlType = 'number';
        break;
      case FieldType.FIELD_TYPE_DATE:
        this.defaultControlType = 'date';
        break;
      case FieldType.FIELD_TYPE_BOOLEAN:
        this.defaultControlType = 'checkbox';
        break;
      case FieldType.FIELD_TYPE_TAG:
        this.isLabelRequired$ = of(false);
        break;
      default:
        this.defaultControlType = 'text';
        break;
    }
  }

  cancelEdits(): void {
    this.formFieldEditorService.cancelEdits();
  }

  onOptionsChanged(field: EditableFormField, $event: FieldOptionInterface[]): void {
    field.options = $event;
    this.fieldChanged.emit(field);
  }

  formUpdated(): void {
    if (this.field.formField?.schema?.unmappedField?.type) {
      this.field.formField.schema.unmappedField.type = this.fieldEditorForm.controls.fieldType.value || 0;
    } else if (this.field.formField?.schema?.mappedField?.type) {
      this.field.formField.schema.mappedField.type = this.fieldEditorForm.controls.fieldType.value || 0;
    }

    this.field.formField.label = this.fieldEditorForm.controls.label?.value
      ? this.fieldEditorForm.controls.label.value
      : '';
    this.field.formField.placeholder = this.fieldEditorForm.controls.placeholder?.value
      ? this.fieldEditorForm.controls.placeholder.value
      : '';
    this.field.defaultValue = this.fieldEditorForm.controls.defaultValue.value;
    this.field.formField.required = this.fieldEditorForm.controls.required.value || undefined;
    this.field.formField.hidden = this.fieldEditorForm.controls.hidden.value || undefined;
    this.field.populateFieldDynamically = this.fieldEditorForm.controls.dynamic.value || false;
    if (this.field.populateFieldDynamically) {
      this.field.formField.preFillByUrlQueryParameter = this.fieldEditorForm.controls.prefillUrl?.value
        ? this.fieldEditorForm.controls.prefillUrl.value
        : '';
    }
    this.field.formField.defaultPhoneIsoCountryCode =
      this.fieldEditorForm.controls.phoneCountryCode.value || DEFAULT_COUNTRY_CODE;

    this.setFieldType(this.fieldEditorForm.controls.fieldType.value || FieldType.FIELD_TYPE_INVALID);
    this.fieldChanged.emit(this.field);
  }

  countryOptions$: Observable<CountryOption[]> = this.countryCodeService.countryOptions$.pipe(
    distinctUntilChanged(),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );
  filteredCountryOptions$: Observable<CountryOption[]> = this.searchCountryControl.valueChanges.pipe(
    startWith(''),
    withLatestFrom(this.countryOptions$),
    map(([filterValue, countryOptions]) => {
      return countryOptions.filter((c) => {
        return (
          c.callingCode.includes(filterValue || '') ||
          c.countryCode.toLowerCase().includes((filterValue || '').toLowerCase()) ||
          c.countryName.toLowerCase().includes((filterValue || '').toLowerCase())
        );
      });
    }),
  );

  selectedCountry = computed(() => {
    return this.countryCodeService.getCountryOption(this.phoneCountryCodeVal() as CountryCode);
  });

  presetTags: string[] = [];
  tagsSearchText = '';

  tagsFilteredBySearchText(): string[] {
    return this.presetTags.filter((tag) => tag.toLowerCase().includes(this.tagsSearchText.toLowerCase()));
  }

  tagsSearchTextChanged(searchText: string): void {
    this.tagsSearchText = searchText;
  }
}
