{"AI_ASSISTANT": {"SHARED": {"STATUS": {"ACTIVE": "Active", "INACTIVE": "Inactive"}, "COMING_SOON": "Coming soon", "BETA": "BETA", "SAVE": "Save", "CANCEL": "Cancel", "DELETE": "Delete", "CLOSE": "Close", "UPGRADE": "Upgrade to gain access", "MODIFIED": "Modified", "UNSAVED_CHANGES": {"TITLE": "Unsaved changes", "MESSAGE": "Are you sure you want to leave? You will lose any unsaved changes.", "CONFIRM": "Discard changes"}, "DELETE_ASSISTANT_CONFIRMATION": {"TITLE": "Delete assistant?", "MESSAGE": "Are you sure you want to delete this assistant? This action cannot be undone. Custom capabilities will not be deleted, and will remain available for use on other assistants.", "CONFIRM": "Delete", "CANCEL": "Cancel"}}, "WORKFORCE": {"TITLE": "AI Workforce", "CREATE_ASSISTANT": "Create", "CONFIGURE_BUTTON": "Configure", "TRY_IT": "Try it", "CREATE_WITH_AI": "Create with AI", "RESPOND_WITH_AI": "Respond with AI", "CHANNEL": {"ACTIVE": "active", "NOT_YET_ACTIVE": "not active"}, "DEFAULT_ASSISTANTS": {"CUSTOM_ASSISTANT": {"ERRORS": {"CREATE_FAILED": "Error creating custom assistant"}, "TITLE": "Custom assistant", "DESCRIPTION": "A custom assistant. Customize its personality, capabilities, and connected knowledge sources."}, "AUTOMATED_ASSISTANT": {"TITLE": "Automated assistant", "SUBTITLE": "Chat receptionist", "DESCRIPTION": "Provides a fast, helpful response to anyone messaging your business. Answers questions, promotes your products and services, and captures contact info for your team to follow up."}, "CHAT_RECEPTIONIST": {"TITLE": "Chat receptionist", "SUBTITLE": "", "DESCRIPTION": "Provides a fast, helpful response to anyone messaging your business. Answers questions, promotes your products and services, and captures contact info for your team to follow up."}, "VOICE_RECEPTIONIST": {"TITLE": "Voice receptionist", "SUBTITLE": "", "DESCRIPTION": "Answers calls to your business with a professional, friendly voice. <PERSON>les inquiries, promotes your products and services, and captures new leads—24/7.", "TRY_IT_TITLE": "Try the voice receptionist", "TRY_IT_INSTRUCTIONS": "Enable the voice receptionist and call your number, <strong>{{phoneNumber}}</strong>.", "ERRORS": {"PHONE_NUMBER": "Error fetching phone number", "ASSISTANT_CONNECTIONS": "Error fetching assistant connections"}, "STATUS": {"PROVISIONING": {"TITLE": "Provisioning", "MESSAGE": "Your voice receptionist is being provisioned and will be ready in a few minutes."}, "INACTIVE": {"TITLE": "Inactive", "MESSAGE": "Your voice receptionist is inactive and will not answer calls."}, "UNAVAILABLE": {"TITLE": "Unavailable", "MESSAGE": "Your voice receptionist is unavailable."}}, "MINUTES_USED": "{{ used }}/{{ limit }} minutes used", "USAGE_PROGRESS": "{{ startDay }} to {{ endDay }}"}, "CONTENT_CREATOR": {"TITLE": "Content creator", "SUBTITLE": "", "DESCRIPTION": "Creates content for your website blog, and social media platforms. Can help you draft, schedule and post content that drives awareness of your brand and engagement with current and future customers."}, "REVIEW_MANAGER": {"TITLE": "Review manager", "SUBTITLE": "", "DESCRIPTION": "Crafts a thoughtful response to reviews to manage your online reputation. Provides smart suggestions to get more positive reviews and handle negative ones."}, "SEO_ANALYST": {"TITLE": "SEO analyst", "SUBTITLE": "Coming soon", "DESCRIPTION": "Analyzes your keywords and suggests improvements to optimize traffic from search engines and ads."}, "SALES_COACH": {"TITLE": "Sales coach", "SUBTITLE": "Coming soon", "DESCRIPTION": "Analyzes your recorded sales calls to highlight keywords and areas for improvement, helping you hone your skills and close deals more effectively."}}}, "GOALS": {"CONFIGURE_GOAL": {"TITLE": "Configure capability", "NAME": "Name", "DESCRIPTION": "Description", "TYPE": "Type", "SUPPORTED_CHANNELS": "Supported channels", "CHANNELS": {"ALL_CHANNELS": "All channels", "PLATFORM_CHAT": "Platform chat", "WEB_CHAT": "Web chat", "SMS": "SMS", "EMAIL": "Email", "VOICE": "Voice", "FACEBOOK": "Facebook", "INSTAGRAM": "Instagram", "WHATSAPP": "WhatsApp"}, "PROMPT": "Prompt", "FUNCTIONS": "Tools", "FUNCTIONS_DESCRIPTION": "A list of tools which the AI assistant can use as part of this capability.", "ADD_FUNCTION": "+ Add a tool", "SAVE_ERROR": "Error saving capability", "SAVE_SUCCESS": "Capability saved successfully", "FORM_INVALID": "Please fill out all required fields", "NAME_HELP_TEXT": "Enter a short, descriptive name for this capability configuration. This helps you identify it later", "DESCRIPTION_HELP_TEXT": "This is for your reference only and won’t be used by the AI.", "PROMPT_HELP_TEXT": "Define what you want the AI to do and how you want it to use the linked tools.", "PROMPT_TOOLTIP_TEXT": "This is a prompt section that is combined with other prompts of this AI assistant. Clearly and concisely explain the steps you'd like your AI to take, and which tool(s) to use under which circumstances. Be careful your instructions do not conflict with other capabilities on the assistant.", "PROMPT_PLACEHOLDER": "## Goal: When the user asks about... you can...\n- Step 1:  Ask the user...\n- Step 2: Then call [tool name] ...\n- Step 3: Then use the response from the tool to..."}, "ADD_GOAL": {"ADD_A_CAPABILITY": "Add a capability", "NEW_CAPABILITY": "New capability", "ERROR_LOADING_CAPABILITIES": "Error loading capabilities", "ERROR_LOADING_CAPABILITY": "Error loading capability"}}, "FUNCTIONS": {"ADD_FUNCTION": {"ADD_A_FUNCTION": "Add a tool", "NEW_FUNCTION": "New tool", "ERROR_LOADING_FUNCTIONS": "Error loading tools"}, "CREATE_FUNCTION": {"TITLE": "Create a tool", "NAME": "Name", "NAME_PATTERN_ERROR": "this should be less than 64 characters and only contain a-z, A-Z, 0-9, underscores or dashes", "DESCRIPTION": "Description", "GENERATE_FROM_CURL": "Generate from cURL", "GENERATE": "Generate", "EXPORT_TO_CURL": "<PERSON><PERSON> as c<PERSON><PERSON>", "METHOD": "Method", "URL": "URL", "URL_PLACEHOLDER": "https://api.example.com/api", "URL_ERROR": "Please enter a valid URL that begins with http:// or https:// (e.g., http://api.example.com/getData).", "URL_HINT": "Enter the full API endpoint, starting with http:// or https:// (e.g., https://api.example.com/getData).", "HEADERS": "Headers", "NO_HEADERS": "No headers added yet. Click \"Add header\" to add HTTP headers for this tool.", "HEADER_NAME_PLACEHOLDER": "Header name", "HEADER_VALUE_PLACEHOLDER": "Value", "ADD_HEADER": "+ Add header", "ADD_COMMON_HEADER": "Add common header", "PARAMETERS": "Parameters", "NO_PARAMETERS": "No parameters added yet. Click \"Add Parameter\" to add a parameter to this tool.", "ADD_PARAMETER": "+ Add parameter", "NO_API_CHECKBOX": "This tool does not call an API", "NO_API_HINT": "use this when you just want to format the AI's response", "CREATE": "Create", "NAME_HELP_TEXT": "Enter a short, unique name for this tool that matches what the tool will do.", "METHOD_TOOLTIP_TEXT": "Typically, you'll use GET for retrieving data, POST for creating new data, PUT/PATCH for updating existing data, and DELETE for removing data. However, make sure to use the method that your endpoint is expecting.", "DESCRIPTION_HELP_TEXT": "Give a brief description of what the tool does to help the AI understand how to use it.\n", "METHOD_HELP_TEXT": "The method your API endpoint expects.", "PARAMETERS_HELP_TEXT": "Parameters are additional pieces of information that help the API understand your request. These are typically added to the URL or request body to filter, sort, or provide extra details for the API to process.", "URL_HELP_TEXT": "Enter the full API endpoint, starting with http:// or https:// (e.g., https://api.example.com/users).", "HEADERS_HELP_TEXT": "API headers provide additional information to the server about the request or response. These should be the headers required by your API, such as authorization tokens, content types, or other custom metadata necessary for processing the request."}, "SNACKBAR": {"SUCCESS_SAVING_TOOL": "<PERSON><PERSON> saved successfully", "ERROR_FETCHING_FUNCTION": "Error fetching function", "MISSING_FIELDS": "There are missing fields in the form", "SAVE_ERROR": "Error saving tool", "TOO_MANY_NESTED_PARAMETERS": "Too many nested object parameters"}, "EDIT_FUNCTION": {"TITLE": "Edit tool", "DONE": "Done"}, "PARAMETER": {"KEY_LABEL": "Parameter key", "KEY_PLACEHOLDER": "Enter the name of the parameter", "DESCRIPTION_LABEL": "Description", "DESCRIPTION_PLACEHOLDER": "Provide a description for the parameter that will help the AI assistant understand how to fill it in dynamically", "TYPE_LABEL": "Type", "LOCATION_LABEL": "Location", "FIELDS": "fields", "NO_FIELDS": "No {{name}} fields added yet. Click \"Add field\" to add a field.", "ADD_FIELD": "+ Add field to: {{name}}", "ADD_FIELD_TO": "+ Add field to: {{name}}", "ADD_FIELD_NO_NAME": "+ Add field", "HELP_TEXT": {"NAME": "Enter the name of the parameter", "LOCATION": "Choose where this parameter goes in the request (e.g., body for POST data, query for URL params).", "TYPE": "Select the expected data type for this parameter. This helps ensure the correct format and guides how the AI will use it.", "DESCRIPTION": "Provide a description for the parameter that will help the AI assistant understand how to fill it in dynamically before making the request"}, "ARRAY_ITEMS": "array items"}}, "KNOWLEDGE_ACCESS": {"ASSOCIATED_PROFILE": "Associated profile", "MANAGE_PROFILE": "Manage profile", "NAME": "Name", "DESCRIPTION": "Description", "CONTENT": "Content", "CONTENT_HINT": "Provide frequently asked questions and answers or other text the AI can use in responses. This does not give special instructions to AI", "KNOWLEDGE_NAME": "Knowledge name", "URL": "URL", "MODE": "Mode", "LAST_REFRESHED": "Last refreshed:", "REFRESH": "Refresh", "REFRESHING_KNOWLEDGE": "Refreshing knowledge...", "MODE_SINGLE": "Single page", "MODE_RECURSIVE": "Follow links", "MODE_SITEMAP": "Sitemap", "KNOWLEDGE_ACCESS": "Knowledge access", "ADD_KNOWLEDGE": "Add knowledge", "ERROR_CREATING_KNOWLEDGE": "Error creating knowledge source.", "SUCCESS_CREATING_KNOWLEDGE": "Knowledge source created successfully.", "UNABLE_TO_SAVE": "There are some empty required fields.", "REFRESH_CONFIRMATION_TITLE": "Refresh this website?", "REFRESH_CONFIRMATION_MESSAGE": "This will update the content from the website. Pages found previously will be replaced.", "FINDING_PAGES": "Finding web pages...", "TRAINING": "Training queued...", "FEW_MINUTES": "This may take a few minutes", "WEB_PAGES": "Web pages", "TRAIN": "Train", "PICK_PAGES": "Pick pages to train the AI on:", "KNOWLEDGE_TYPES": {"TEXT": {"TITLE": "Text", "DESCRIPTION": "FAQs or custom info"}, "WEBSITE": {"TITLE": "Website", "DESCRIPTION": "Retrieve info from a website", "UNABLE_TO_FIND": "No pages found at this URL. Please verify the address or try refreshing the content if you're certain it's correct."}}}, "SETTINGS": {"SETUP": "Setup", "SETUP_CONNECTIONS": "Setup {{ connectionName }}", "SETTINGS": "Settings", "LOADING": "Loading assistant...", "TITLE": "Configure {{<PERSON><PERSON><PERSON>}}", "CREATE_CUSTOM_ASSISTANT_TITLE": "Create Custom Assistant", "CREATE": "Create", "APPEARANCE": "Appearance", "PROFILE_CAPABILITY": {"TITLE": "Profile", "PURPOSE": {"TITLE": "Purpose", "HELP_TEXT": "Define your AI's role and behavioural guidelines."}, "MODEL": {"RECOMMENDED": "Recommended", "TITLE": "Generative AI", "HELP_TEXT": "Define the model that powers the AI's responses.", "SELECT": {"TITLE": "Written content generation model", "DEFAULT": "Use recommended"}}, "SPEECH": {"TITLE": "Speech", "HELP_TEXT": "Define the model that powers speech and the sound of the AI's voice."}}, "ROLLBACK": "Rollback to managed", "ROLLBACK_CAPABILITY_TITLE": "Rollback Capability", "ROLLBACK_CAPABILITY_CONFIRMATION": "Are you sure you want to rollback this capability to its managed version?", "APPEARANCE_DESCRIPTION": "How your AI's name and avatar appear in Web Chat and SMS", "GOALS_INSTRUCTIONS": "Goals and instructions", "KNOWLEDGE_SOURCES": "Knowledge sources", "KNOWLEDGE_SOURCES_DESCRIPTION": "Choose what information the AI assistant can reference when responding", "LEARN_MORE": "Learn more", "SOMETHING_WENT_WRONG": "Something went wrong.", "ASSISTANT_UPDATED": "Assistant updated successfully.", "ASSISTANT_CREATED": "Assistant created successfully.", "ASSISTANT_DELETED": "Assistant deleted successfully.", "ASSISTANT_UPDATE_ERROR": "Error updating Assistant.", "ASSISTANT_DELETE_ERROR": "Error deleting Assistant.", "ASSISTANT_NOT_FOUND": "Assistant not found.", "ERROR_FETCHING_ASSISTANT": "Error fetching assistant.", "ASSISTANT_VALIDATION_ERROR": "Please correct the invalid fields before saving.", "ASSISTANT_TITLE": "AI assistant", "DEFAULT_ASSISTANT_NAME": "<PERSON>t Receptionist", "ASSISTANT_NAME": "Name", "ASSISTANT_NAME_TOOLTIP": "It's recommended you include a title after the name that clearly indicates to your users that they are interacting with an AI, and not a human employee, to set proper expectations.", "ASSISTANT_AVATAR": "Avatar", "ASSISTANT_PRIMARY_GOAL": "Primary goal", "CUSTOM_CAPABILITIES": "Custom capabilities", "CAPABILITIES": "Capabilities", "ADD_CAPABILITY": "Add a capability", "MOVE_UP": "Move up", "MOVE_DOWN": "Move down", "REMOVE": "Remove", "REMOVE_CAPABILITY_TITLE": "Remove Capability", "REMOVE_CAPABILITY_CONFIRMATION": "Are you sure you want to remove this capability?", "ASSISTANT_ADDITIONAL_INSTRUCTIONS": "Additional instructions", "ASSISTANT_ADDITIONAL_INSTRUCTIONS_DESCRIPTION": "Add any additional instructions for your AI assistant to follow.", "ASSISTANT_ADDITIONAL_INSTRUCTIONS_LIMIT_EXCEEDED": "Too many characters", "VOICE_BETA_BANNER_MESSAGE": "Voice AI is in beta preview — Calls are limited to 5 minutes per call and 300 minutes of calling per month. If you exceed this monthly limit, the feature might experience rate limits.", "CHANNELS": {"TITLE": "Channels", "SUBTITLE": "Choose which communication channels your AI assistant is assigned to.", "FETCH_ERROR": "Error fetching channels.", "FETCH_WIDGET_NAME_WARNING": "Unable to load widget names.", "WEB_CHAT": "Web Chat", "SMS": "SMS", "NO_CHANNELS_AVAILABLE": "No channels available. Upgrade to gain access."}, "SYSTEM_ASSISTANT": {"DESCRIPTION": "In-platform AI assistant", "ALERT_INFORMATION": "Aurora is the AI that appears in the top navigation of Partner Center that all partners can chat with privately."}, "ENABLE_LEAD_CAPTURE": {"DESCRIPTION": "Capture lead information", "INFORMATION": "The AI will prioritize getting a name and contact information, create a new contact, and let the user know someone on your team will follow up."}, "ENABLE_MEETING_BOOKING": {"LABEL": "Select event link to book with:", "DESCRIPTION": "Book calendar events", "INFORMATION": "When selected, users will be guided through booking a calendar event during the conversation. We'll collect their preferred time and show real-time availability.", "MANAGE_EVENT_LINKS": "Manage event links", "QUESTION_1": "When \"Capture lead information\" is also enabled:", "ANSWER_1": "The assistant will first collect the user's name, email, and optionally their phone number before scheduling. This ensures lead information is saved even if the user abandons the process.", "QUESTION_2": "When \"Capture lead information\" is disabled:", "ANSWER_2": "The assistant will only gather the essential details needed for booking—typically name and email.", "QUESTION_3": "Will it ask for a phone number twice?", "ANSWER_3": "No, the assistant intelligently avoids redundancy. If a phone number is gathered during lead capture and is also needed for booking, it will reuse the same information."}, "IMAGE_UPLOAD": {"CHANGE_APPEARANCE": "Change your AI assistant's appearance", "REMOVE_IMAGE": "Remove image", "HINT": "Pick an assistant profile picture.", "CHANGE": "Change", "REMOVE": "Remove"}, "ENABLE_LEAD_CAPTURE_DIALOG": {"INFORMATION": "Your AI has the mission of capturing a lead for your team to follow up with and turn into a customer. It will ask for their name and phone number, or email address as a fallback.", "INFORMATION_2": "The moment a lead provides single piece of contact info – phone number or email, a new contact will be created in the CRM, along with their name, any other contact details they provided (address, city, state/province, zip/postal code, country), what URL the conversation was captured on, and any UTM fields present – which is great for attributing leads to certain ad campaigns.", "INFORMATION_3": "If you disable this option, the AI assistant will act more like a help agent and answer questions, but will not ask for contact information, nor tell the user anyone will follow up, and leads will not be captured in the CRM."}, "ADDITIONAL_INSTRUCTIONS_DIALOG": {"TITLE": "Additional instructions information", "INFORMATION_WARNING": "Warning:", "INFORMATION": "These instructions will affect the main instructions and conversational behavior of your AI assistant. Make sure to test your assistant thoroughly after making any changes, because this has the potential for unintended consequences for your AI assistant's behavior. Use plain text or markdown format.", "EXAMPLE_HEADING": "Example instructions that you can use:", "EXAMPLE_1": "Link sharing: Describe when the AI assistant should direct someone to a link, like if they want to book a meeting or create a support ticket.", "EXAMPLE_2": "Ask additional qualifying questions, like if the user is within service area, or more details about their project.", "EXAMPLE_3": "Adjust the assistant's tone and personality, to match your brand voice.", "EXAMPLE_4": "Localizations – for example, you could ask it to use British English instead of American English.", "EXAMPLE_5": "Adjust the conversation flow, like ask for both the phone number and email address, or ask for the email address first.", "EXAMPLE_6": "Ask for additional contact fields; these fields will be added to the contact if found within the conversation:", "FIELD_1": "First name", "FIELD_2": "Last name", "FIELD_3": "Phone number", "FIELD_4": "Email address", "FIELD_5": "Address", "FIELD_6": "City", "FIELD_7": "State/Province", "FIELD_8": "Zip/Postal code", "FIELD_9": "Country", "DO_NOT_WORK_HEADING": "Instructions that do not work yet:", "DO_NOT_WORK_DESCRIPTION": "You cannot make the AI assistant perform actions outside of the conversation. For example, you cannot say \"If a customer asks for [employee], then notify that user.\""}, "BUSINESS_PROFILE": {"BUSINESS_PROFILE": "Business Profile", "ALLOW_ACCESS": "Allow access to your business profile", "DESCRIPTION": "Answer questions about your business contact information, hours, products and services you offer.", "MANAGE_BUSINESS_PROFILE": "Manage business profile", "LEARN_MORE": {"TITLE": "Learn more", "WHAT_INFORMATION_QUESTION": "What information is made available to your AI assistant?", "WHAT_INFORMATION": "These fields are shared from your business profile:", "BUSINESS_NAME": "Business name", "ADDRESS_SERVICE_AREAS": "Address and/or service areas", "WEBSITE": "Website", "PHONE_NUMBER": "Phone number", "CATEGORIES": "Categories", "HOURS": "Hours", "SERVICES_OFFERED": "Services offered", "SHORT_DESCRIPTION": "Short description", "LONG_DESCRIPTION": "Long description", "SOCIAL_MEDIA_URLS": "Social media URLs", "BOOKING_URL": "Booking URL"}}, "VOICE_CONFIG": {"VOICE_CONFIG": "Voice Configuration", "FAMILY": "Provider", "FAMILY_DESCRIPTION": {"ASTRA": "Multilingual, less expressive, slower speaking", "HORIZON": "Multilingual, very expressive", "ECHOES": "English-only, less expressive, slower response times", "GPT_RT_PREVIEW_SEPT_2024": "Multilingual, less expressive, slower speaking", "GPT_RT_PREVIEW_DEC_2024": "Multilingual, very expressive", "DEEPGRAM_AURA": "English-only, more accurate, less expressive, slower response times", "DEEPGRAM_AURA_2": "English-only, expressive, fast response times"}, "VOICE": "Voice", "GENDER": {"MALE": "Male", "FEMALE": "Female"}, "REGION": {"US": "US", "UK": "UK", "IRELAND": "Ireland", "PHILIPPINES": "Philippines", "AUSTRALIA": "Australia"}, "RECOMMENDED": "Recommended", "ADVANCED": "Advanced voice options", "TURN_DETECTION": {"THRESHOLD": {"LABEL": "Interruption threshold", "DESCRIPTION": "This threshold helps the AI tell the difference between background noise and a human speaking, with a higher value meaning the AI is less likely to be interrupted. Recommended range is 0.60 to 0.75"}, "PREFIX_PADDING": {"LABEL": "Prefix padding", "DESCRIPTION": "Specifies the amount of audio (in milliseconds) to include before the detected start of speech. This ensures that the beginning of the speech isn't cut off."}, "SILENCE_DURATION": {"LABEL": "Silence duration", "DESCRIPTION": "Determines the length of silence (in milliseconds) that indicates the end of speech. Adjusting this helps in managing how quickly the AI responds after a user stops speaking."}}}, "CUSTOM_KNOWLEDGE": {"AI_KNOWLEDGE_BASE": "AI knowledge base", "MANAGE_KNOWLEDGE": "Manage knowledge", "MANAGE_KNOWLEDGE_DESCRIPTION": "Create and manage facts and information that can be added to AI assistants", "REFRESH_OPTIONS": "Refresh options", "ADD_KNOWLEDGE": "Add knowledge", "NEW_KNOWLEDGE": "New knowledge", "SELECT_KNOWLEDGE": "Select knowledge", "CUSTOM_KNOWLEDGE": "Q&A", "DESCRIPTION": "Provide additional facts and answers to frequently asked questions your Chat Receptionist can use when responding to inquiries.", "MAX_CHARACTER_EXCEEDED": "Maximum character limit exceeded", "APPLICATION_SETTINGS": "Settings", "APPS_WARNING": "This knowledge is currently being used by the following:", "NO_APPS": "This knowledge is not currently being used.", "EDIT": {"TITLE": "Custom knowledge", "NAME": "Name", "CONTENT": "Content"}, "DELETE": {"TITLE": "Delete custom knowledge?"}}, "CONNECTIONS": {"WEBCHAT_CREATING": "Webchat being created. Please check back in a minute.", "RESPOND_TO": "Respond to {{ connectionName }}", "TRY_MY_LISTING": "Enable and Try"}, "SALES_COACH": {"SALES_METHODOLOGY": {"TITLE": "Sales methodology", "MEDDPICC": "MEDDPICC", "BANT": "BANT", "SANDLER": "<PERSON><PERSON> Selling System"}, "DISPLAY_NAME_LABEL": "Meeting display name", "DISPLAY_NAME_DESCRIPTION": "The Sales Coach will use this name when joining meetings", "LOG_CRM_ACTIVITY": "Log meeting activity in the CRM", "LOG_CRM_ACTIVITY_DESCRIPTION": "The activity will be associated to the contacts invited to the meeting. The activity will provide a link to view the meeting details including the recording and transcription."}, "VIEW_RECORDED_MEETINGS": "View recorded meetings"}}}