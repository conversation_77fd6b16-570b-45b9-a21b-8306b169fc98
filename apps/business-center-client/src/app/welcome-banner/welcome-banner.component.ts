import { Component, computed, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { combineLatest, firstValueFrom, map, of, switchMap, tap } from 'rxjs';
import { AccountGroupService } from '../account-group';
import { AppConfigService } from '../app-config.service';
import { ImageService } from '../core/image.service';
import { isAccountGroup, LocationsService } from '../locations';
import { PageAccessService, PageId } from '../page-access';
import { SocialConnectionsService } from '../social-connections/social-connections.service';
import { BusinessAppBuildingComponent } from '../shared/business-app-building/business-app-building.component';

const SOCIAL_CONNECTED_KEY = 'social-connected';

@Component({
  selector: 'bc-welcome-banner',
  templateUrl: './welcome-banner.component.html',
  styleUrls: ['./welcome-banner.component.scss'],
  imports: [TranslateModule, MatIconModule, RouterModule, BusinessAppBuildingComponent],
})
export class WelcomeBannerComponent {
  readonly cloudImgSrc = this.imageService.getImageSrc('cloud_outline.svg');
  readonly googleMyBusinessSrc = this.imageService.getImageSrc('connections/google_my_business.png');
  readonly facebookSrc = this.imageService.getImageSrc('connections/facebook.png');

  readonly showActions = signal(true);
  readonly socialIsConnected = signal(false);
  readonly showConnectActions = computed(() => this.showActions() && !this.socialIsConnected());
  readonly isBrand = toSignal(this.locationsService.isCurrentLocationABrand$);
  readonly businessAppName = toSignal(this.appConfig.businessAppName$);
  readonly connectionsUrl = toSignal(
    this.accountGroupService.currentAccountGroupId$.pipe(
      map((agid) => `/account/location/${agid}/settings/connections`),
    ),
  );

  constructor(
    private imageService: ImageService,
    private appConfig: AppConfigService,
    private accountGroupService: AccountGroupService,
    private locationsService: LocationsService,
    private socialConnectionsService: SocialConnectionsService,
    private pageVisibilityService: PageAccessService,
  ) {
    this.locationsService.currentLocation$
      .pipe(
        takeUntilDestroyed(),
        switchMap((location) => {
          if (isAccountGroup(location)) {
            return combineLatest([
              this.accountGroupService.currentAccountGroup$,
              this.pageVisibilityService.isPageAccessible$(PageId.social_connections),
            ]).pipe(
              tap(([accountGroup, accessToSocialConnections]) => {
                this.configureWelcomeBanner(accountGroup.accountGroupId, accessToSocialConnections);
              }),
            );
          } else {
            this.configureWelcomeBanner();
            return of(null);
          }
        }),
        takeUntilDestroyed(),
      )
      .subscribe();
  }

  private configureWelcomeBanner(accountGroupId: string = null, accessToSocialConnections = false): void {
    if (!accountGroupId || !accessToSocialConnections) {
      this.showActions.set(false);
    } else {
      if (this.socialConnectionsConnected(accountGroupId)) {
        this.socialIsConnected.set(true);
      }
      this.getSocialConnections(accountGroupId);
    }
  }

  private socialConnectionsConnected(accountGroupId: string): boolean {
    // This will be set only when the user have both GMB and Facebook connected
    return !!localStorage.getItem(`${accountGroupId}-${SOCIAL_CONNECTED_KEY}`);
  }

  private getSocialConnections(accountGroupId: string): void {
    firstValueFrom(
      combineLatest([this.socialConnectionsService.gmbConnected$, this.socialConnectionsService.facebookConnected$]),
    ).then(([gmbConnected, facebookConnected]) => {
      if (gmbConnected && facebookConnected) {
        this.setBannerFlag(accountGroupId);
      } else {
        this.socialIsConnected.set(false);
        localStorage.removeItem(`${accountGroupId}-${SOCIAL_CONNECTED_KEY}`);
      }
    });
  }

  private setBannerFlag(accountGroupId: string): void {
    localStorage.setItem(`${accountGroupId}-${SOCIAL_CONNECTED_KEY}`, 'connected');
  }
}
