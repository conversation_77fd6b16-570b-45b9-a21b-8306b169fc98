import { Component, Input, ViewChildren, QueryList, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { DynamicPostCardComponent } from '../dynamic-post-card/dynamic-post-card.component';
import { MAX_ALLOWED_POSTS, POSTHOG_KEYS } from '../../../composer/constants';
import { ProductAnalyticsService } from '@vendasta/product-analytics';
import { Posts } from '../dynamic-posts-models';
import {
  CampaignService,
  CreateCampaignRequestInterface,
  CreateCampaignResponse,
  PostMediaV2Interface,
  PostTypeV2,
  SocialPostRequestInterface,
  SocialPostsV2Service,
  SocialPostV2Interface,
} from '@vendasta/social-posts';
import { ConfigService } from '../../../core/config/config.service';
import { firstValueFrom, take } from 'rxjs';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { Media, MediaType, PostScheduleType } from '../../../pages/posts/ai-bundle-posts/model';
import { FileType } from '../../../composer/interfaces';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-dynamic-posts-container',
  standalone: false,
  templateUrl: './dynamic-posts-container.component.html',
  styleUrl: './dynamic-posts-container.component.scss',
})
export class DynamicPostsContainerComponent {
  @Input() postType;
  @Input() services;
  @Input() posts: Posts[];
  @Output() postSubmitted = new EventEmitter<void>();
  @Input() generatedCampaignTitle: string;
  @Output() addAnotherPost = new EventEmitter<void>();
  @ViewChildren(DynamicPostCardComponent) postCards: QueryList<DynamicPostCardComponent>;
  isEditing = false;
  isContentGenerating = false;
  maxPost = MAX_ALLOWED_POSTS;
  partnerId: string;
  isTitleValid = true;
  showSpinner = false;

  constructor(
    private productAnalyticsService: ProductAnalyticsService,
    private configService: ConfigService,
    private snackService: SnackbarService,
    private campaignService: CampaignService,
    private socialPostServiceV2: SocialPostsV2Service,
    private translateService: TranslateService,
  ) {}

  ngOnInit() {
    this.configService.partnerId$.pipe(take(1)).subscribe((partnerId) => {
      this.partnerId = partnerId;
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['posts'] && this.posts?.length) {
      this.isContentGenerating = false;
    }
  }

  postDeleted(event: { index: number; isLastPost: boolean }) {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.SOCIAL_CAMPAIGN_DELETE_POST, 'user', 'click', 0);
    this.posts.splice(event.index, 1);
  }

  toggleEdit() {
    this.isEditing = !this.isEditing;
  }

  generateCustomId() {
    return `${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  getPostTypeFromMedia(media: PostMediaV2Interface[]): PostTypeV2 {
    if (!media?.length) {
      return PostTypeV2.POST_TYPE_TEXT;
    }
  }

  private mapMedia(medias: Media[]): PostMediaV2Interface[] {
    return (
      medias?.map((m) => ({
        mediaUrl: m.mediaUrl,
        mediaType:
          m.mediaType === MediaType.IMAGE || m.mediaType.toString() === FileType.IMAGE
            ? 'MEDIA_TYPE_IMAGE'
            : 'MEDIA_TYPE_VIDEO',
      })) || []
    );
  }

  async saveCampaign() {
    this.showSpinner = true;
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.SOCIAL_CAMPAIGN_SAVE_POSTS, 'user', 'click', 0);

    const postsCards = this.postCards.toArray();
    const scheduledCards = postsCards.filter((card) =>
      card.postContent?.some((content) => content?.postMode === PostScheduleType.SCHEDULE),
    );

    const draftCards = postsCards.filter((card) =>
      card.postContent?.some((content) => content?.postMode === PostScheduleType.DRAFT),
    );

    const createCampaign: CreateCampaignRequestInterface = {
      name: this.generatedCampaignTitle.trim(),
      businessId: this.configService.accountGroupId,
      partnerId: this.partnerId,
    };

    try {
      const response: CreateCampaignResponse = await firstValueFrom(
        this.campaignService.createCampaign(createCampaign),
      );

      const campaignId = response?.campaignId;
      if (scheduledCards?.length > 0) {
        this.handleSchedulePost(campaignId, scheduledCards);
      } else if (draftCards?.length > 0) {
        //This condition is to handle draft posts
        this.showSpinner = false;
        //For this I not used translate service because this is a temporary thing.
        return this.snackService.openErrorSnack('Draft posts are not supported yet. Please schedule.');
      }
    } catch (error) {
      this.snackService.openErrorSnack(this.translateService.instant('POSTS_CAMPAIGN_VALIDATION.SAVE_CAMPAIGN_ERROR'));
      this.showSpinner = false;
    }
  }

  private handleSchedulePost(campaignId: string, scheduledCards: DynamicPostCardComponent[]): void {
    this.showSpinner = true;
    const socialPosts: SocialPostV2Interface[] = scheduledCards.flatMap((post) => {
      return post.postContent.map((content) => {
        const castedMedias = content?.Medias?.map((m) => ({
          ...m,
          mediaType: m.mediaType as MediaType,
        }));

        const mappedMedias = this.mapMedia(castedMedias);

        return {
          businessId: this.configService.accountGroupId,
          partnerId: this.partnerId,
          postContent: {
            postText: content.postText,
            medias: mappedMedias,
          },
          scheduled: content.scheduleDate,
          socialServiceId: content.socialConnection?.ssid,
          postType: this.getPostTypeFromMedia(mappedMedias),
          internalPostId: this.generateCustomId(),
          campaignId: campaignId,
        };
      });
    });

    const requestBody: SocialPostRequestInterface = {
      socialPosts: socialPosts,
    };

    this.socialPostServiceV2.scheduleSocialPostV2(requestBody).subscribe({
      next: () => {
        //This setTimeout is for delay the post submission event to allow the UI to update
        setTimeout(() => {
          this.postSubmitted.emit();
          this.snackService.openSuccessSnack(
            this.translateService.instant('POSTS_CAMPAIGN_VALIDATION.SAVE_POSTS_SUCCESS'),
          );
          this.showSpinner = false;
        }, 4000);
      },
      error: () => {
        this.snackService.openErrorSnack(this.translateService.instant('POSTS_CAMPAIGN_VALIDATION.SAVE_POSTS_ERROR'));
        this.showSpinner = false;
      },
    });
  }

  addPost() {
    this.productAnalyticsService.trackEvent(POSTHOG_KEYS.SOCIAL_CAMPAIGN_ADD_ANOTHER_POST, 'user', 'click', 0);
    this.isContentGenerating = true;
    this.addAnotherPost.emit();
  }

  validateTitle() {
    this.isTitleValid = this.generatedCampaignTitle.trim().length > 0;
  }
}
