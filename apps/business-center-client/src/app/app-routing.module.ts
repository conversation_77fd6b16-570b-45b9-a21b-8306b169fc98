import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './auth.guard';
import { eventsAndMeetingsSettingsUrls, integrationsUrl } from './meeting-integrations/providers';

const routes: Routes = [
  { path: 'error', loadComponent: () => import('./error-page/error-page.component') },
  {
    path: 'public',
    loadChildren: () => import('./public-app.module').then((m) => m.PublicAppModule),
  },
  {
    // slice(1) -> removing leading /
    path: integrationsUrl.slice(1),
    pathMatch: 'full',
    loadComponent: () =>
      import('./meeting-scheduler/connection-redirect.component').then((m) => m.ConnectionRedirectComponent),
  },
  {
    // slice(1) -> removing leading /
    path: eventsAndMeetingsSettingsUrls.slice(1),
    pathMatch: 'full',
    loadComponent: () =>
      import('./meeting-scheduler/connection-redirect.component').then((m) => m.ConnectionRedirectComponent),
  },
  {
    path: 'landing',
    loadComponent: () => import('./auth/landing/landing.component'),
  },
  {
    path: 'confirmation-code',
    loadComponent: () => import('./auth/confirmation-code/confirmation-code.component'),
  },
  {
    path: 'provider-selection',
    loadComponent: () => import('./auth/provider-selection/provider-selection.component'),
  },
  {
    path: 'oauth/redirect',
    loadComponent: () => import('./auth/redirect.component'),
  },
  {
    path: 'oauth/logout',
    loadComponent: () => import('./auth/logout-redirect.component'),
  },
  {
    path: '',
    loadChildren: () => import('./authenticated-app.module').then((m) => m.AuthenticatedAppModule),
    canActivate: [AuthGuard],
  },
];
@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      onSameUrlNavigation: 'reload',
      scrollPositionRestoration: 'enabled',
      anchorScrolling: 'enabled',
      scrollOffset: [0, 64],
      bindToComponentInputs: true,
      paramsInheritanceStrategy: 'always',
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
