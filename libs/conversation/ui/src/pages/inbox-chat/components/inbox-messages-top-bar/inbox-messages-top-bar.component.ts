import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  Inject,
  inject,
  input,
  Input,
  Optional,
  Output,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';
import { BehaviorSubject, firstValueFrom, map, of, switchMap } from 'rxjs';
import { ConversationService } from '../../../../../../core/src/lib/state/conversation.service';
import { InboxService } from '../../../../../../core/src/lib/inbox.service';
import {
  ConversationUnseen,
  KabobDynamicButton,
} from '../../../../../../core/src/lib/interface/conversation.interface';
import { HostAppInterface } from '../../../../../../core/src/lib/interface/host-app.interface';
import {
  CONVERSATION_HOST_APP_INTERFACE_TOKEN,
  CONVERSATION_ROUTES_TOKEN,
} from '../../../../../../core/src/lib/tokens';
import { ViewModeService } from '../../../../../../core/src/lib/view-mode.service';
import { InboxConversationAssigneeComponent } from '../../../../components/inbox-conversation-assignee/inbox-conversation-assignee.component';
import { InboxConversationSubtitleAccountComponent } from '../../../../components/inbox-conversation-subtitle/inbox-conversation-subtitle.component';
import { InboxConversationTitleComponent } from '../../../../components/inbox-conversation-title/inbox-conversation-title.component';
import { INBOX_NAVIGATION_LINK, INBOX_NAVIGATION_LINK_FULLSCREEN } from '../../../../constants';
import { InboxInfoDrawerService } from '../inbox-info-pane/inbox-info-drawer.service';
import { MatDialog } from '@angular/material/dialog';
import { ConversationApiService, ParticipantType } from '@vendasta/conversation';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { isMobile } from '../../../../../../core/src/lib/inbox-utils';

@Component({
  selector: 'inbox-messages-top-bar',
  templateUrl: './inbox-messages-top-bar.component.html',
  styleUrls: ['./inbox-messages-top-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    // Common
    AsyncPipe,
    NgFor,
    NgIf,
    NgClass,
    // Material
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatTooltipModule,
    // Everything else
    GalaxyBadgeModule,
    InboxConversationAssigneeComponent,
    InboxConversationTitleComponent,
    TranslateModule,
    InboxConversationSubtitleAccountComponent,
  ],
})
export class InboxMessagesTopBarComponent {
  conversationDetail$ = this.conversationService.currentConversationDetail$;
  kabobAvailableActions$ = this.conversationDetail$.pipe(
    switchMap((conversationDetail) => {
      if (!conversationDetail) return of(new Array<KabobDynamicButton>());
      return this.hostAppInterface.getKabobAvailableActions(conversationDetail);
    }),
  );

  isInternalInfoDeleted = false;
  unseen$$ = new BehaviorSubject<ConversationUnseen>({ read: true, conversationId: '' });
  unseen$ = this.unseen$$.asObservable();

  readonly showInboxViews$ = this.inboxService.showInboxViews$;

  readonly showInboxBCSideBar = toSignal(this.inboxService.showInboxBCSideBar$);
  readonly canAccessInboxSidebar = computed(() => this.showInboxBCSideBar() || this.inboxService.isPartnerCenter);

  readonly isBusinessApp = this.inboxService.isBusinessApp;

  // only display assignee saleperson for sales center and partner center for now
  readonly displayAssignee = this.inboxService.isSalesCenter || this.inboxService.isPartnerCenter;

  readonly showAIResponseToggle$ = this.conversationService.aiResponderForConversation$.pipe(
    map((aiResponder) => aiResponder?.isEnabled),
  );

  readonly aiResponseStatus = toSignal(
    this.conversationDetail$.pipe(map((detail) => detail?.conversation.aiConfiguration?.aiResponseEnabled ?? false)),
    { requireSync: true },
  );

  readonly $showCloseConversationButton = toSignal(
    this.conversationDetail$.pipe(
      map(
        (detail) =>
          !detail?.participants?.some((p) => p.participantType === ParticipantType.PARTICIPANT_TYPE_ANONYMOUS),
      ),
    ),
  );

  readonly $conversationFullScreenFeatureFlag = toSignal(this.inboxService.featureFlagFullscreenInboxPC$);

  readonly $showConversationOverlayButton = computed(
    () => !this.isConversationOverlayMode() && this.$conversationFullScreenFeatureFlag() && !isMobile(),
  );

  @Input() set unseen(value: ConversationUnseen) {
    this.unseen$$.next(value);
  }

  @Input() isFollowing = false;
  @Input() isOpen = false;
  isConversationOverlayMode = input<boolean>(false);

  useSidebarStyles$ = this.viewModeService.viewMode$.pipe(map((viewMode) => viewMode === 'sidebar'));

  readonly $showInboxBack = computed(() => {
    if (this.isConversationOverlayMode()) {
      return false;
    }

    return isMobile();
  });

  @Input() accountGroupId = '';
  @Input() groupId = '';

  @Output() markedUnread = new EventEmitter();
  @Output() changeFollowStatus = new EventEmitter<boolean>();
  @Output() changeOpenStatus = new EventEmitter<boolean>();
  @Output() titleInBusinessAppClicked = new EventEmitter<boolean>();
  @Output() changeAIResponseEnabled = new EventEmitter<boolean>();
  @Output() openConversationOverlayClicked = new EventEmitter<boolean>();
  @Output() closeConversationOverlayClicked = new EventEmitter<boolean>();

  private readonly routes = toSignal(inject(CONVERSATION_ROUTES_TOKEN));

  constructor(
    private router: Router,
    private conversationService: ConversationService,
    private inboxService: InboxService,
    @Optional()
    private viewModeService: ViewModeService,
    private inboxInfoDrawerService: InboxInfoDrawerService,
    @Inject(CONVERSATION_HOST_APP_INTERFACE_TOKEN) private readonly hostAppInterface: HostAppInterface,
    private readonly dialog: MatDialog,
    private conversationAPIService: ConversationApiService,
    private snackbarService: SnackbarService,
    private translateService: TranslateService,
  ) {}

  async back(): Promise<void> {
    if (this.routes()?.useModal) {
      this.conversationService.removeLastConversationId();
      this.router.navigate(
        this.$conversationFullScreenFeatureFlag() ? INBOX_NAVIGATION_LINK_FULLSCREEN : INBOX_NAVIGATION_LINK,
        {
          queryParamsHandling: 'merge',
          queryParams: { inboxPrefilledMessage: null },
        },
      );
      return;
    }

    if (this.accountGroupId) {
      this.router.navigateByUrl(`/account/location/${this.accountGroupId}/inbox`);
    } else if (this.groupId) {
      this.router.navigateByUrl(`/account/brands/${this.groupId}/inbox`);
    } else {
      this.router.navigateByUrl(`/inbox`);
    }
  }

  markUnread(): void {
    this.markedUnread.emit({
      read: false,
    });
  }

  markRead(): void {
    this.markedUnread.emit({
      read: true,
    });
  }

  updateFollowStatus(): void {
    this.changeFollowStatus.emit(!this.isFollowing);
  }

  async toggleDetailsDrawer(): Promise<void> {
    if (this.showInboxBCSideBar()) {
      this.titleInBusinessAppClicked.emit(true);
    }
    this.inboxInfoDrawerService.toggle();
  }

  toggleConversationOpen(): void {
    this.changeOpenStatus.emit(!this.isOpen);
  }

  async toggleAIResponseStatus() {
    const conversation = await firstValueFrom(this.conversationDetail$);

    if (!conversation) return;

    const respStatus = !this.aiResponseStatus();
    try {
      await this.conversationService.setConversationAIResponderStatus(
        conversation.conversation?.conversationId,
        !this.aiResponseStatus(),
      );
      this.changeAIResponseEnabled.emit(respStatus);
    } catch (e) {
      console.error('error enabling/disabling ai response in the conversation', e);
      this.snackbarService.openErrorSnack(this.translateService.instant('INBOX.SOMETHING_WENT_WRONG'));
    }
  }

  closeConversationOverlay(): void {
    this.closeConversationOverlayClicked.emit(true);
  }

  async openConversationOverlay(): Promise<void> {
    this.openConversationOverlayClicked.emit(true);
  }

  get isMobile(): boolean {
    return isMobile();
  }
}
