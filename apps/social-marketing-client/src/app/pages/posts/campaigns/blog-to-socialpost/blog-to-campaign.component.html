<mat-drawer-container [hasBackdrop]="false" class="drawer-container">
  <mat-drawer-content class="container">
    <ng-container>
      <mat-card>
        <div class="campaign-tool-bar">
          <div class="campaign-header" mat-dialog-title>
            <button mat-icon-button (click)="handleCloseCampaign()">
              <mat-icon>arrow_back</mat-icon>
            </button>
            <span class="title-header">{{ 'CAMPAIGN.FROM_BLOG.CREATE_CAMPAIGN' | translate }}</span>
          </div>
          <div class="ai-setting-header">
            <button mat-stroked-button (click)="toggleSetting()">
              <span>{{ 'SETTINGS_BUTTON.LABEL' | translate }}</span>
            </button>
          </div>
        </div>
      </mat-card>
    </ng-container>

    @if (iscontentGenerating) {
      <div class="loadingPage">
        <app-ui-loader-generate
          [title]="'BLOG.LOADER.GENERATING_BLOG.TITLE'"
          [animateProgress]="true"
          [triggerSource]="'campaign'"
        ></app-ui-loader-generate>
      </div>
    } @else {
      <div class="campaign-modal-content">
        <div class="campaign-container">
          <app-dynamic-posts-container
            [services]="selectedNetworks"
            [posts]="socialPosts"
            [generatedCampaignTitle]="generatedCampaignTitle"
            (addAnotherPost)="addAnotherPost()"
            (postSubmitted)="onPostSubmitted()"
          ></app-dynamic-posts-container>
        </div>
      </div>
    }
  </mat-drawer-content>
  <mat-drawer role="region" #aiSettings [position]="'end'" [mode]="'side'">
    <h2 mat-dialog-title class="dialog-title settings-title">
      <span>{{ 'SETTINGS_BUTTON.TITLE' | translate }}</span>
      <span class="spacer"></span>
      <button mat-icon-button (click)="aiSettings.close()">
        <mat-icon>close</mat-icon>
      </button>
    </h2>
    <app-ai-setting-button [InstructionFor]="AIInstruction.SOCIAL_INSTRUCTION"></app-ai-setting-button>
  </mat-drawer>
</mat-drawer-container>
