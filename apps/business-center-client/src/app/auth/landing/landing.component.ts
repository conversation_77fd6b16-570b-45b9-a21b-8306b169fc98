import { CommonModule, AsyncPipe } from '@angular/common';
import { Component, inject, OnInit, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { AuthService } from 'ionic-appauth';
import { partnerId, multiPartnerApps } from '../../../globals';
import { MOBILE_PARTNER_ID, MOBILE_USER_EMAIL } from '../../constants';
import { BrandingV2Service } from '@galaxy/partner';
import { shareReplay, firstValueFrom, catchError, map, of } from 'rxjs';
import { UIKitModule } from '@vendasta/uikit';
import { FormsModule } from '@angular/forms';
import { UsersApiService } from '@vendasta/platform-users';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { BusinessAppBuildingComponent } from '../../shared/business-app-building/business-app-building.component';
import { GreyLabelService } from '../grey-label.service';
import { GalaxyButtonLoadingIndicatorModule } from '@vendasta/galaxy/button-loading-indicator';
import { GalaxyPageModule } from '@vendasta/galaxy/page';

@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss'],
  imports: [
    CommonModule,
    RouterModule,
    AsyncPipe,
    GalaxyEmptyStateModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    UIKitModule,
    FormsModule,
    GalaxyFormFieldModule,
    GalaxyButtonLoadingIndicatorModule,
    BusinessAppBuildingComponent,
    GalaxyPageModule,
  ],
})
export default class LandingComponent implements OnInit {
  private readonly auth = inject(AuthService);
  private readonly brandingService = inject(BrandingV2Service);
  private readonly userService = inject(UsersApiService);
  private readonly snackbar = inject(SnackbarService);
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly greyLabelService = inject(GreyLabelService);

  protected readonly partnerBranding$ = this.brandingService.getBranding(partnerId).pipe(
    catchError(() => of(null)),
    shareReplay({ refCount: true, bufferSize: 1 }),
  );

  readonly logoUrl$ = this.partnerBranding$.pipe(map((branding) => branding?.logoUrl || ''));
  readonly businessAppName$ = this.partnerBranding$.pipe(map((branding) => branding?.name || 'Business App'));
  readonly isMultiPartnerApp = multiPartnerApps.includes(partnerId) || !partnerId;
  readonly isGreyLabelApp = this.greyLabelService.isGreyLabel;
  readonly isPartnerIDConfigured = !!partnerId;

  readonly userEmail = localStorage.getItem(MOBILE_USER_EMAIL) || '';
  readonly sendingOTP = signal(false);

  ngOnInit(): void {
    if (this.localStoragePartnerID() && this.userEmail && this.isMultiPartnerApp) {
      this.signIn();
    }

    if (this.activatedRoute.snapshot.queryParamMap.get('autoPromptLogin') === 'true') {
      this.signIn(this.activatedRoute.snapshot.queryParamMap.get('email') || '');
    }
  }

  signIn(emailHint?: string): void {
    this.auth.signIn({
      partner_id: partnerId,
      email_hint: emailHint || '',
      prompt: 'login',
    });
  }

  async submitEmail() {
    if (partnerId) {
      await this.loadNamespaceForEmail();
    } else {
      await this.sendOneTimePassword();
    }
  }

  resetProvider(): void {
    this.greyLabelService.resetProvider();
  }

  private async loadNamespaceForEmail(): Promise<void> {
    if (!this.userEmail) return;

    localStorage.setItem(MOBILE_USER_EMAIL, this.userEmail);

    let userNamespace: string;
    try {
      const { namespaces } = await firstValueFrom(
        this.userService.listNamespaces({ email: this.userEmail.toLowerCase() }),
      );
      userNamespace = multiPartnerApps.find((ns) => namespaces.includes(ns));
    } catch (error) {
      this.snackbar.openErrorSnack('Error signing in, please try again.');
      return;
    }

    userNamespace ? localStorage.setItem(MOBILE_PARTNER_ID, userNamespace) : localStorage.removeItem(MOBILE_PARTNER_ID);

    if (userNamespace === partnerId || !userNamespace) {
      this.signIn();
    } else {
      // Reload the app so the partner id is set correctly
      window.location.reload();
    }
  }

  private async sendOneTimePassword(): Promise<void> {
    this.sendingOTP.set(true);
    try {
      await this.greyLabelService.sendOTP(this.userEmail);
    } catch (error) {
      this.snackbar.openErrorSnack(error.message || 'Error sending one time password, please try again.');
      return;
    } finally {
      this.sendingOTP.set(false);
    }

    this.router.navigate(['../confirmation-code']);
  }

  private localStoragePartnerID(): string {
    return localStorage.getItem(MOBILE_PARTNER_ID);
  }
}
