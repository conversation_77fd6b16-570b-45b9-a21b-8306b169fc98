@let form = assistantForm();
@let toolForm = activeToolForm();

<glxy-page [pagePadding]="!!toolForm">
  @if (!toolForm) {
    <glxy-page-toolbar>
      <glxy-page-nav>
        <glxy-page-nav-button [useHistory]="true"></glxy-page-nav-button>
      </glxy-page-nav>
      <glxy-page-title>
        @if (!aiAssistant()?.assistant?.id) {
          {{ 'AI_ASSISTANT.SETTINGS.CREATE_CUSTOM_ASSISTANT_TITLE' | translate }}
        } @else {
          {{ 'AI_ASSISTANT.SETTINGS.TITLE' | translate: { assistantName: aiAssistant()?.assistant?.name } }}
        }
        @if (showBetaBadge()) {
          <glxy-badge [color]="'green'" class="beta-badge">{{ 'AI_ASSISTANT.SHARED.BETA' | translate }}</glxy-badge>
        }
      </glxy-page-title>
      @if (isCustomAssistant()) {
        <glxy-page-actions>
          <button mat-stroked-button (click)="openDeleteAssistantConfirmationModal()">
            {{ 'AI_ASSISTANT.SHARED.DELETE' | translate }}
          </button>
        </glxy-page-actions>
      }
    </glxy-page-toolbar>
  }
  @if (loading()) {
    <glxy-loading-spinner></glxy-loading-spinner>
  } @else if (form) {
    @if (toolForm) {
      <glxy-page-wrapper>
        <ai-tool-form [form]="toolForm.form" class="tool-form" />
        <ai-sticky-footer
          (submitClick)="handleToolFormSubmit()"
          (cancelClick)="activeToolForm.set(undefined)"
          [submitOverride]="'AI_ASSISTANT.FUNCTIONS.EDIT_FUNCTION.DONE' | translate"
          [hideCancel]="toolForm.form.disabled"
        />
      </glxy-page-wrapper>
    }

    <form class="config-page-wrapper" [formGroup]="form" [ngClass]="{ hidden: toolForm }">
      <div class="two-column-layout">
        <div class="left-column">
          <ai-image-upload
            (imageUrlChanged)="onImageChanged($event)"
            [imageUrl]="form.controls.avatarUrl.value"
            [defaultSvg]="aiAssistant()?.decoration?.defaultAvatarIcon ?? defaultAvatarIcon"
            class="avatar"
          />

          <!-- Name -->
          <glxy-form-field>
            <glxy-label>
              {{ 'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME' | translate }}
              <mat-icon class="info-icon" [glxyTooltip]="'AI_ASSISTANT.SETTINGS.ASSISTANT_NAME_TOOLTIP' | translate">
                info_outline
              </mat-icon>
            </glxy-label>
            <input
              matInput
              id="assistant-name"
              formControlName="name"
              placeholder="{{ 'AI_ASSISTANT.SETTINGS.DEFAULT_ASSISTANT_NAME' | translate }}"
            />
          </glxy-form-field>

          @if (isSalesCoach()) {
            <button mat-stroked-button class="view-recorded-btn" (click)="viewRecordedMeetings()">
              {{ 'AI_ASSISTANT.SETTINGS.VIEW_RECORDED_MEETINGS' | translate }}
            </button>
          }

          <!-- Voice Usage Information -->
          @if (assistantSupportsVoice()) {
            <glxy-alert type="info">
              <div>
                <strong>Important</strong>
                &mdash; {{ 'AI_ASSISTANT.SETTINGS.VOICE_BETA_BANNER_MESSAGE' | translate }}
              </div>
              <ai-assistant-voice-usage
                class="voice-usage"
                [namespace]="aiAssistant()?.assistant?.namespace"
              ></ai-assistant-voice-usage>
            </glxy-alert>
          }
        </div>
        <div class="right-column">
          <div class="form-wrapper">
            <div class="form-cards">
              <!-- Profile -->
              <ai-profile-accordion
                [assistantForm]="form"
                [assistantSupportsVoice]="assistantSupportsVoice()"
                [availableModels]="availableModels()"
                [isLangChainEnabled]="isLangChainEnabled()"
                (rollback)="handlePersonalityRollback($event)"
              />

              <!-- Connections -->
              @let connectionsForm = form?.controls?.connections;
              @if (connectionsForm && aiConnections()) {
                <ai-connections-form [connectionForms]="connectionsForm"></ai-connections-form>
              }

              <!-- Capabilities -->
              @let capabilitiesForm = form?.controls?.capabilities;
              @if (capabilitiesForm) {
                <ai-capabilities-accordion
                  [capabilities]="capabilitiesForm"
                  (toolFormDisplay)="handleToolFormDisplay($event)"
                />
              }

              <!-- Knowledge -->
              <mat-card>
                <mat-card-header>
                  <mat-card-title>{{ 'AI_ASSISTANT.SETTINGS.KNOWLEDGE_SOURCES' | translate }}</mat-card-title>
                  <mat-card-subtitle
                    >{{ 'AI_ASSISTANT.SETTINGS.KNOWLEDGE_SOURCES_DESCRIPTION' | translate }}
                  </mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <ai-knowledge-application-knowledge
                    [appId]="appId()"
                    (selectedSourcesChange)="updateSelectedSources($event)"
                  ></ai-knowledge-application-knowledge>
                </mat-card-content>
              </mat-card>
            </div>

            <ai-sticky-footer
              [attr.class]="toolForm ? 'hidden' : ''"
              (submitClick)="submit()"
              (cancelClick)="back()"
              [disabled]="submitDisabled()"
            />
          </div>
        </div>
      </div>
    </form>
  }
</glxy-page>
