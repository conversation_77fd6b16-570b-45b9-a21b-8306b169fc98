@use 'design-tokens' as *;

button {
  span.loading {
    visibility: hidden;
  }
  .spinner-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
  }
}

.dialog-content {
  white-space: pre-line;
}

.dialog-title {
  display: flex;
  align-items: center;
  margin-top: $spacing-3;
  margin-bottom: $negative-2;
}

.dialog-title mat-icon {
  margin-right: $spacing-2;
  font-size: 27px;
}

.dialog-content {
  margin-left: $spacing-3;
}

.dialog-title-content {
  margin-left: $spacing-2;
}
