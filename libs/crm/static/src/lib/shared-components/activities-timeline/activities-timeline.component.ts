import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  AfterViewChecked,
  Component,
  computed,
  DestroyRef,
  effect,
  ElementRef,
  inject,
  Inject,
  input,
  OnDestroy,
  OnInit,
  signal,
  untracked,
  viewChild,
  ViewChild,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  CrmDependencies,
  CrmInjectionToken,
  ObjectType,
  PrimaryAccountGroupIDForCrmObject,
  tableFiltersInjectionTokenGenerator,
} from '../../tokens-and-interfaces';
import { SEARCH_DEBOUNCE_MS } from '../../constants';
import { TranslationModule } from '../../i18n/translation-module';
import {
  combineLatest,
  debounceTime,
  filter,
  map,
  of,
  ReplaySubject,
  shareReplay,
  skipWhile,
  Subscription,
  switchMap,
  tap,
} from 'rxjs';
import { CrmActivityLoggerComponent } from './activities/activity-logger.component';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CrmTimelineComponent } from './timeline/timeline.component';
import { MatButtonModule } from '@angular/material/button';
import { GalaxyFilterChipsComponent } from '@vendasta/galaxy/filter/chips/src/galaxy-filter-chips.component';
import {
  GalaxyFilterChipDependencies,
  GalaxyFilterChipInjectionToken,
  GalaxyFilterInterface,
  GalaxyFilterOperator,
} from '@vendasta/galaxy/filter/chips';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { MatIconModule } from '@angular/material/icon';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { ActivityInterface } from '@vendasta/crm';
import { RxState } from '@rx-angular/state';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { decodeFilters, encodeFilters, validateFilters } from '@vendasta/galaxy/filter/chips/src/utils';
import { ActivitiesTimelineService } from './timeline/timeline.service';
import { FilterViewComponent } from '../filter-view/filter-view.component';
import { SavedFilter, SavedFilters } from '../filter-view/interfaces';
import { CrmFieldService, CrmObjectService, PlatformExtensionFieldIds, StandardIds } from '../../shared-services';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { CrmAISummaryComponent } from '../ai-summary';
import { v4 as uuidv4 } from 'uuid';
import { TabGroup, ViewTabsComponent } from '@galaxy/crm/components/view-tabs';
import { startWith } from 'rxjs/operators';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { FeatureFlagsApiService } from '@vendasta/partner';
import { MatSlideToggle } from '@angular/material/slide-toggle';

@Component({
  selector: 'crm-activities-timeline',
  templateUrl: './activities-timeline.component.html',
  styleUrls: ['./activities-timeline.component.scss'],
  providers: [
    RxState,
    ActivitiesTimelineService,
    {
      provide: GalaxyFilterChipInjectionToken,
      useFactory: tableFiltersInjectionTokenGenerator('Activity'),
    },
    {
      provide: PrimaryAccountGroupIDForCrmObject,
      useFactory: (
        comp: CrmActivitiesTimelineComponent,
        objectService: CrmObjectService,
        fieldService: CrmFieldService,
      ) => {
        const crmObjectId$ = toObservable(comp.crmObjectId);
        const crmObjectType$ = toObservable(comp.activityObjectType).pipe(filter(Boolean));
        return combineLatest([crmObjectId$, crmObjectType$]).pipe(
          switchMap((v: [string, ObjectType]) => {
            const crmObjectId = v[0];
            const crmObjectType = v[1];
            return objectService.getMultiObject(crmObjectType, [crmObjectId]);
          }),
          map((response) => response?.crmObjects?.[0]),
          map((crmObject) => {
            return (
              fieldService.getFieldValueFromCrmObject(crmObject!, PlatformExtensionFieldIds.AccountGroupID)
                ?.stringValue ?? null
            );
          }),
          filter(Boolean),
          shareReplay(1),
        );
      },
      deps: [CrmActivitiesTimelineComponent, CrmObjectService, CrmFieldService],
    },
  ],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
          opacity: 1,
        }),
      ),
      state(
        'closed',
        style({
          height: '0px',
          opacity: 0,
        }),
      ),
      transition('open <=> closed', [animate('0.2s')]),
    ]),
  ],
  imports: [
    CommonModule,
    TranslateModule,
    TranslationModule,
    CrmActivityLoggerComponent,
    CrmTimelineComponent,
    GalaxyFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    GalaxyFilterChipsComponent,
    MatIconModule,
    GalaxyInfiniteScrollTriggerModule,
    FilterViewComponent,
    GalaxyAlertModule,
    CrmAISummaryComponent,
    ViewTabsComponent,
    MatSlideToggle,
    FormsModule,
  ],
})
export class CrmActivitiesTimelineComponent implements OnDestroy, OnInit, AfterContentInit, AfterViewChecked {
  timeline = viewChild(CrmTimelineComponent);
  @ViewChild('scrollableContainerRef', { static: true }) scrollableContainer!: ElementRef;
  @ViewChild('filterChipRef') filterChipRef?: GalaxyFilterChipsComponent;

  crmObjectId = input<string>('');
  showFiltersOpen = false;

  activityObjectType = input<ObjectType | null>(null);
  objectSubtype = input<string>('');
  enableAISummary = input<boolean>(false);

  private readonly excludeCampaignFilters: GalaxyFilterInterface[] = [
    {
      fieldId: StandardIds.ActivityCampaignID,
      filterId: 'exclude-campaign-preset-campaign-id',
      operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_EMPTY,
    },
    {
      fieldId: StandardIds.ActivityCommunicationType,
      filterId: 'exclude-campaign-preset-comm-type',
      operator: GalaxyFilterOperator.FILTER_OPERATOR_IS_NOT,
      values: [{ string: 'Campaign SMS' }],
    },
  ];

  hasTimelineActivitiesFeatureFlag = toSignal(this.config?.hasTimelineActivitiesFeatureFlag$ ?? of(false));
  presetFilters = computed(() => {
    const objectType = this.activityObjectType();
    let objectPresets: SavedFilters | null;
    if (objectType) {
      objectPresets = this.config[objectType?.toLowerCase()]?.profilePageDefaultViews ?? null;
    } else {
      objectPresets = this.config?.activityFeedDefaultViews ?? null;
    }
    const presets: SavedFilters = [
      { name: this.translateService.instant('VIEWS.PRESET_FILTERS.ALL'), filters: [], id: 'activities-all' },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.NOTES'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'notes-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Note' }],
          },
        ],
        id: 'activities-notes',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.EMAILS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'emails-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Email' }],
          },
        ],
        id: 'activities-email',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.CALLS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'calls-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Call' }],
          },
        ],
        id: 'activities-calls',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.MEETINGS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'meetings-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Meeting' }],
          },
        ],
        id: 'activities-meetings',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.TASKS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'tasks-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Task' }],
          },
        ],
        id: 'activities-tasks',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.COMMUNICATIONS'),
        filters: [
          {
            fieldId: 'system__activity_activity_type',
            filterId: 'communications-preset-activity-type',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Communication' }],
          },
        ],
        id: 'activities-communications',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.RECORDED_MEETINGS'),
        filters: [
          {
            fieldId: StandardIds.ActivityMeetingStatus,
            filterId: 'activities-recorded-meetings-status-completed',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Completed' }],
          },
          {
            fieldId: StandardIds.ActivitySourceName,
            filterId: 'activities-recorded-meetings-type-meeting-analysis',
            operator: GalaxyFilterOperator.FILTER_OPERATOR_IS,
            values: [{ string: 'Meeting Analysis' }],
          },
        ],
        id: 'activities-recorded-meetings',
      },
      {
        name: this.translateService.instant('VIEWS.PRESET_FILTERS.EXCLUDE_CAMPAIGNS'),
        filters: this.excludeCampaignFilters,
        toggle: this.hasTimelineActivitiesFeatureFlag(),
      },
    ];
    if (objectPresets) {
      presets.push(...objectPresets);
    }
    if (!this.crmObjectId()) {
      return presets.filter((filter) => filter.name !== this.translateService.instant('VIEWS.PRESET_FILTERS.TASKS'));
    }
    return presets;
  });

  objectType: ObjectType = 'Activity';

  private readonly translateService = inject(TranslateService);
  private readonly destroyRef = inject(DestroyRef);

  protected readonly filters = signal<GalaxyFilterInterface[]>([]);
  protected readonly filtersApplied = computed(() => this.filters().length > 0);

  searchControl = new FormControl<string>('');
  subscriptions: Subscription[] = [];
  showLoadMore = false;
  private scrollTop = 0;

  private initialSearchTerm = '';
  private initialSearchTermSet = false;
  private initialFilters: GalaxyFilterInterface[] = [];
  keys: string[];

  constructor(
    @Inject(CrmInjectionToken) private readonly config: CrmDependencies,
    private readonly activitiesTimelineService: ActivitiesTimelineService,
    @Inject(GalaxyFilterChipInjectionToken)
    private readonly activitiesTimelineFilterService: GalaxyFilterChipDependencies,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly snackService: SnackbarService,
    @Inject(FeatureFlagsApiService) private readonly featureFlagService: FeatureFlagsApiService,
  ) {
    effect(() => {
      const viewTabKey = this.viewTabKey();
      this.userSavedTabs$$.next(this.loadSavedFilters(viewTabKey, this.keys[0]));
    });
    effect(() => {
      const defaultsKey = this.hideDefaultsViewKey();
      this.hiddenDefaultTabs$$.next(this.loadSavedStringList(defaultsKey));
    });
    effect(() => {
      const viewOrderKey = this.viewTabOrderKey();
      this.tabOrder$$.next(this.loadSavedStringList(viewOrderKey));
    });
    effect(() => {
      this.excludeCampaignsOverride(); //needed to trigger effect
      const timeline = untracked(this.timeline);
      if (!timeline) {
        return;
      }
      const filters = untracked(this.filters);
      this.changeViewFilters(filters);
    });

    const filterParam = this.activatedRoute.snapshot.queryParamMap.get('filter');
    let filters: GalaxyFilterInterface[] = [];
    if (filterParam) {
      const f = decodeFilters(filterParam);
      if (validateFilters(f) && f.length > 0) {
        filters = f;
        const campaignOverride = f.find(
          (filter) =>
            filter.filterId === 'exclude-campaign-preset-campaign-id' ||
            filter.filterId === 'exclude-campaign-preset-comm-type',
        );
        if (campaignOverride) {
          this.excludeCampaignsOverride.set(true);
        }
      }
    } else {
      filters = this.excludeCampaignFilters;
      this.excludeCampaignsOverride.set(true);
    }
    this.initialFilters = filters;
    this.activitiesTimelineFilterService.setInitialAppliedFilters?.(filters);

    const keys: string[] = [];
    if (this.config?.appID) {
      keys.push('crm:' + this.config.appID + ':activities-timeline:views');
    }
    keys.push('crm:activities-timeline:views');
    this.keys = keys;
  }

  ngOnInit() {
    const searchParam = this.activatedRoute.snapshot.queryParamMap.get('search');
    if (searchParam) {
      this.initialSearchTerm = decodeURI(searchParam);
    }
    this.activitiesTimelineService.initializeState(this.initialSearchTerm, this.initialFilters);

    this.subscriptions.push(
      this.searchControl.valueChanges
        .pipe(
          skipWhile((term) => term !== this.initialSearchTerm),
          debounceTime(SEARCH_DEBOUNCE_MS),
          map((value) => value?.trim() ?? ''),
          tap((searchTerm) => {
            let searchParam: string | null = null;
            if (searchTerm) {
              searchParam = encodeURI(searchTerm);
            }
            this.router.navigate([], {
              relativeTo: this.activatedRoute,
              queryParams: { search: searchParam },
              queryParamsHandling: 'merge',
            });
            const timeline = this.timeline();
            if (timeline) {
              timeline.setSearchTerm(searchTerm);
            }
          }),
        )
        .subscribe(),
    );
  }

  ngAfterViewChecked(): void {
    if (!this.initialSearchTermSet) {
      this.searchControl.setValue(this.initialSearchTerm);
      this.searchControl.updateValueAndValidity();
      this.initialSearchTermSet = true;
    }
  }

  ngAfterContentInit(): void {
    const timeline = this.timeline();
    if (timeline) {
      timeline.filters$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((filters) => this.filters.set(filters));
    }
  }

  addActivity(activity: ActivityInterface): void {
    const timeline = this.timeline();
    if (timeline) {
      timeline.addActivity(activity);
    }
  }

  handleActivitiesChanged(activities: ActivityInterface[]): void {
    this.scrollableContainer.nativeElement.scrollTop = this.scrollTop;
    this.showLoadMore = activities?.length > 0;
  }

  loadMoreActivities(): void {
    this.scrollTop = this.scrollableContainer?.nativeElement?.scrollTop ?? 0;
    const timeline = this.timeline();
    if (timeline) {
      timeline.loadMoreActivities();
    }
  }

  ngOnDestroy() {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  toggleFilterBar(): void {
    this.showFiltersOpen = !this.showFiltersOpen;
  }

  updateFilters($event: GalaxyFilterInterface[], updateChipList?: boolean): void {
    const timeline = this.timeline();
    if (!timeline) {
      return;
    }
    let filterParam: string | null = null;
    if ($event.length > 0) {
      const readyFilters = $event.filter(
        (f) => !(f.operator === GalaxyFilterOperator.FILTER_OPERATOR_IS && f.values?.length === 0),
      );
      if (readyFilters.length > 0) {
        filterParam = encodeFilters(readyFilters);
      }
    }
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { filter: filterParam },
      queryParamsHandling: 'merge',
    });
    timeline.setFilters($event);
    if (updateChipList) {
      this.filterChipRef?.filters.set($event);
    }
    this.filters.set($event);
  }

  //View tabs
  protected readonly hasViewTabsFeatureFlag$ = combineLatest([
    this.config.namespace$,
    this.config.parentNamespace$,
  ]).pipe(
    switchMap(([namespace, parentNamespace]) => {
      return this.featureFlagService.batchGetStatus({
        featureIds: ['crm_table_view_tabs'],
        partnerId: parentNamespace || namespace,
      });
    }),
    map((response) => {
      return response?.hasAccess.length > 0 && response?.hasAccess[0];
    }),
  );

  protected readonly viewTabKey = computed(() => {
    const subType = this.objectSubtype();
    const objectType = this.activityObjectType() ?? '';
    const objectKey = subType ? subType : objectType;
    return this.config?.appID
      ? 'crm:' + this.config.appID + ':activities-timeline:views:' + objectKey
      : 'crm:' + ':activities-timeline:views:' + objectKey;
  });

  // preset views are not saved locally so we need to handle hiding them differently
  private readonly hideDefaultsViewKey = computed(() => {
    return this.viewTabKey() + ':hide-defaults';
  });

  private readonly viewTabOrderKey = computed(() => {
    return this.viewTabKey() + ':order';
  });

  protected readonly newViewTemplate = computed(() => {
    return {
      name: '',
      filters: this.filters(), //based on current filters
      preset: false,
    } as SavedFilter;
  });

  protected excludeCampaignsOverride = signal(false);
  private readonly presetTabs$ = toObservable(this.presetFilters);
  private readonly userSavedTabs$$ = new ReplaySubject<SavedFilter[]>(1);
  private readonly hiddenDefaultTabs$$ = new ReplaySubject<string[]>(1);
  protected readonly tabOrder$$ = new ReplaySubject<string[]>(1);

  protected readonly viewTabs$ = combineLatest([this.presetTabs$, this.userSavedTabs$$, this.hiddenDefaultTabs$$]).pipe(
    map(([presets, userSaved, hiddenDefaults]) => {
      const presetFilters =
        presets
          ?.filter((f) => !f.toggle)
          .map((f) => ({ ...f, preset: true, hidden: hiddenDefaults?.includes(f.id) }) as SavedFilter) ?? [];

      userSaved.forEach((userSaved) => {
        if (!userSaved.id) {
          //handles custom views saved without an id (ie through the old view dropdown)
          userSaved.id = uuidv4();
        }
      });

      const customFilterGroup = {
        name: this.translateService.instant('VIEW_TABS.CUSTOM_VIEWS_TAB_GROUP'),
        id: 'custom',
        tabs: userSaved,
        canModify: true,
      } as TabGroup<SavedFilter>;

      return presetFilters.length > 0
        ? [
            {
              name: this.translateService.instant('VIEW_TABS.DEFAULT_VIEWS_TAB_GROUP'),
              id: 'default',
              tabs: presetFilters,
              canAddMore: false,
            },
            customFilterGroup,
          ]
        : ([customFilterGroup] as TabGroup<SavedFilter>[]);
    }),
    startWith([] as TabGroup<SavedFilter>[]),
  );

  addExcludeCampaignOverride(currentFilters: GalaxyFilterInterface[]): GalaxyFilterInterface[] {
    this.excludeCampaignFilters.forEach((filter) => {
      const alreadyHas = currentFilters.findIndex((currentFilter) => currentFilter.filterId === filter.filterId);
      if (alreadyHas === -1) {
        currentFilters.push(filter);
      }
    });
    return currentFilters;
  }

  removeExcludeCampaignOverride(currentFilters: GalaxyFilterInterface[]): GalaxyFilterInterface[] {
    return currentFilters.filter((filter) => {
      return !(
        filter.filterId === 'exclude-campaign-preset-campaign-id' ||
        filter.filterId === 'exclude-campaign-preset-comm-type'
      );
    });
  }

  protected async changeViewFilters(newFilters: GalaxyFilterInterface[]) {
    let filterCopy: GalaxyFilterInterface[] = JSON.parse(JSON.stringify(newFilters));
    const campaignsOverride = this.excludeCampaignsOverride();
    if (campaignsOverride) {
      filterCopy = this.addExcludeCampaignOverride(filterCopy);
    } else {
      filterCopy = this.removeExcludeCampaignOverride(filterCopy);
    }
    this.updateFilters(filterCopy, true);
  }

  private sortFilters(filters: SavedFilters): SavedFilters {
    return filters.sort((a, b) => a.name.toLocaleLowerCase().localeCompare(b.name.toLocaleLowerCase()));
  }

  private loadSavedFilters(key: string, fallbackKey?: string): SavedFilter[] {
    const savedFilters = key ? localStorage.getItem(key) : undefined;
    if (savedFilters) {
      try {
        const filters = JSON.parse(savedFilters);
        return this.sortFilters(filters);
      } catch {
        localStorage.removeItem(key);
      }
    } else if (fallbackKey) {
      const fallbackFilters = localStorage.getItem(fallbackKey) ?? '';
      try {
        const filters = JSON.parse(fallbackFilters);
        return this.sortFilters(filters);
      } catch {
        localStorage.removeItem(key);
      }
    }
    return [];
  }

  private loadSavedStringList(key: string): string[] {
    const hiddenDefaults = localStorage.getItem(key);
    if (hiddenDefaults) {
      try {
        return JSON.parse(hiddenDefaults);
      } catch {
        localStorage.removeItem(key);
      }
    }
    return [];
  }

  protected saveTabChanges($event: TabGroup<SavedFilter>[]) {
    $event.forEach((group) => {
      if (group.id === 'default') {
        const hiddenTabs = group.tabs
          .filter((tab) => tab.hidden && tab.id)
          .map((tab) => {
            return tab.id;
          });
        try {
          localStorage.setItem(this.hideDefaultsViewKey(), JSON.stringify(hiddenTabs));
          this.hiddenDefaultTabs$$.next(hiddenTabs);
          //force reload
        } catch {
          this.snackService.openErrorSnack('VIEW_TABS.ERRORS.FAILED_TO_SAVE_DEFAULT_VIEWS');
        }
      }
      if (group.id === 'custom') {
        try {
          const tabsAsString = JSON.stringify(group.tabs);
          localStorage.setItem(this.viewTabKey(), tabsAsString);
          this.userSavedTabs$$.next(group.tabs);
        } catch {
          this.snackService.openErrorSnack('VIEW_TABS.ERRORS.FAILED_TO_SAVE_CUSTOM_VIEWS');
        }
        return;
      }
    });
  }

  protected saveTabOrder($event: string[]) {
    try {
      localStorage.setItem(this.viewTabOrderKey(), JSON.stringify($event));
      this.tabOrder$$.next($event);
    } catch {
      this.snackService.openErrorSnack('VIEW_TABS.ERRORS.FAILED_TO_SAVE_VIEW_ORDER');
    }
    return;
  }

  protected toggleExcludeCampaignsOverride() {
    this.excludeCampaignsOverride.update((prev) => !prev);
  }
}
