<integrations-connect-to-service [service]="microsoft" [connectionInfoKey]="serviceKey" [useAlternateStyling]="true">
  <integrations-connect-element>
    <ng-container [ngTemplateOutlet]="microsoftButton" [ngTemplateOutletContext]="{ label: signInText }"></ng-container>
  </integrations-connect-element>
  <!-- Copied from default disconnect button in @vendasta/integrations npm package -->
  <integrations-disconnect-element *ngIf="primaryDisconnect">
    <ng-container
      [ngTemplateOutlet]="microsoftButton"
      [ngTemplateOutletContext]="{ label: disconnectText }"
    ></ng-container>
  </integrations-disconnect-element>
</integrations-connect-to-service>

<ng-container
  *ngIf="!primaryDisconnect && isConnected$ | async"
  [ngTemplateOutlet]="microsoftButtonSecondaryDisconnect"
></ng-container>

<ng-template #microsoftButtonSecondaryDisconnect>
  <integrations-connect-to-service [service]="microsoft" [connectionInfoKey]="serviceKey" [useAlternateStyling]="true">
    <integrations-disconnect-element>
      <ng-container [ngTemplateOutlet]="microsoftDisconnectButton"></ng-container>
    </integrations-disconnect-element>
  </integrations-connect-to-service>
</ng-template>

<ng-template #microsoftButton let-label="label" let-cursor="cursor">
  <button
    class="microsoft-button-container"
    matStepperNext
    [disabled]="!primaryDisconnect && (isConnected$ | async)"
    [ngStyle]="{ cursor: cursor || 'pointer' }"
  >
    <div class="microsoft-icon-container">
      <img
        class="microsoft-icon"
        src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/microsoft-logo.svg"
      />
    </div>
    <div class="sign-in-text">
      <span>{{ label }}</span>
    </div>
  </button>
</ng-template>
<ng-template #microsoftDisconnectButton>
  <button mat-stroked-button class="secondary-disconnect" matStepperNext (click)="openDisconnectDialog($event)">
    <div class="microsoft-icon-container">
      <img
        class="microsoft-icon"
        src="https://storage.googleapis.com/meeting-scheduler-prod-public-images/microsoft-logo.svg"
      />
    </div>
    <div class="disconnect-text">
      <span>{{ disconnectSecondaryText }}</span>
    </div>
  </button>
</ng-template>
