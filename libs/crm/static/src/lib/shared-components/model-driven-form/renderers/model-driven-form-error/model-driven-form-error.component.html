@let showError = showError$$ | async;
@if (showError) {
  @for (errorCode of errorCodes; track errorCode) {
    @if (control.hasError(errorCode) && validation.errorCodeMap[errorCode].severity === 'INVALID') {
      <glxy-error>
        {{
          validation.errorCodeMap[errorCode].message
            | translateByObjectType: objectType : { label: label.toLowerCase() }
        }}
      </glxy-error>
      <crm-uniqueness-suggestion-card
        [displayCard]="control.hasError(errorCode) && errorCode === 'uniqueness_enforced'"
        [objectType]="objectType"
        [fieldId]="fieldId"
        [label]="label"
        (displayError)="updateError($event)"
      ></crm-uniqueness-suggestion-card>
    }
  }
}
@let warningCode = warningCode$ | async;
@if (warningCode) {
  <glxy-hint>
    @if (control.status === 'VALID') {
      <span class="warning-messages">
        {{
          validation.errorCodeMap[warningCode].message
            | translateByObjectType: objectType : { label: label.toLowerCase() }
        }}
      </span>
      <crm-uniqueness-suggestion-card
        [displayCard]="true"
        [objectType]="objectType"
        [fieldId]="fieldId"
        [label]="label"
        (displayError)="updateError($event)"
      ></crm-uniqueness-suggestion-card>
    }
  </glxy-hint>
}
