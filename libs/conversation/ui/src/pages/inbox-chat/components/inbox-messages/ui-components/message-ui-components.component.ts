import { Component, inject, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LexiconModule } from '@galaxy/lexicon';
import {
  ConversationApiService,
  Participant,
  UIButtonInterface,
  UIComponentInterface,
  UIComponentType,
} from '@vendasta/conversation';
import {
  ESCALATE_TO_SUPPORT_ACTION_KEY,
  TOOL_CALL_ACTION_KEY,
} from '../../../../../../../core/src/lib/conversation.constants';
import {
  ConversationMessage,
  EscalateToSupportPayload,
} from '../../../../../../../core/src/lib/interface/conversation.interface';
import { ConversationPipesModule } from '../../../../../../../pipes/src/pipes.module';
import { MatButtonModule } from '@angular/material/button';
import { ParticipantService } from '../../../../../../../core/src/lib/participant.service';
import { SnackbarService } from '@vendasta/galaxy/snackbar-service';
import { firstValueFrom } from 'rxjs';
import { Environment, EnvironmentService } from '@galaxy/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { CONVERSATION_CONFIG_TOKEN } from '../../../../../../../core/src/lib/tokens';

@Component({
  selector: 'inbox-message-ui-components',
  imports: [CommonModule, LexiconModule, ConversationPipesModule, MatButtonModule],
  templateUrl: './message-ui-components.component.html',
  styleUrl: './message-ui-components.component.scss',
})
export class MessageUIComponentsComponent {
  inputMessage = input.required<ConversationMessage>();
  toolCallStarted = output<void>();
  private readonly participantService = inject(ParticipantService);
  private readonly snackbarService = inject(SnackbarService);
  private readonly environmentService = inject(EnvironmentService);
  private readonly conversationApiService = inject(ConversationApiService);
  private readonly translationService = inject(TranslateService);
  private readonly router = inject(Router);
  private readonly conversationConfig = inject(CONVERSATION_CONFIG_TOKEN);

  private readonly currentParticipant = this.participantService.currentParticipant$;

  get messageButtons(): UIComponentInterface[] {
    return (
      this.inputMessage().UIComponents?.filter(
        (component) => component.type === UIComponentType.UI_COMPONENT_TYPE_BUTTON,
      ) ?? []
    );
  }

  async handleButtonClick(button: UIButtonInterface): Promise<void> {
    const currentParticipant = await firstValueFrom(this.currentParticipant);

    if (button.action === ESCALATE_TO_SUPPORT_ACTION_KEY) {
      await this.escalateToSupport(this.inputMessage(), currentParticipant);
    }
    if (button.action === TOOL_CALL_ACTION_KEY) {
      this.toolCallStarted.emit();
      try {
        await firstValueFrom(
          this.conversationApiService.makeToolCall({
            messageOption: {
              messageId: this.inputMessage().messageId,
              selectedOptionIds: [button.id || ''],
            },
          }),
        );
      } catch (error) {
        console.error('Error making tool call:', error);
        this.snackbarService.openErrorSnack(this.translationService.instant('INBOX.SOMETHING_WENT_WRONG'));
      }
    }
  }

  async escalateToSupport(message: ConversationMessage, currentParticipant: Participant): Promise<void> {
    const windowZopim = (window as any).$zopim;

    if (!windowZopim || !windowZopim.livechat) {
      console.warn('Zopim chat widget is not available');
      this.snackbarService.openErrorSnack('INBOX.NEW_MESSAGE.LIVE_CHAT_NOT_AVAILABLE');
      return;
    }

    const escalateButton = message.UIComponents?.find((c) => c.button?.action === ESCALATE_TO_SUPPORT_ACTION_KEY);
    if (!escalateButton || !escalateButton.button?.payload) {
      return;
    }

    let conversationContext = '';
    try {
      const parsedPayload = JSON.parse(escalateButton.button.payload) as EscalateToSupportPayload;
      conversationContext = parsedPayload.conversationSummary || '';
    } catch (e) {
      console.warn('Could not parse payload JSON:', escalateButton.button.payload, e);
    }

    if (!windowZopim.livechat.isChatting()) {
      windowZopim.livechat.set({
        name: currentParticipant.name || '',
        email: currentParticipant.email || '',
      });

      if (conversationContext) {
        switch (this.environmentService.getEnvironment()) {
          case Environment.DEMO:
          case Environment.LOCAL:
            windowZopim.livechat.say(
              'Ignore this message. This is a test from Vendasta development team - ' + conversationContext,
            );
            break;
          case Environment.PROD:
            windowZopim.livechat.say(conversationContext);
            break;
          default:
            windowZopim.livechat.say(conversationContext);
            break;
        }
      }
    }

    await this.router.navigate([{ outlets: { inbox: null } }]);

    windowZopim.livechat.window.show();
    this.conversationConfig.openZendeskChat?.();
  }
}
