import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ChangeDetectorRef, OnD<PERSON>roy } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { SentenceCasePipe } from '@vendasta/galaxy/pipes';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { FieldValidation } from '../../field-validation';
import { FieldWarningService } from './field-warning.service';
import { CrmUniquenessSuggestionCardComponent } from './uniqueness-suggestion-card.component';
import { ObjectType } from '../../../../tokens-and-interfaces';
import { TranslateByObjectTypePipe } from '../../../../i18n/translate-by-object-type/translate-by-object-type.pipe';

@Component({
  selector: 'crm-model-driven-form-error',
  templateUrl: './model-driven-form-error.component.html',
  styleUrls: ['./model-driven-form-error.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    GalaxyFormFieldModule,
    SentenceCasePipe,
    TranslateByObjectTypePipe,
    CrmUniquenessSuggestionCardComponent,
  ],
})
export class CrmModelDrivenFormErrorComponent implements OnInit, OnDestroy {
  @Input({ required: true }) objectType!: ObjectType;
  @Input({ required: true }) fieldId!: string;
  @Input({ required: true }) control!: UntypedFormControl;
  @Input({ required: true }) validation!: FieldValidation;
  @Input() label!: string;

  errorCodes: string[] = [];
  warningCode$!: Observable<string>;

  showError$$ = new BehaviorSubject<boolean>(false);
  private statusSubscription?: Subscription;

  constructor(
    private warningService: FieldWarningService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.showError$$.next(true);
    this.errorCodes = Object.keys(this.validation.errorCodeMap);
    this.warningCode$ = this.control.valueChanges.pipe(
      switchMap(() => {
        return this.warningService.validateWarning(
          this.control,
          this.validation.getAsyncWarningValidators(),
          this.validation.getWarningValidators(),
        );
      }),
      map((warnings) => {
        if (warnings !== null && Object.keys(warnings).length > 0) {
          return Object.keys(warnings)[0];
        }
        return '';
      }),
    );

    // Subscribe to validation status changes to trigger change detection
    // This ensures errors appear immediately when async validation completes
    this.statusSubscription = this.control.statusChanges.subscribe(() => {
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.statusSubscription?.unsubscribe();
  }

  updateError(err: boolean): void {
    this.showError$$.next(err);
  }
}
