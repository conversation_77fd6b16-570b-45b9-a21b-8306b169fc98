<mat-card class="card">
  <mat-card-header class="card-header">
    <glxy-avatar
      *ngIf="cardData.hideAvatar === false"
      mat-card-avatar
      width="36"
      class="user-avatar"
      [src]="cardData.avatar"
      [icon]="cardData.type === 'user' ? 'person' : 'business'"
    />
    <!-- User -->
    <mat-card-title *ngIf="cardData.type === 'user' && cardData.userName" class="name">
      <a [routerLink]="cardData.profileButtonUrl">
        {{ cardData.userName }}
      </a>
    </mat-card-title>

    <mat-card-subtitle *ngIf="cardData.type === 'user' && cardData.email" class="email">
      <div class="card-contents">
        <a href="mailto:{{ cardData.email }}" target="_blank" rel="noopener" (click)="openMailWindow()">
          {{ cardData.email }}
        </a>
        <button mat-icon-button (click)="copyToClipboard(cardData.email)" class="custom-icon-button">
          <mat-icon class="copy-icon" inline>content_copy</mat-icon>
        </button>
      </div>
    </mat-card-subtitle>

    @if (cardData.type === 'user' && cardData.phone) {
      <mat-card-subtitle class="phone">
        <a href="tel:{{ cardData.phone }}">
          {{ cardData.phone }}
        </a>
      </mat-card-subtitle>
    }

    <!-- Business -->
    <mat-card-title *ngIf="cardData.type === 'business' && cardData.businessName" class="name">
      <a [routerLink]="cardData.profileButtonUrl">
        {{ cardData.businessName }}
      </a>
    </mat-card-title>

    <mat-card-subtitle *ngIf="cardData.type === 'business' && cardData.businessWebsite" class="website">
      <a class="link" [href]="cardData.businessWebsite" target="_blank" rel="noopener">
        {{ cardData.businessWebsite }}
      </a>
    </mat-card-subtitle>

    <!-- Account -->
    <mat-card-title *ngIf="cardData.type === 'account' && cardData.accountGroupCompanyId" class="account-id">
      @if (cardData.profileButtonUrl) {
        <a [routerLink]="cardData.profileButtonUrl">
          {{ cardData.accountGroupCompanyId }}
        </a>
      } @else {
        {{ cardData.accountGroupCompanyId }}
      }
    </mat-card-title>

    <mat-card-subtitle *ngIf="cardData.type === 'account' && cardData.accountGroupCompanyName" class="account-name">
      <p>{{ cardData.accountGroupCompanyName ?? '' }}</p>
    </mat-card-subtitle>

    <button *ngIf="showActionsMenu" class="kebab-menu" mat-icon-button matTooltip="Actions" [matMenuTriggerFor]="menu">
      <mat-icon class="menu-icon">more_vert</mat-icon>
    </button>
    <mat-menu [xPosition]="'before'" #menu="matMenu">
      <button *ngIf="showEditTagButton" mat-menu-item (click)="onEditTags()">
        {{ 'ASSOCIATIONS.ACTIONS.EDIT_TAGS' | translate }}
      </button>
      <button *ngIf="showRemoveButton" mat-menu-item (click)="onRemove()">
        {{ 'ASSOCIATIONS.ACTIONS.REMOVE' | translate }}
      </button>
      <ng-container *ngFor="let action of actions">
        <button mat-menu-item (click)="action.callback(cardData)">
          <span class="action-button">
            {{ action.label | translate }}
            @if (action.icon) {
              <mat-icon>{{ action.icon }}</mat-icon>
            }
          </span>
        </button>
      </ng-container>
    </mat-menu>
  </mat-card-header>
  <mat-card-content class="content">
    <crm-common-association-card-content
      *ngIf="cardData?.type === 'business' && fullBusinessAddress"
      [iconName]="'location_on'"
    >
      <div class="full-business-address">{{ fullBusinessAddress }}</div>
    </crm-common-association-card-content>
    <ng-content></ng-content>
  </mat-card-content>
</mat-card>
