import { <PERSON>ac<PERSON><PERSON><PERSON><PERSON> as <PERSON>rowser } from 'ionic-appauth/lib/capacitor';
import { <PERSON><PERSON><PERSON> as CapBrowser, OpenOptions } from '@capacitor/browser';

// This is copied and modified from https://github.com/wi3land/ionic-appauth/blob/master/src/capacitor/capacitor-browser.ts
export class VCapacitorBrowser extends Browser {
  public async showWindow(url: string): Promise<string | undefined> {
    if (!CapBrowser) throw new Error('Capacitor Browser Is Undefined!');

    CapBrowser.addListener('browserFinished', () => {
      this.onCloseFunction();
    });

    const options: OpenOptions = {
      url: url,
      presentationStyle: 'popover',
      windowName: '_self',
    };

    await CapBrowser.open(options);

    return 'opened';
  }
}
