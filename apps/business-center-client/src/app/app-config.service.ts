import { Injectable } from '@angular/core';
import { BrandingV2Service, WhitelabelService } from '@galaxy/partner';
import { Observable, of } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { partnerId as appPartnerId } from '../globals';
import { AccountGroup } from './account-group/account-group';
import { Location, LocationsService, isAccountGroup } from './locations';

@Injectable({ providedIn: 'root' })
export class AppConfigService {
  readonly branding$ = this.locationService.marketId$.pipe(
    filter(() => !!appPartnerId),
    switchMap((marketId) => this.brandingService.getBranding(appPartnerId, marketId)),
    catchError(() => of(null)),
    shareReplay(1),
  );

  readonly foundationalProductsWhitelabelledNames$ = this.locationService.marketId$.pipe(
    filter(() => !!appPartnerId),
    switchMap((marketId) => this.whitelabelService.getBranding(appPartnerId, marketId)),
    map((branding) =>
      Object.keys(branding.apps).reduce((acc, key) => ({ ...acc, [key]: branding.apps[key].name }), {}),
    ),
    shareReplay(1),
  );

  readonly businessAppName$ = this.foundationalProductsWhitelabelledNames$.pipe(
    map((apps) => apps['VBC']),
    shareReplay(1),
  );

  readonly legacyWhitelabelConfig$ = this.locationService.marketId$.pipe(
    filter(() => !!appPartnerId),
    switchMap((marketId) => this.whitelabelService.getConfiguration(appPartnerId, marketId)),
    catchError(() => of(null)),
    shareReplay(1),
  );

  readonly legacyConfig$ = this.legacyWhitelabelConfig$.pipe(
    map((config) => config?.businessCenterConfiguration || {}),
    shareReplay(1),
  );

  constructor(
    private readonly whitelabelService: WhitelabelService,
    private readonly locationService: LocationsService,
    private readonly brandingService: BrandingV2Service,
  ) {}

  getMarketLogo(loc: Observable<Location>): Observable<string> {
    return loc.pipe(
      filter(isAccountGroup),
      switchMap((location) => this.getMarketLogoForAccountGroup(location)),
    );
  }

  getMarketLogoByMarket(partnerId: string, marketId: string): Observable<string> {
    return this.brandingService.getBranding(partnerId, marketId).pipe(map((branding) => branding.logoUrl));
  }

  private getMarketLogoForAccountGroup(accountGroup: AccountGroup): Observable<string> {
    const partnerId = accountGroup.partnerId;
    const marketId = accountGroup.marketId;
    return this.getMarketLogoByMarket(partnerId, marketId);
  }
}
