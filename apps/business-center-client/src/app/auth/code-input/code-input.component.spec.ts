import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CodeInputComponent } from './code-input.component';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

describe('CodeInputComponent', () => {
  let component: CodeInputComponent;
  let fixture: ComponentFixture<CodeInputComponent>;
  let inputElements: DebugElement[];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CodeInputComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(CodeInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    inputElements = fixture.debugElement.queryAll(By.css('input'));
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should create the correct number of input fields based on totalDigits', () => {
    expect(inputElements.length).toBe(6);

    component.digits = new Array(4).fill('');
    fixture.detectChanges();

    inputElements = fixture.debugElement.queryAll(By.css('input'));
    expect(inputElements.length).toBe(4);
  });

  it('should emit codeComplete event when all digits are filled out', () => {
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');

    const testCode = ['1', '2', '3', '4', '5', '6'];

    inputElements.forEach((inputEl, index) => {
      const input = inputEl.nativeElement;
      input.value = testCode[index];

      const inputEvent = new Event('input');
      input.dispatchEvent(inputEvent);
    });

    expect(codeCompleteSpy).toHaveBeenCalledWith('123456');
  });

  it('should not emit codeComplete event if not all digits are filled out', () => {
    const codeCompleteSpy = jest.spyOn(component.codeComplete, 'emit');

    const testCode = ['1', '2', '3', '', '5', '6'];

    inputElements.forEach((inputEl, index) => {
      const input = inputEl.nativeElement;
      input.value = testCode[index];

      const inputEvent = new Event('input');
      input.dispatchEvent(inputEvent);
    });

    expect(codeCompleteSpy).not.toHaveBeenCalled();
  });

  it('should focus on the next input element after entering a digit', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    const firstInput = inputElements[0].nativeElement;
    firstInput.value = '1';

    const inputEvent = new Event('input');
    firstInput.dispatchEvent(inputEvent);

    expect(mockFocus).toHaveBeenCalledTimes(1);
  });

  it('should not focus on next element if current input is empty', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    const firstInput = inputElements[0].nativeElement;
    firstInput.value = '';

    const inputEvent = new Event('input');
    firstInput.dispatchEvent(inputEvent);

    expect(mockFocus).not.toHaveBeenCalled();
  });

  it('should focus on previous element when delete key is pressed and current field is empty', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    component.digits[1] = '';

    const backspaceEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
    });

    const secondInput = inputElements[1].nativeElement;
    secondInput.dispatchEvent(backspaceEvent);

    expect(mockFocus).toHaveBeenCalledTimes(1);
  });

  it('should not focus on previous element when delete key is pressed but current field has a value', () => {
    const mockFocus = jest.fn();

    inputElements.forEach((el) => {
      el.nativeElement.focus = mockFocus;
    });

    component.digits[1] = '2';

    const backspaceEvent = new KeyboardEvent('keydown', {
      key: 'Backspace',
    });

    const secondInput = inputElements[1].nativeElement;
    secondInput.dispatchEvent(backspaceEvent);

    expect(mockFocus).not.toHaveBeenCalled();
  });

  it('should be disabled when disabled input is true', () => {
    const inputElements = fixture.debugElement.queryAll(By.css('input'));

    inputElements.forEach((inputEl) => {
      inputEl.nativeElement.disabled = true;
    });
    fixture.detectChanges();

    inputElements.forEach((inputEl) => {
      expect(inputEl.nativeElement.disabled).toBeTruthy();
    });
  });
});
