import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'meeting-scheduler-disconnect-meeting-integration-confirmation-dialog',
  templateUrl: './disconnect-meeting-integration-confirmation-dialog.component.html',
  styleUrl: './disconnect-meeting-integration-confirmation-dialog.component.scss',
  standalone: false,
})
export class DisconnectMeetingIntegrationConfirmationDialogComponent {
  titleKey = '';
  disconnectLabel = '';
  working = false;

  constructor(
    public dialogRef: MatDialogRef<DisconnectMeetingIntegrationConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.setTranslationKeys(data);
  }
  setTranslationKeys(data: any): void {
    this.titleKey = data?.titleKey ?? '';
    this.disconnectLabel = data?.disconnectLabel ?? '';
  }

  onCancelDisconnect(): void {
    this.dialogRef.close(false);
  }

  onConfirmDisconnect(): void {
    this.dialogRef.close(true);
  }
}
