<div class="phone-number-multi">
  <div class="placeholder">
    {{ placeholder }}
    <span *ngIf="required">*</span>
  </div>
  <ng-container *ngFor="let number of phoneNumbers; let i = index">
    <div class="phone-number-row">
      <mat-form-field class="phone-number">
        <input
          matInput
          [value]="number.number"
          [disabled]="disabled"
          placeholder="Phone number"
          (change)="updateNumber(i, $event.target.value)"
          (blur)="handleBlur()"
        />
      </mat-form-field>
      <mat-form-field class="extension">
        <input
          matInput
          [value]="number.extension || ''"
          [disabled]="disabled"
          placeholder="Ext"
          (change)="updateExtension(i, $event.target.value)"
          (blur)="handleBlur()"
        />
      </mat-form-field>
      <mat-icon class="delete-row" *ngIf="!required || phoneNumbers.length > 1" (click)="deleteNumber(i)">
        close
      </mat-icon>
    </div>
  </ng-container>
  <div class="add-new-row">
    <span *ngIf="phoneNumbers.length < maxMultiples" class="add-btn" (click)="addNumber()">
      <mat-icon>add</mat-icon>
      Add Phone Number
    </span>
  </div>
</div>
<mat-error *ngIf="formControl.errors">{{ errorOutput(formControl) }}</mat-error>
