import { Component, input, computed, signal, effect } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CdkAccordionModule } from '@angular/cdk/accordion';
import { MatSliderModule } from '@angular/material/slider';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { CommonModule } from '@angular/common';
import { AiModelConfigurationForm, DEFAULT_MODEL } from '../../core/forms/ai-model-config.form';
import { Model } from '@vendasta/ai-assistants';
import { GalaxyFormFieldModule } from '@vendasta/galaxy/form-field';
import { AiAssistantI18nModule } from '../../assets/i18n/ai-assistant-i18n.module';
import { GalaxyBadgeModule } from '@vendasta/galaxy/badge';

@Component({
  selector: 'ai-model-configuration-form',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatSliderModule,
    CdkAccordionModule,
    ReactiveFormsModule,
    GalaxyFormFieldModule,
    AiAssistantI18nModule,
    GalaxyBadgeModule,
  ],
  templateUrl: './ai-model-configuration-form.component.html',
  styleUrl: './ai-model-configuration-form.component.scss',
})
export class AiModelConfigurationFormComponent {
  readonly aiModelConfigForm = input.required<AiModelConfigurationForm>();
  readonly availableModels = input.required<Model[]>();

  // Signal to track the current model value
  private readonly currentModelValue = signal<Model | null>(null);

  readonly aiModel = computed(() => {
    const modelValue = this.currentModelValue();
    return this.getCompleteModel(modelValue ?? this.defaultModel);
  });

  readonly defaultModel = DEFAULT_MODEL;

  constructor() {
    // Effect to track form changes and update the signal
    effect(() => {
      const form = this.aiModelConfigForm();

      // Set initial value
      this.currentModelValue.set(form.controls.model.value);

      // Subscribe to value changes
      const subscription = form.controls.model.valueChanges.subscribe((value) => {
        this.currentModelValue.set(value);
      });

      // Cleanup subscription when form changes
      return () => subscription.unsubscribe();
    });
  }

  /**
   * Compare function for mat-select to properly match models
   * Compares models by vendor and id for proper selection
   */
  compareModels = (model1: Model | null, model2: Model | null): boolean => {
    if (model1 === model2) return true;
    if (!model1 || !model2) return false;
    return model1.vendor === model2.vendor && model1.id === model2.id;
  };

  /**
   * Get the recommended model from the available models
   * @returns The first model marked as recommended, or null if none found
   */
  getRecommendedModel(): Model | null {
    return this.availableModels().find((model) => model.recommended) || null;
  }

  getCompleteModel(model: Model): Model {
    for (const availableModel of this.availableModels()) {
      if (availableModel.id === model?.id && availableModel.vendor === model?.vendor) {
        return availableModel;
      }
    }
    return this.defaultModel;
  }
}
