import { FormControl } from '@angular/forms';
import { PhoneNumber, PhoneNumberMultiComponent } from './phone-number-multi.component';

class FormControlMock extends FormControl {
  constructor(value) {
    super(value);
    this.setErrors = jest.fn();
  }
}

describe('PhoneNumberMultiComponent', () => {
  let testPhoneNumberMultiComponent: PhoneNumberMultiComponent;
  const sampleNumbers: PhoneNumber[] = [
    { number: '5194950020' },
    { number: '+44 20 7234 3456', extension: '123' },
    { number: '(*************', extension: '0' },
  ];
  beforeEach(() => {
    testPhoneNumberMultiComponent = new PhoneNumberMultiComponent(null, null);
    testPhoneNumberMultiComponent.formControl = new FormControlMock([]);
    testPhoneNumberMultiComponent.maxMultiples = 4;
    testPhoneNumberMultiComponent.ngOnInit();
  });
  describe('parseNumbers', () => {
    it('should return an array of correctly parsed PhoneNumber objects when passed in an array of number strings', () => {
      const numberStrings: string[] = ['5194950020', '+44 20 7234 3456,123', '(*************,', '123 4567,0'];
      const parsedNumbers: PhoneNumber[] = testPhoneNumberMultiComponent.parseNumbers(numberStrings);
      expect(parsedNumbers[0].number).toEqual('5194950020');
      expect(parsedNumbers[0].extension).toBeUndefined();
      expect(parsedNumbers[1].number).toEqual('+44 20 7234 3456');
      expect(parsedNumbers[1].extension).toEqual('123');
      expect(parsedNumbers[2].number).toEqual('(*************');
      expect(parsedNumbers[2].extension).toBeUndefined();
      expect(parsedNumbers[3].number).toEqual('123 4567');
      expect(parsedNumbers[3].extension).toEqual('0');
    });
    it('should return an empty array if passed an empty array', () => {
      const parsedNumbers = testPhoneNumberMultiComponent.parseNumbers([]);
      expect(Array.isArray(parsedNumbers));
      expect(parsedNumbers.length).toEqual(0);
    });
  });
  describe('serializeNumbers', () => {
    it('should return an array of correctly serialized phone numbers when passed an array of PhoneNumber objects', () => {
      const serializedNumbers = testPhoneNumberMultiComponent.serializeNumbers(Array.from(sampleNumbers));
      expect(serializedNumbers[0]).toEqual('5194950020');
      expect(serializedNumbers[1]).toEqual('+44 20 7234 3456,123');
      expect(serializedNumbers[2]).toEqual('(*************,0');
    });
    it('should return an empty array if passed an empty array', () => {
      const serializedNumbers = testPhoneNumberMultiComponent.serializeNumbers([]);
      expect(Array.isArray(serializedNumbers));
      expect(serializedNumbers.length).toEqual(0);
    });
  });
  describe('updateNumber', () => {
    beforeEach(() => {
      testPhoneNumberMultiComponent.phoneNumbers = Array.from(sampleNumbers);
    });
    it('should update the correct number in phoneNumbers$$', () => {
      testPhoneNumberMultiComponent.updateNumber(1, '123 4567');
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0].number).toEqual(sampleNumbers[0].number);
      expect(phoneNumbersValue[1].number).toEqual('123 4567');
      expect(phoneNumbersValue[2].number).toEqual(sampleNumbers[2].number);
      expect(phoneNumbersValue[1].extension).toEqual(sampleNumbers[1].extension);
    });
    it('should not change the value of phoneNumbers$$ when called with an invalid index', () => {
      testPhoneNumberMultiComponent.updateNumber(5, '123 4567');
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0]).toEqual(sampleNumbers[0]);
      expect(phoneNumbersValue[1]).toEqual(sampleNumbers[1]);
      expect(phoneNumbersValue[2]).toEqual(sampleNumbers[2]);
    });
  });
  describe('updateExtension', () => {
    beforeEach(() => {
      testPhoneNumberMultiComponent.phoneNumbers = Array.from(sampleNumbers);
    });
    it('should update the correct extension in phoneNumbers$$', () => {
      testPhoneNumberMultiComponent.updateExtension(1, '369');
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0].extension).toBeUndefined();
      expect(phoneNumbersValue[1].extension).toEqual('369');
      expect(phoneNumbersValue[2].extension).toEqual(sampleNumbers[2].extension);
      expect(phoneNumbersValue[1].number).toEqual(sampleNumbers[1].number);
    });
    it('should remove the correct extension in phoneNumber$$ when called with an empty value', () => {
      testPhoneNumberMultiComponent.updateExtension(1, '');
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0].extension).toBeUndefined();
      expect(phoneNumbersValue[1].extension).toBeUndefined();
      expect(phoneNumbersValue[2].extension).toEqual(sampleNumbers[2].extension);
      expect(phoneNumbersValue[1].number).toEqual(sampleNumbers[1].number);
    });
    it('should not change the value of phoneNumbers$$ when called with an invalid index', () => {
      testPhoneNumberMultiComponent.updateExtension(5, '555');
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0]).toEqual(sampleNumbers[0]);
      expect(phoneNumbersValue[1]).toEqual(sampleNumbers[1]);
      expect(phoneNumbersValue[2]).toEqual(sampleNumbers[2]);
    });
  });
  describe('addNumber', () => {
    it('should add an empty number to phoneNumbers$$ when current item count is less than maxMultiples', () => {
      testPhoneNumberMultiComponent.phoneNumbers = Array.from(sampleNumbers);
      testPhoneNumberMultiComponent.addNumber();
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[3].number).toEqual('');
      expect(phoneNumbersValue[3].extension).toBeUndefined();
      expect(phoneNumbersValue.length).toEqual(4);
    });
    it('should not add a number to phoneNumbers$$ when current item count is equal to maxMultiples', () => {
      testPhoneNumberMultiComponent.phoneNumbers = Array.from(sampleNumbers).concat([
        { number: '123 4567', extension: '0' },
      ]);
      testPhoneNumberMultiComponent.addNumber();
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue.length).toEqual(4);
    });
  });
  describe('deleteNumber', () => {
    it('should delete the specified number from phoneNumbers$$ when current item count is more than 1', () => {
      testPhoneNumberMultiComponent.phoneNumbers = Array.from(sampleNumbers);
      testPhoneNumberMultiComponent.deleteNumber(1);
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0]).toEqual(sampleNumbers[0]);
      expect(phoneNumbersValue[1]).toEqual(sampleNumbers[2]);
      expect(phoneNumbersValue[2]).toBeUndefined();
      expect(phoneNumbersValue.length).toEqual(sampleNumbers.length - 1);
    });
    it('should not delete any numbers from phoneNumber$$ when current item count is less than 2', () => {
      testPhoneNumberMultiComponent.phoneNumbers = [sampleNumbers[0]];
      testPhoneNumberMultiComponent.deleteNumber(1);
      const phoneNumbersValue = testPhoneNumberMultiComponent.phoneNumbers;
      expect(phoneNumbersValue[0]).toEqual(sampleNumbers[0]);
      expect(phoneNumbersValue.length).toEqual(1);
    });
  });
  describe('updateErrors', () => {
    it('should set a required error on the FormControl when passed an empty phone number and required is true', () => {
      testPhoneNumberMultiComponent.required = true;
      testPhoneNumberMultiComponent.updateErrors([{ number: '' }]);
      expect(testPhoneNumberMultiComponent.formControl.setErrors).toBeCalledWith({ required: true });
    });
    it('should set a required error on the FormControl when passed an empty list and required is true', () => {
      testPhoneNumberMultiComponent.required = true;
      testPhoneNumberMultiComponent.updateErrors([]);
      expect(testPhoneNumberMultiComponent.formControl.setErrors).toBeCalledWith({ required: true });
    });
    it('should not set a required error on the FormControl when passed an empty phone number and required is false', () => {
      testPhoneNumberMultiComponent.required = false;
      testPhoneNumberMultiComponent.updateErrors([{ number: '' }]);
      expect(testPhoneNumberMultiComponent.formControl.setErrors).toBeCalledWith(null);
    });
    it('should set a phone error on the FormControl when passed an invalid phone number', () => {
      testPhoneNumberMultiComponent.required = true;
      testPhoneNumberMultiComponent.updateErrors([{ number: 'INVALID PHONE NUMBER' }, sampleNumbers[1]]);
      expect(testPhoneNumberMultiComponent.formControl.setErrors).toBeCalledWith({ phone: true });
    });
    it('should set a phone error on the FormControl when passed an invalid extension', () => {
      testPhoneNumberMultiComponent.required = true;
      testPhoneNumberMultiComponent.updateErrors([
        { number: '4032651123', extension: 'INVALID EXTENSION' },
        sampleNumbers[1],
      ]);
      expect(testPhoneNumberMultiComponent.formControl.setErrors).toBeCalledWith({ phone: true });
    });
  });
});
