import { AiConnectionsForm } from './ai-connections.form';
import { AiCapabilityFormArray } from './ai-capability.form';
import { AiKnowledgeFormArray } from './ai-knowledge.form';
import { FormControl, FormGroup } from '@angular/forms';
import { AssistantInterface, ConnectionInterface, NamespaceInterface, AssistantType } from '@vendasta/ai-assistants';
import { AiAssistant } from '../interfaces/ai-assistant.interface';
import { KnowledgeSource } from '@vendasta/embeddings';
import { AiVoiceConfigurationForm } from './ai-voice-model-config.form';
import { AiToolFormRegistryService } from '../services/ai-tool-form-registry.service';
import { AiModelConfigurationForm } from './ai-model-config.form';

// Assistant
export type AiAssistantFormControls = {
  id: FormControl<string | null>;
  namespace: FormControl<NamespaceInterface | null>;
  name: FormControl<string | null>;
  avatarUrl: FormControl<string | null>;
  capabilities: AiCapabilityFormArray;
  connections: AiConnectionsForm;
  knowledge: AiKnowledgeFormArray;
  voiceConfig: AiVoiceConfigurationForm;
  type: FormControl<AssistantType | null>;
  model: AiModelConfigurationForm;
};

export class AiAssistantForm extends FormGroup<AiAssistantFormControls> {
  constructor(
    assistant?: AiAssistant,
    promptModuleContents?: Record<string, string>,
    knowledgeSources?: KnowledgeSource[],
    toolFormRegistry?: AiToolFormRegistryService,
  ) {
    const type = assistant?.assistant.type ?? AssistantType.ASSISTANT_TYPE_CUSTOM;
    const goals = assistant?.assistant.configurableGoals?.filter((goal) => goal !== undefined) || [];
    const model = assistant?.assistant?.config?.models?.[0];

    super({
      id: new FormControl({ value: assistant?.assistant.id ?? null, disabled: true }),
      type: new FormControl({ value: type, disabled: true }),
      namespace: new FormControl(assistant?.assistant.namespace ?? null),
      name: new FormControl(assistant?.assistant.name || ''),
      avatarUrl: new FormControl(assistant?.assistant.avatarUrl || ''),
      capabilities: new AiCapabilityFormArray(goals, promptModuleContents || {}, toolFormRegistry),
      connections: new AiConnectionsForm(assistant?.connections || [], assistant?.assistant.id || ''),
      knowledge: new AiKnowledgeFormArray(knowledgeSources ?? []),
      voiceConfig: new AiVoiceConfigurationForm(assistant?.assistant?.config?.voiceConfig),
      model: new AiModelConfigurationForm(model),
    });
  }

  toAssistant(fallbackNamespace: NamespaceInterface | undefined): AssistantInterface {
    const isVoiceAssistant = this.controls.type.getRawValue() === AssistantType.ASSISTANT_TYPE_VOICE_RECEPTIONIST;
    const model = this.controls.model.toAssistantModel();

    return {
      id: this.controls.id.getRawValue() ?? undefined,
      type: this.controls.type.getRawValue() ?? AssistantType.ASSISTANT_TYPE_CUSTOM,
      namespace: this.controls.namespace.getRawValue() ?? fallbackNamespace,
      name: this.controls.name.getRawValue() ?? undefined,
      avatarUrl: this.controls.avatarUrl.getRawValue() ?? undefined,
      configurableGoals: this.controls.capabilities.toConfigurableGoals(fallbackNamespace),
      config: isVoiceAssistant
        ? {
            voiceConfig: this.controls.voiceConfig.toAssistantVoiceConfig(),
          }
        : {
            models: model ? [model] : [],
          },
    };
  }

  toConnections(): ConnectionInterface[] {
    return this.controls.connections.toConnections();
  }

  updatedPromptModules(): Array<{ id: string | undefined; content: string | undefined }> {
    return this.controls.capabilities.updatedPromptModules();
  }
}
