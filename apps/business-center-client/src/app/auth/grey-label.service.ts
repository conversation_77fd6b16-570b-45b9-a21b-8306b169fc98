import { Injectable, inject, signal } from '@angular/core';
import { UserService } from '@vendasta/business-center';
import { NamespaceDetails } from '@vendasta/business-center/lib/_internal';
import { firstValueFrom } from 'rxjs';
import { MOBILE_PARTNER_ID } from '../constants';
import { isGreyLabel } from '../../globals';

@Injectable({
  providedIn: 'root',
})
export class GreyLabelService {
  private readonly userService = inject(UserService);

  private readonly _userEmail = signal<string>('');
  readonly userEmail = this._userEmail.asReadonly();

  private readonly _namespaceDetails = signal<NamespaceDetails[]>([]);
  readonly namespaceDetails = this._namespaceDetails.asReadonly();
  readonly isGreyLabel = isGreyLabel;

  async sendOTP(email: string) {
    if (!email) {
      throw new Error('Please enter your email address.');
    }

    this._userEmail.set(email);
    this._namespaceDetails.set([]);
    await firstValueFrom(this.userService.requestShortOtpCode({ email }));
  }

  async resendOTP() {
    this._namespaceDetails.set([]);
    await this.sendOTP(this.userEmail());
  }

  async loadNamespacesForOTP(oneTimePassword: string) {
    if (!oneTimePassword || oneTimePassword.length !== 6) {
      throw new Error('Please enter a valid 6-digit code.');
    }

    const { namespaces } = await firstValueFrom(
      this.userService.listNamespaceDetails({
        email: this.userEmail(),
        otpCode: oneTimePassword,
      }),
    );

    if (namespaces.length === 0) {
      throw new Error('No namespaces found for the provided OTP.');
    }

    this._namespaceDetails.set(namespaces);
  }

  navigateToProvider(providerId: string, promptLogin = false): void {
    localStorage.setItem(MOBILE_PARTNER_ID, providerId);
    const encodedEmail = encodeURIComponent(this.userEmail());
    window.location.href = `/landing?email=${encodedEmail}&autoPromptLogin=${promptLogin}`;
  }

  resetProvider(): void {
    localStorage.removeItem(MOBILE_PARTNER_ID);
    window.location.href = '/landing';
  }
}
